import { Suspense } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Truck, 
  Package, 
  Clock, 
  MapPin, 
  Calculator,
  FileText,
  Star,
  Plus,
  TrendingUp
} from 'lucide-react'

// Données de démonstration
const recentShipments = [
  {
    id: 'SH-2024-001',
    description: 'Équipements électroniques',
    weight: 45.5,
    origin: 'Lagos',
    status: 'IN_TRANSIT',
    estimatedDelivery: '2024-01-15',
    cost: 85000
  },
  {
    id: 'SH-2024-002', 
    description: 'Pièces automobiles',
    weight: 120.0,
    origin: 'Kano',
    status: 'DELIVERED',
    estimatedDelivery: '2024-01-10',
    cost: 156000
  },
  {
    id: 'SH-2024-003',
    description: 'Produits pharmaceutiques',
    weight: 25.0,
    origin: 'Abuja',
    status: 'PENDING',
    estimatedDelivery: '2024-01-20',
    cost: 95000
  }
]

const stats = {
  totalShipments: 24,
  totalWeight: 2450,
  totalSpent: 2850000,
  averageRating: 4.8
}

export default function LogisticsClientPage() {
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { label: 'En attente', color: 'bg-yellow-100 text-yellow-800' },
      IN_TRANSIT: { label: 'En transit', color: 'bg-blue-100 text-blue-800' },
      DELIVERED: { label: 'Livré', color: 'bg-green-100 text-green-800' },
      CANCELLED: { label: 'Annulé', color: 'bg-red-100 text-red-800' }
    }
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING
    return <Badge className={config.color}>{config.label}</Badge>
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0
    }).format(amount)
  }

  return (
    <div className="p-6 space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Espace Client Logistique
          </h1>
          <p className="text-gray-600">
            Gérez vos expéditions et suivez vos transports en temps réel
          </p>
        </div>
        <Button className="flex items-center">
          <Plus className="h-4 w-4 mr-2" />
          Nouvelle demande
        </Button>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total expéditions</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalShipments}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Poids total</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalWeight.toLocaleString()} kg</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <FileText className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total dépensé</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(stats.totalSpent)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Note moyenne</p>
                <p className="text-2xl font-bold text-gray-900">{stats.averageRating}/5</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Contenu principal */}
      <Tabs defaultValue="shipments" className="space-y-6">
        <TabsList>
          <TabsTrigger value="shipments">Mes expéditions</TabsTrigger>
          <TabsTrigger value="calculator">Calculateur</TabsTrigger>
          <TabsTrigger value="invoices">Factures</TabsTrigger>
          <TabsTrigger value="tracking">Suivi</TabsTrigger>
        </TabsList>

        <TabsContent value="shipments">
          <Card>
            <CardHeader>
              <CardTitle>Expéditions récentes</CardTitle>
              <CardDescription>
                Historique de vos dernières demandes de transport
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentShipments.map((shipment) => (
                  <div key={shipment.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="font-medium text-gray-900">{shipment.id}</h3>
                          {getStatusBadge(shipment.status)}
                        </div>
                        <p className="text-gray-600 mb-2">{shipment.description}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-1" />
                            {shipment.origin} → Dakar
                          </div>
                          <div className="flex items-center">
                            <Package className="h-4 w-4 mr-1" />
                            {shipment.weight} kg
                          </div>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {new Date(shipment.estimatedDelivery).toLocaleDateString('fr-FR')}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-lg text-gray-900">
                          {formatCurrency(shipment.cost)}
                        </div>
                        <Button variant="outline" size="sm" className="mt-2">
                          Détails
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calculator">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calculator className="h-5 w-5 mr-2" />
                Calculateur de tarifs
              </CardTitle>
              <CardDescription>
                Obtenez un devis instantané pour vos expéditions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Formulaire de calcul */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Route
                    </label>
                    <select className="w-full p-2 border border-gray-300 rounded-md" aria-label="Sélectionner une route">
                      <option>Lagos → Dakar</option>
                      <option>Abuja → Dakar</option>
                      <option>Kano → Dakar</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Poids (kg)
                    </label>
                    <input
                      type="number"
                      placeholder="Ex: 100"
                      className="w-full p-2 border border-gray-300 rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Mode de transport
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      <button type="button" className="p-3 border border-blue-500 bg-blue-50 rounded-md text-sm">
                        🚛 Routier
                      </button>
                      <button type="button" className="p-3 border border-gray-300 rounded-md text-sm">
                        ✈️ Aérien
                      </button>
                    </div>
                  </div>
                  <Button className="w-full">
                    Calculer le tarif
                  </Button>
                </div>

                {/* Résultat */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                  <h3 className="font-bold text-lg text-green-800 mb-4">Estimation</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Transport de base:</span>
                      <span className="font-medium">85,000 CFA</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Frais de manutention:</span>
                      <span className="font-medium">4,250 CFA</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Surcharge carburant:</span>
                      <span className="font-medium">10,200 CFA</span>
                    </div>
                    <hr className="my-2" />
                    <div className="flex justify-between font-bold text-lg">
                      <span>Total:</span>
                      <span className="text-green-700">99,450 CFA</span>
                    </div>
                    <p className="text-sm text-green-600 mt-2">
                      Délai estimé: 6 jours
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="invoices">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Factures et paiements
              </CardTitle>
              <CardDescription>
                Consultez vos factures et l'historique des paiements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <FileText className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                <p className="text-gray-500 mb-4">
                  Aucune facture disponible pour le moment
                </p>
                <Button variant="outline">
                  Voir toutes les factures
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tracking">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Truck className="h-5 w-5 mr-2" />
                Suivi en temps réel
              </CardTitle>
              <CardDescription>
                Suivez vos expéditions en cours en temps réel
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Truck className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                <p className="text-gray-500 mb-4">
                  Aucune expédition en cours de suivi
                </p>
                <Button variant="outline">
                  Rechercher par numéro
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Actions rapides */}
      <Card>
        <CardHeader>
          <CardTitle>Actions rapides</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-20 flex-col">
              <Plus className="h-6 w-6 mb-2" />
              Nouvelle demande
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Calculator className="h-6 w-6 mb-2" />
              Calculer un tarif
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Truck className="h-6 w-6 mb-2" />
              Suivre une expédition
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export const metadata = {
  title: 'Espace Client Logistique - Bi-Express',
  description: 'Dashboard client pour la gestion des expéditions et transports'
}
