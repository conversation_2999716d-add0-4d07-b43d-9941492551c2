import { ClientTypeSelector } from '@/components/client/ClientTypeSelector'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge-component'
import { 
  MapPin, 
  Clock, 
  Shield, 
  Star,
  Truck,
  Package,
  Users,
  TrendingUp
} from 'lucide-react'

// Statistiques de l'entreprise
const companyStats = {
  totalShipments: 1247,
  satisfactionRate: 4.8,
  averageDeliveryTime: 6.2,
  clientsServed: 156
}

const testimonials = [
  {
    name: '<PERSON>ina<PERSON>',
    business: 'Boutique Mode Dakar',
    type: 'Commerce',
    rating: 5,
    comment: 'Service excellent ! Je reçois mes tissus directement à Dakar sans me soucier de rien.',
    avatar: '👩🏾‍💼'
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    business: 'Import-Export MT',
    type: 'Logistique',
    rating: 5,
    comment: 'Tarifs transparents et livraisons toujours dans les délais. Je recommande !',
    avatar: '👨🏾‍💼'
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    business: 'Cosmétiques FS',
    type: 'Commerce',
    rating: 4,
    comment: 'Très pratique pour quelqu\'un qui débute dans l\'import. Service clé en main parfait.',
    avatar: '👩🏾'
  }
]

const routes = [
  { from: 'Lagos', to: 'Dakar', duration: '5-7 jours', mode: 'Routier' },
  { from: 'Abuja', to: 'Dakar', duration: '6-8 jours', mode: 'Routier' },
  { from: 'Kano', to: 'Dakar', duration: '4-6 jours', mode: 'Routier' },
  { from: 'Lagos', to: 'Dakar', duration: '24-48h', mode: 'Aérien' }
]

export default function ClientPortalPage() {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ))
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      {/* En-tête hero */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Bi-Express Client Portal
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Votre partenaire de confiance pour l'import Nigeria ↔ Dakar
            </p>
            
            {/* Statistiques rapides */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">{companyStats.totalShipments}</div>
                <div className="text-sm text-gray-600">Expéditions réalisées</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">{companyStats.satisfactionRate}/5</div>
                <div className="text-sm text-gray-600">Satisfaction client</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">{companyStats.averageDeliveryTime}j</div>
                <div className="text-sm text-gray-600">Délai moyen</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600">{companyStats.clientsServed}</div>
                <div className="text-sm text-gray-600">Clients actifs</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Sélecteur de type de client */}
        <div className="mb-12">
          <ClientTypeSelector />
        </div>

        {/* Informations sur les routes */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MapPin className="h-5 w-5 mr-2 text-blue-600" />
                Routes disponibles
              </CardTitle>
              <CardDescription>
                Nos liaisons régulières Nigeria - Dakar
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {routes.map((route, index) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center">
                        {route.mode === 'Routier' ? (
                          <Truck className="h-4 w-4 text-blue-600" />
                        ) : (
                          <Package className="h-4 w-4 text-purple-600" />
                        )}
                        <span className="ml-2 font-medium text-sm">
                          {route.from} → {route.to}
                        </span>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {route.mode}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600 flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      {route.duration}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2 text-green-600" />
                Nos garanties
              </CardTitle>
              <CardDescription>
                Ce qui fait notre différence
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="p-1 bg-green-100 rounded">
                    <Shield className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">Assurance incluse</div>
                    <div className="text-xs text-gray-600">
                      Couverture complète de vos marchandises
                    </div>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="p-1 bg-blue-100 rounded">
                    <Clock className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">Délais respectés</div>
                    <div className="text-xs text-gray-600">
                      95% de nos livraisons dans les délais
                    </div>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="p-1 bg-purple-100 rounded">
                    <Users className="h-4 w-4 text-purple-600" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">Support 24/7</div>
                    <div className="text-xs text-gray-600">
                      Équipe dédiée à votre service
                    </div>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="p-1 bg-orange-100 rounded">
                    <TrendingUp className="h-4 w-4 text-orange-600" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">Tarifs compétitifs</div>
                    <div className="text-xs text-gray-600">
                      Meilleur rapport qualité-prix du marché
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Témoignages clients */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Star className="h-5 w-5 mr-2 text-yellow-600" />
              Témoignages clients
            </CardTitle>
            <CardDescription>
              Ce que disent nos clients satisfaits
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {testimonials.map((testimonial, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="text-2xl">{testimonial.avatar}</div>
                    <div>
                      <div className="font-medium text-sm">{testimonial.name}</div>
                      <div className="text-xs text-gray-600">{testimonial.business}</div>
                      <div className="flex items-center space-x-1">
                        <Badge 
                          variant="secondary" 
                          className={`text-xs ${
                            testimonial.type === 'Commerce' 
                              ? 'bg-purple-100 text-purple-800' 
                              : 'bg-blue-100 text-blue-800'
                          }`}
                        >
                          {testimonial.type}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center mb-2">
                    {renderStars(testimonial.rating)}
                  </div>
                  <p className="text-sm text-gray-700 italic">
                    "{testimonial.comment}"
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Call to action */}
        <div className="text-center mt-12">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white">
            <h2 className="text-2xl font-bold mb-4">
              Prêt à commencer ?
            </h2>
            <p className="text-blue-100 mb-6">
              Choisissez votre type de service ci-dessus et accédez à votre dashboard personnalisé
            </p>
            <div className="flex justify-center space-x-4">
              <div className="text-center">
                <div className="text-3xl font-bold">2min</div>
                <div className="text-sm text-blue-100">Configuration</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">24h</div>
                <div className="text-sm text-blue-100">Premier devis</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">5-7j</div>
                <div className="text-sm text-blue-100">Première livraison</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export const metadata = {
  title: 'Portail Client - Bi-Express',
  description: 'Choisissez votre type de service et accédez à votre dashboard personnalisé'
}
