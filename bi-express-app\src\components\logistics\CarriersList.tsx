import { getCarriers } from '@/lib/actions/carriers'
import { Truck, Star, Clock, Package, DollarSign, Phone, Mail, MapPin } from 'lucide-react'
import { AddCarrierButton } from './AddCarrierButton'

const cityLabels = {
  LAGOS: 'Lagos',
  ABUJA: 'Abuja',
  KANO: 'Kano'
}

export async function CarriersList() {
  const carriers = await getCarriers()

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">
          Transporteurs ({carriers.length})
        </h3>
        <AddCarrierButton />
      </div>

      {carriers.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
          <Truck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun transporteur</h3>
          <p className="text-gray-600 mb-4">
            Ajoutez des transporteurs pour gérer vos expéditions.
          </p>
          <AddCarrierButton />
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {carriers.map((carrier) => (
            <div key={carrier.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              {/* En-tête du transporteur */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Truck className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="text-lg font-medium text-gray-900">{carrier.name}</h4>
                    <p className="text-sm text-gray-600">{cityLabels[carrier.city]}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-sm font-medium text-gray-900">
                    {carrier.stats.averageRating.toFixed(1)}
                  </span>
                </div>
              </div>

              {/* Informations de contact */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Phone className="h-4 w-4" />
                  <span>{carrier.phone}</span>
                </div>
                {carrier.email && (
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Mail className="h-4 w-4" />
                    <span>{carrier.email}</span>
                  </div>
                )}
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <MapPin className="h-4 w-4" />
                  <span>{carrier.address}</span>
                </div>
              </div>

              {/* Modes de transport */}
              <div className="mb-4">
                <p className="text-xs text-gray-500 mb-2">Modes de transport</p>
                <div className="flex flex-wrap gap-2">
                  {carrier.transportModes.split(',').map((mode, index) => (
                    <span
                      key={index}
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        mode === 'ROAD' 
                          ? 'bg-blue-100 text-blue-800' 
                          : 'bg-purple-100 text-purple-800'
                      }`}
                    >
                      {mode === 'ROAD' ? '🚛 Routier' : '✈️ Aérien'}
                    </span>
                  ))}
                </div>
              </div>

              {/* Capacité */}
              {carrier.capacity && (
                <div className="mb-4">
                  <p className="text-xs text-gray-500 mb-1">Capacité</p>
                  <p className="text-sm font-medium text-gray-900">
                    {carrier.capacity.toLocaleString()} kg
                  </p>
                </div>
              )}

              {/* Statistiques de performance */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <Package className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-xs text-gray-500">Expéditions</p>
                      <p className="text-sm font-medium text-gray-900">
                        {carrier.stats.totalShipments}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-xs text-gray-500">Ponctualité</p>
                      <p className="text-sm font-medium text-gray-900">
                        {carrier.stats.onTimeRate}%
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <Package className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-xs text-gray-500">Livrées</p>
                      <p className="text-sm font-medium text-gray-900">
                        {carrier.stats.deliveredShipments}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-xs text-gray-500">Revenus</p>
                      <p className="text-sm font-medium text-gray-900">
                        {carrier.stats.totalCost.toLocaleString()} XOF
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Indicateur de statut */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    carrier.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {carrier.isActive ? 'Actif' : 'Inactif'}
                  </span>
                  
                  <button className="text-sm text-blue-600 hover:text-blue-700">
                    Voir détails
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
