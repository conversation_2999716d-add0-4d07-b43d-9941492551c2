import { Suspense } from 'react'
import { SuppliersList } from '@/components/suppliers/SuppliersList'
import { SupplierFilters } from '@/components/suppliers/SupplierFilters'
import { AddSupplierButton } from '@/components/suppliers/AddSupplierButton'
import { Plus } from 'lucide-react'

export default function SuppliersPage() {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 pb-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Fournisseurs</h1>
          <p className="text-gray-600 mt-1">
            Gérez vos fournisseurs au Nigeria (Lagos, Abuja, Kano)
          </p>
        </div>
        <AddSupplierButton />
      </div>

      {/* Filters */}
      <Suspense fallback={<div className="animate-pulse h-16 bg-gray-200 rounded-lg" />}>
        <SupplierFilters />
      </Suspense>

      {/* Suppliers List */}
      <Suspense fallback={<div className="animate-pulse h-96 bg-gray-200 rounded-lg" />}>
        <SuppliersList />
      </Suspense>
    </div>
  )
}
