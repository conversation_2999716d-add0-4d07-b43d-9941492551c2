import { getShipments } from '@/lib/actions/shipments'
import { getShipmentStatusColor, getShipmentStatusLabel, formatDuration } from '@/lib/logistics'
import { Package, MapPin, Calendar, Truck, User, Building } from 'lucide-react'
import { ShipmentStatusBadge } from './ShipmentStatusBadge'
import { TrackingButton } from './TrackingButton'

const transportModeLabels = {
  ROAD: 'Routier',
  AIR_EXPRESS: 'Aérien Express'
}

const transportModeIcons = {
  ROAD: '🚛',
  AIR_EXPRESS: '✈️'
}

export async function ShipmentsList() {
  const shipments = await getShipments()

  if (shipments.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
        <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune expédition</h3>
        <p className="text-gray-600">
          Les expéditions créées apparaîtront ici.
        </p>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">
          Expéditions ({shipments.length})
        </h3>
      </div>
      
      <div className="divide-y divide-gray-200">
        {shipments.map((shipment) => (
          <div key={shipment.id} className="p-6 hover:bg-gray-50">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                {/* En-tête avec numéro de suivi et statut */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">
                        {transportModeIcons[shipment.transportMode]}
                      </span>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {shipment.trackingNumber}
                        </p>
                        <p className="text-xs text-gray-500">
                          {transportModeLabels[shipment.transportMode]}
                        </p>
                      </div>
                    </div>
                  </div>
                  <ShipmentStatusBadge status={shipment.status} />
                </div>

                {/* Informations de la commande */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-xs text-gray-500">Client</p>
                      <p className="text-sm font-medium text-gray-900">
                        {shipment.order.customer.name}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Building className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-xs text-gray-500">Transporteur</p>
                      <p className="text-sm font-medium text-gray-900">
                        {shipment.carrier.name}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-xs text-gray-500">Trajet</p>
                      <p className="text-sm font-medium text-gray-900">
                        {shipment.originCity} → {shipment.destinationCity}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Informations de livraison */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-xs text-gray-500">Livraison estimée</p>
                      <p className="text-sm font-medium text-gray-900">
                        {new Date(shipment.estimatedDelivery).toLocaleDateString('fr-FR')}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Package className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-xs text-gray-500">Poids</p>
                      <p className="text-sm font-medium text-gray-900">
                        {shipment.weight} kg
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Truck className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-xs text-gray-500">Coût transport</p>
                      <p className="text-sm font-medium text-gray-900">
                        {shipment.transportCost.toLocaleString()} XOF
                      </p>
                    </div>
                  </div>
                </div>

                {/* Localisation actuelle */}
                {shipment.currentLocation && (
                  <div className="mb-4">
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-blue-500" />
                      <div>
                        <p className="text-xs text-gray-500">Localisation actuelle</p>
                        <p className="text-sm font-medium text-blue-600">
                          {shipment.currentLocation}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Dernier événement de suivi */}
                {shipment.trackingEvents.length > 0 && (
                  <div className="mb-4">
                    <div className="bg-gray-50 rounded-lg p-3">
                      <p className="text-xs text-gray-500 mb-1">Dernier événement</p>
                      <p className="text-sm font-medium text-gray-900">
                        {shipment.trackingEvents[0].description}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {new Date(shipment.trackingEvents[0].timestamp).toLocaleString('fr-FR')} 
                        • {shipment.trackingEvents[0].location}
                      </p>
                    </div>
                  </div>
                )}

                {/* Notes */}
                {shipment.notes && (
                  <div className="mb-4">
                    <p className="text-xs text-gray-500 mb-1">Notes</p>
                    <p className="text-sm text-gray-700">{shipment.notes}</p>
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="ml-6 flex flex-col space-y-2">
                <TrackingButton trackingNumber={shipment.trackingNumber} />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
