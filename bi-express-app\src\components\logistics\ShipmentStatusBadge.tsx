import { getShipmentStatusColor, getShipmentStatusLabel } from '@/lib/logistics'
import { ShipmentStatus } from '@/types'

interface ShipmentStatusBadgeProps {
  status: ShipmentStatus
}

export function ShipmentStatusBadge({ status }: ShipmentStatusBadgeProps) {
  const colorClass = getShipmentStatusColor(status)
  const label = getShipmentStatusLabel(status)

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>
      {label}
    </span>
  )
}
