{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/client/ClientTypeSelector.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Truck, \n  ShoppingCart, \n  ArrowRight,\n  Check,\n  Package,\n  DollarSign,\n  Clock,\n  Users\n} from 'lucide-react'\n\ninterface ClientTypeSelectorProps {\n  onSelect?: (type: 'LOGISTICS' | 'COMMERCE') => void\n  showComparison?: boolean\n}\n\nexport function ClientTypeSelector({ onSelect, showComparison = true }: ClientTypeSelectorProps) {\n  const [selectedType, setSelectedType] = useState<'LOGISTICS' | 'COMMERCE' | null>(null)\n  const router = useRouter()\n\n  const handleSelect = (type: 'LOGISTICS' | 'COMMERCE') => {\n    setSelectedType(type)\n    if (onSelect) {\n      onSelect(type)\n    } else {\n      // Navigation par défaut\n      const route = type === 'LOGISTICS' ? '/logistics-client' : '/commerce-client'\n      router.push(route)\n    }\n  }\n\n  const clientTypes = {\n    LOGISTICS: {\n      title: 'Client Logistique',\n      subtitle: 'Transport uniquement',\n      description: 'Vous avez déjà vos produits et cherchez uniquement un service de transport fiable entre le Nigeria et Dakar.',\n      icon: Truck,\n      color: 'blue',\n      features: [\n        'Transport de vos marchandises existantes',\n        'Tarification transparente au poids/volume',\n        'Suivi en temps réel de vos expéditions',\n        'Consolidation avec d\\'autres clients',\n        'Assurance transport optionnelle',\n        'Enlèvement et livraison inclus'\n      ],\n      pricing: {\n        base: 'À partir de 850 CFA/kg',\n        margin: '20% de marge service',\n        example: 'Ex: 100kg Lagos-Dakar = ~85,000 CFA'\n      },\n      advantages: [\n        'Prix compétitifs',\n        'Flexibilité totale',\n        'Pas d\\'intermédiaire produit'\n      ]\n    },\n    COMMERCE: {\n      title: 'Client Commerce',\n      subtitle: 'Achat + Transport intégré',\n      description: 'Vous souhaitez acheter des produits au Nigeria et les recevoir directement à Dakar, avec un service tout-en-un.',\n      icon: ShoppingCart,\n      color: 'purple',\n      features: [\n        'Catalogue de produits sélectionnés',\n        'Prix \"rendu Dakar\" tout inclus',\n        'Sourcing et négociation inclus',\n        'Contrôle qualité avant expédition',\n        'Gestion complète de A à Z',\n        'Garantie satisfaction'\n      ],\n      pricing: {\n        base: 'Prix produit + 35% marge globale',\n        margin: 'Transport et marge intégrés',\n        example: 'Ex: Produit 100k + transport = ~135k CFA'\n      },\n      advantages: [\n        'Service clé en main',\n        'Pas de gestion logistique',\n        'Garantie qualité'\n      ]\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* En-tête */}\n      <div className=\"text-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">\n          Choisissez votre type de service\n        </h2>\n        <p className=\"text-gray-600\">\n          Sélectionnez le service qui correspond le mieux à vos besoins\n        </p>\n      </div>\n\n      {/* Sélection des types */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {Object.entries(clientTypes).map(([type, config]) => {\n          const Icon = config.icon\n          const isSelected = selectedType === type\n          const colorClasses = {\n            blue: {\n              border: isSelected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200 hover:border-blue-300',\n              bg: 'bg-blue-100',\n              text: 'text-blue-600',\n              button: 'bg-blue-600 hover:bg-blue-700'\n            },\n            purple: {\n              border: isSelected ? 'border-purple-500 ring-2 ring-purple-200' : 'border-gray-200 hover:border-purple-300',\n              bg: 'bg-purple-100',\n              text: 'text-purple-600',\n              button: 'bg-purple-600 hover:bg-purple-700'\n            }\n          }\n          const colors = colorClasses[config.color as keyof typeof colorClasses]\n\n          return (\n            <Card \n              key={type}\n              className={`cursor-pointer transition-all duration-200 ${colors.border}`}\n              onClick={() => handleSelect(type as 'LOGISTICS' | 'COMMERCE')}\n            >\n              <CardHeader>\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className={`p-2 rounded-lg ${colors.bg}`}>\n                      <Icon className={`h-6 w-6 ${colors.text}`} />\n                    </div>\n                    <div>\n                      <CardTitle className=\"text-lg\">{config.title}</CardTitle>\n                      <CardDescription className=\"font-medium\">\n                        {config.subtitle}\n                      </CardDescription>\n                    </div>\n                  </div>\n                  {isSelected && (\n                    <div className={`p-1 rounded-full ${colors.bg}`}>\n                      <Check className={`h-4 w-4 ${colors.text}`} />\n                    </div>\n                  )}\n                </div>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-gray-600 mb-4\">\n                  {config.description}\n                </p>\n\n                {/* Fonctionnalités */}\n                <div className=\"space-y-2 mb-4\">\n                  <h4 className=\"font-medium text-gray-900\">Fonctionnalités incluses</h4>\n                  <ul className=\"space-y-1\">\n                    {config.features.slice(0, 4).map((feature, index) => (\n                      <li key={index} className=\"flex items-center text-sm text-gray-600\">\n                        <Check className=\"h-3 w-3 text-green-500 mr-2 flex-shrink-0\" />\n                        {feature}\n                      </li>\n                    ))}\n                    {config.features.length > 4 && (\n                      <li className=\"text-sm text-gray-500\">\n                        +{config.features.length - 4} autres fonctionnalités\n                      </li>\n                    )}\n                  </ul>\n                </div>\n\n                {/* Tarification */}\n                <div className=\"bg-gray-50 rounded-lg p-3 mb-4\">\n                  <h4 className=\"font-medium text-gray-900 mb-2\">Tarification</h4>\n                  <div className=\"space-y-1 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Base:</span>\n                      <span className=\"font-medium\">{config.pricing.base}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Marge:</span>\n                      <span className=\"font-medium\">{config.pricing.margin}</span>\n                    </div>\n                    <div className=\"text-xs text-gray-500 mt-2\">\n                      {config.pricing.example}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Avantages */}\n                <div className=\"flex flex-wrap gap-2 mb-4\">\n                  {config.advantages.map((advantage, index) => (\n                    <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                      {advantage}\n                    </Badge>\n                  ))}\n                </div>\n\n                <Button \n                  className={`w-full ${colors.button} text-white`}\n                  onClick={(e) => {\n                    e.stopPropagation()\n                    handleSelect(type as 'LOGISTICS' | 'COMMERCE')\n                  }}\n                >\n                  Choisir ce service\n                  <ArrowRight className=\"h-4 w-4 ml-2\" />\n                </Button>\n              </CardContent>\n            </Card>\n          )\n        })}\n      </div>\n\n      {/* Tableau comparatif */}\n      {showComparison && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Comparaison des services</CardTitle>\n            <CardDescription>\n              Tableau détaillé pour vous aider à choisir\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full text-sm\">\n                <thead>\n                  <tr className=\"border-b\">\n                    <th className=\"text-left py-3 px-4\">Critère</th>\n                    <th className=\"text-center py-3 px-4\">\n                      <div className=\"flex items-center justify-center\">\n                        <Truck className=\"h-4 w-4 mr-2 text-blue-600\" />\n                        Logistique\n                      </div>\n                    </th>\n                    <th className=\"text-center py-3 px-4\">\n                      <div className=\"flex items-center justify-center\">\n                        <ShoppingCart className=\"h-4 w-4 mr-2 text-purple-600\" />\n                        Commerce\n                      </div>\n                    </th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr className=\"border-b\">\n                    <td className=\"py-3 px-4 font-medium\">Sourcing produits</td>\n                    <td className=\"py-3 px-4 text-center text-red-600\">✗ À votre charge</td>\n                    <td className=\"py-3 px-4 text-center text-green-600\">✓ Inclus</td>\n                  </tr>\n                  <tr className=\"border-b\">\n                    <td className=\"py-3 px-4 font-medium\">Contrôle qualité</td>\n                    <td className=\"py-3 px-4 text-center text-red-600\">✗ À votre charge</td>\n                    <td className=\"py-3 px-4 text-center text-green-600\">✓ Inclus</td>\n                  </tr>\n                  <tr className=\"border-b\">\n                    <td className=\"py-3 px-4 font-medium\">Transport</td>\n                    <td className=\"py-3 px-4 text-center text-green-600\">✓ Inclus</td>\n                    <td className=\"py-3 px-4 text-center text-green-600\">✓ Inclus</td>\n                  </tr>\n                  <tr className=\"border-b\">\n                    <td className=\"py-3 px-4 font-medium\">Flexibilité prix</td>\n                    <td className=\"py-3 px-4 text-center text-green-600\">✓ Élevée</td>\n                    <td className=\"py-3 px-4 text-center text-orange-600\">~ Modérée</td>\n                  </tr>\n                  <tr className=\"border-b\">\n                    <td className=\"py-3 px-4 font-medium\">Simplicité</td>\n                    <td className=\"py-3 px-4 text-center text-orange-600\">~ Modérée</td>\n                    <td className=\"py-3 px-4 text-center text-green-600\">✓ Maximale</td>\n                  </tr>\n                  <tr>\n                    <td className=\"py-3 px-4 font-medium\">Marge appliquée</td>\n                    <td className=\"py-3 px-4 text-center\">20% sur transport</td>\n                    <td className=\"py-3 px-4 text-center\">35% globale</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Aide à la décision */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center text-blue-600\">\n              <Truck className=\"h-5 w-5 mr-2\" />\n              Choisir Logistique si...\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <ul className=\"space-y-2 text-sm\">\n              <li className=\"flex items-start\">\n                <Check className=\"h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0\" />\n                Vous avez déjà vos fournisseurs au Nigeria\n              </li>\n              <li className=\"flex items-start\">\n                <Check className=\"h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0\" />\n                Vous maîtrisez la qualité de vos produits\n              </li>\n              <li className=\"flex items-start\">\n                <Check className=\"h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0\" />\n                Vous voulez optimiser vos coûts de transport\n              </li>\n              <li className=\"flex items-start\">\n                <Check className=\"h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0\" />\n                Vous expédiez régulièrement\n              </li>\n            </ul>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center text-purple-600\">\n              <ShoppingCart className=\"h-5 w-5 mr-2\" />\n              Choisir Commerce si...\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <ul className=\"space-y-2 text-sm\">\n              <li className=\"flex items-start\">\n                <Check className=\"h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0\" />\n                Vous débutez dans l'import Nigeria-Dakar\n              </li>\n              <li className=\"flex items-start\">\n                <Check className=\"h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0\" />\n                Vous voulez un service clé en main\n              </li>\n              <li className=\"flex items-start\">\n                <Check className=\"h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0\" />\n                Vous n'avez pas de contacts au Nigeria\n              </li>\n              <li className=\"flex items-start\">\n                <Check className=\"h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0\" />\n                Vous préférez un prix fixe \"rendu Dakar\"\n              </li>\n            </ul>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;;;;;;;;;;;;;;AAIA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAuBO,SAAS,mBAAmB,EAAE,QAAQ,EAAE,iBAAiB,IAAI,EAA2B;IAC7F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IAClF,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,gBAAgB;QAChB,IAAI,UAAU;YACZ,SAAS;QACX,OAAO;YACL,wBAAwB;YACxB,MAAM,QAAQ,SAAS,cAAc,sBAAsB;YAC3D,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,cAAc;QAClB,WAAW;YACT,OAAO;YACP,UAAU;YACV,aAAa;YACb,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;gBACP,MAAM;gBACN,QAAQ;gBACR,SAAS;YACX;YACA,YAAY;gBACV;gBACA;gBACA;aACD;QACH;QACA,UAAU;YACR,OAAO;YACP,UAAU;YACV,aAAa;YACb,MAAM,sNAAA,CAAA,eAAY;YAClB,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;gBACP,MAAM;gBACN,QAAQ;gBACR,SAAS;YACX;YACA,YAAY;gBACV;gBACA;gBACA;aACD;QACH;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,8OAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,MAAM,OAAO;oBAC9C,MAAM,OAAO,OAAO,IAAI;oBACxB,MAAM,aAAa,iBAAiB;oBACpC,MAAM,eAAe;wBACnB,MAAM;4BACJ,QAAQ,aAAa,yCAAyC;4BAC9D,IAAI;4BACJ,MAAM;4BACN,QAAQ;wBACV;wBACA,QAAQ;4BACN,QAAQ,aAAa,6CAA6C;4BAClE,IAAI;4BACJ,MAAM;4BACN,QAAQ;wBACV;oBACF;oBACA,MAAM,SAAS,YAAY,CAAC,OAAO,KAAK,CAA8B;oBAEtE,qBACE,8OAAC;wBAEC,WAAW,CAAC,2CAA2C,EAAE,OAAO,MAAM,EAAE;wBACxE,SAAS,IAAM,aAAa;;0CAE5B,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,eAAe,EAAE,OAAO,EAAE,EAAE;8DAC3C,cAAA,8OAAC;wDAAK,WAAW,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE;;;;;;;;;;;8DAE3C,8OAAC;;sEACC,8OAAC;4DAAU,WAAU;sEAAW,OAAO,KAAK;;;;;;sEAC5C,8OAAC;4DAAgB,WAAU;sEACxB,OAAO,QAAQ;;;;;;;;;;;;;;;;;;wCAIrB,4BACC,8OAAC;4CAAI,WAAW,CAAC,iBAAiB,EAAE,OAAO,EAAE,EAAE;sDAC7C,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;0CAKlD,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDACV,OAAO,WAAW;;;;;;kDAIrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA4B;;;;;;0DAC1C,8OAAC;gDAAG,WAAU;;oDACX,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBACzC,8OAAC;4DAAe,WAAU;;8EACxB,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB;;2DAFM;;;;;oDAKV,OAAO,QAAQ,CAAC,MAAM,GAAG,mBACxB,8OAAC;wDAAG,WAAU;;4DAAwB;4DAClC,OAAO,QAAQ,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;;;;;;;kDAOrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAe,OAAO,OAAO,CAAC,IAAI;;;;;;;;;;;;kEAEpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAe,OAAO,OAAO,CAAC,MAAM;;;;;;;;;;;;kEAEtD,8OAAC;wDAAI,WAAU;kEACZ,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;kDAM7B,8OAAC;wCAAI,WAAU;kDACZ,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACjC,8OAAC;gDAAkB,SAAQ;gDAAY,WAAU;0DAC9C;+CADS;;;;;;;;;;kDAMhB,8OAAC;wCACC,WAAW,CAAC,OAAO,EAAE,OAAO,MAAM,CAAC,WAAW,CAAC;wCAC/C,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,aAAa;wCACf;;4CACD;0DAEC,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;uBAlFrB;;;;;gBAuFX;;;;;;YAID,gCACC,8OAAC;;kCACC,8OAAC;;0CACC,8OAAC;0CAAU;;;;;;0CACX,8OAAC;0CAAgB;;;;;;;;;;;;kCAInB,8OAAC;kCACC,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;kDACC,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAAsB;;;;;;8DACpC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAA+B;;;;;;;;;;;;8DAIpD,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,sNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;4DAAiC;;;;;;;;;;;;;;;;;;;;;;;kDAMjE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;;;;;;;0DAEvD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;;;;;;;0DAEvD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;;;;;;;0DAEvD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;;;;;;;0DAExD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;;;;;;;0DAEvD,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;0CACC,cAAA,8OAAC;oCAAU,WAAU;;sDACnB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAItC,8OAAC;0CACC,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAqD;;;;;;;sDAGxE,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAqD;;;;;;;sDAGxE,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAqD;;;;;;;sDAGxE,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAqD;;;;;;;;;;;;;;;;;;;;;;;;kCAO9E,8OAAC;;0CACC,8OAAC;0CACC,cAAA,8OAAC;oCAAU,WAAU;;sDACnB,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI7C,8OAAC;0CACC,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAqD;;;;;;;sDAGxE,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAqD;;;;;;;sDAGxE,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAqD;;;;;;;sDAGxE,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAqD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStF", "debugId": null}}, {"offset": {"line": 1015, "column": 0}, "map": {"version": 3, "file": "arrow-right.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/arrow-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'm12 5 7 7-7 7', key: 'xquz4c' }],\n];\n\n/**\n * @component @name ArrowRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJtMTIgNSA3IDctNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowRight = createLucideIcon('arrow-right', __iconNode);\n\nexport default ArrowRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1061, "column": 0}, "map": {"version": 3, "file": "check.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/check.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M20 6 9 17l-5-5', key: '1gmf2c' }]];\n\n/**\n * @component @name Check\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgNiA5IDE3bC01LTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Check = createLucideIcon('check', __iconNode);\n\nexport default Check;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,iBAAmB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAahF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}