import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const carrierId = searchParams.get('carrierId')
    const transportMode = searchParams.get('transportMode')

    const where: any = {}

    if (status) {
      where.status = status
    }

    if (carrierId) {
      where.carrierId = carrierId
    }

    if (transportMode) {
      where.transportMode = transportMode
    }

    const shipments = await prisma.shipment.findMany({
      where,
      include: {
        carrier: {
          select: {
            id: true,
            name: true,
            phone: true,
            rating: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(shipments)
  } catch (error) {
    console.error('Erreur lors de la récupération des expéditions:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la récupération des expéditions' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      carrierId,
      transportMode,
      orderIds,
      pickupDate,
      estimatedDelivery,
      destinationCity = 'DAKAR',
      originCity = 'LAGOS', // Valeur par défaut
      notes,
      priority
    } = body

    // Validation des champs obligatoires
    if (!carrierId || !transportMode || !orderIds || orderIds.length === 0 || !pickupDate) {
      return NextResponse.json(
        { error: 'Transporteur, mode de transport, commandes et date d\'enlèvement sont obligatoires' },
        { status: 400 }
      )
    }

    // Vérifier que le transporteur existe
    const carrier = await prisma.carrier.findUnique({
      where: { id: carrierId }
    })

    if (!carrier) {
      return NextResponse.json(
        { error: 'Transporteur non trouvé' },
        { status: 404 }
      )
    }

    // Vérifier que le transporteur supporte ce mode de transport
    if (!carrier.transportModes.includes(transportMode)) {
      return NextResponse.json(
        { error: 'Ce transporteur ne supporte pas ce mode de transport' },
        { status: 400 }
      )
    }

    // Vérifier que toutes les commandes existent et sont confirmées
    const orders = await prisma.order.findMany({
      where: {
        id: { in: orderIds },
        status: { in: ['PENDING', 'CONFIRMED'] }
      },
      include: {
        orderItems: {
          include: {
            product: true
          }
        }
      }
    })

    if (orders.length !== orderIds.length) {
      return NextResponse.json(
        { error: 'Certaines commandes sont introuvables ou non confirmées' },
        { status: 400 }
      )
    }

    // Calculer le poids total et le coût de transport
    let totalWeight = 0
    let totalValue = 0

    orders.forEach(order => {
      order.orderItems.forEach(item => {
        totalWeight += (item.product.weight || 25) * item.quantity // 25kg par défaut
        totalValue += item.totalPrice
      })
    })

    const transportCostPerKg = transportMode === 'AIR' ? 150 : 75
    const totalTransportCost = totalWeight * transportCostPerKg

    // Générer un numéro de suivi unique
    const shipmentCount = await prisma.shipment.count()
    const trackingNumber = `${transportMode === 'AIR' ? 'AIR' : 'TRK'}-${new Date().getFullYear()}-${String(shipmentCount + 1).padStart(6, '0')}`

    // Créer l'expédition
    const shipment = await prisma.shipment.create({
      data: {
        trackingNumber,
        carrierId,
        transportMode,
        status: 'PENDING',
        originCity,
        pickupDate: new Date(pickupDate),
        estimatedDelivery: estimatedDelivery ? new Date(estimatedDelivery) : null,
        destinationCity,
        currentWeight: totalWeight,
        maxWeight: totalWeight * 1.5, // Capacité maximale = 150% du poids actuel
        totalTransportCost,
        notes: notes || '',
        orders: {
          connect: orderIds.map((id: string) => ({ id }))
        }
      },
      include: {
        carrier: {
          select: {
            id: true,
            name: true,
            phone: true,
            rating: true
          }
        },
        orders: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                city: true,
                type: true
              }
            }
          }
        }
      }
    })

    // Mettre à jour le statut des commandes
    await prisma.order.updateMany({
      where: {
        id: { in: orderIds }
      },
      data: {
        status: 'SHIPPED'
      }
    })

    // Créer l'événement de suivi initial
    await prisma.trackingEvent.create({
      data: {
        shipmentId: shipment.id,
        eventType: 'PICKUP_SCHEDULED',
        description: 'Expédition créée et en attente d\'enlèvement',
        location: originCity
      }
    })

    return NextResponse.json(shipment, { status: 201 })
  } catch (error) {
    console.error('Erreur lors de la création de l\'expédition:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la création de l\'expédition' },
      { status: 500 }
    )
  }
}
