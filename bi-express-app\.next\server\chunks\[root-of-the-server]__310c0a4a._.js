module.exports = {

"[project]/.next-internal/server/app/api/shipments/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[project]/src/app/api/shipments/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const status = searchParams.get('status');
        const carrierId = searchParams.get('carrierId');
        const transportMode = searchParams.get('transportMode');
        const where = {};
        if (status) {
            where.status = status;
        }
        if (carrierId) {
            where.carrierId = carrierId;
        }
        if (transportMode) {
            where.transportMode = transportMode;
        }
        const shipments = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].shipment.findMany({
            where,
            include: {
                carrier: {
                    select: {
                        id: true,
                        name: true,
                        phone: true,
                        rating: true
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(shipments);
    } catch (error) {
        console.error('Erreur lors de la récupération des expéditions:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Erreur lors de la récupération des expéditions'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { carrierId, transportMode, orderIds, pickupDate, estimatedDelivery, destinationCity = 'DAKAR', originCity = 'LAGOS', notes, priority } = body;
        // Validation des champs obligatoires
        if (!carrierId || !transportMode || !orderIds || orderIds.length === 0 || !pickupDate) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Transporteur, mode de transport, commandes et date d\'enlèvement sont obligatoires'
            }, {
                status: 400
            });
        }
        // Vérifier que le transporteur existe
        const carrier = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].carrier.findUnique({
            where: {
                id: carrierId
            }
        });
        if (!carrier) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Transporteur non trouvé'
            }, {
                status: 404
            });
        }
        // Vérifier que le transporteur supporte ce mode de transport
        if (!carrier.transportModes.includes(transportMode)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Ce transporteur ne supporte pas ce mode de transport'
            }, {
                status: 400
            });
        }
        // Vérifier que toutes les commandes existent et sont confirmées
        const orders = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].order.findMany({
            where: {
                id: {
                    in: orderIds
                },
                status: {
                    in: [
                        'PENDING',
                        'CONFIRMED'
                    ]
                }
            },
            include: {
                orderItems: {
                    include: {
                        product: true
                    }
                }
            }
        });
        if (orders.length !== orderIds.length) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Certaines commandes sont introuvables ou non confirmées'
            }, {
                status: 400
            });
        }
        // Calculer le poids total et le coût de transport
        let totalWeight = 0;
        let totalValue = 0;
        orders.forEach((order)=>{
            order.orderItems.forEach((item)=>{
                totalWeight += (item.product.weight || 25) * item.quantity // 25kg par défaut
                ;
                totalValue += item.totalPrice;
            });
        });
        const transportCostPerKg = transportMode === 'AIR' ? 150 : 75;
        const totalTransportCost = totalWeight * transportCostPerKg;
        // Générer un numéro de suivi unique
        const shipmentCount = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].shipment.count();
        const trackingNumber = `${transportMode === 'AIR' ? 'AIR' : 'TRK'}-${new Date().getFullYear()}-${String(shipmentCount + 1).padStart(6, '0')}`;
        // Créer l'expédition
        const shipment = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].shipment.create({
            data: {
                trackingNumber,
                carrierId,
                transportMode,
                status: 'PENDING',
                originCity,
                pickupDate: new Date(pickupDate),
                estimatedDelivery: estimatedDelivery ? new Date(estimatedDelivery) : null,
                destinationCity,
                currentWeight: totalWeight,
                maxWeight: totalWeight * 1.5,
                totalTransportCost,
                notes: notes || '',
                orders: {
                    connect: orderIds.map((id)=>({
                            id
                        }))
                }
            },
            include: {
                carrier: {
                    select: {
                        id: true,
                        name: true,
                        phone: true,
                        rating: true
                    }
                },
                orders: {
                    include: {
                        customer: {
                            select: {
                                id: true,
                                name: true,
                                city: true,
                                type: true
                            }
                        }
                    }
                }
            }
        });
        // Mettre à jour le statut des commandes
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].order.updateMany({
            where: {
                id: {
                    in: orderIds
                }
            },
            data: {
                status: 'SHIPPED'
            }
        });
        // Créer l'événement de suivi initial
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].trackingEvent.create({
            data: {
                shipmentId: shipment.id,
                eventType: 'PICKUP_SCHEDULED',
                description: 'Expédition créée et en attente d\'enlèvement',
                location: originCity
            }
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(shipment, {
            status: 201
        });
    } catch (error) {
        console.error('Erreur lors de la création de l\'expédition:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Erreur lors de la création de l\'expédition'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__310c0a4a._.js.map