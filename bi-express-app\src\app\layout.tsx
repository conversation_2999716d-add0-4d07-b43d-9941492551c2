import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AppLayout } from "@/components/layout/AppLayout";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Bi-Express - Vente en Gros Nigeria-Dakar",
  description: "Application de gestion pour la vente en gros de tissus, cosmétiques et mèches entre le Nigeria et Dakar",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr" className={inter.variable}>
      <body className="font-sans antialiased bg-gray-50">
        <AppLayout>
          {children}
        </AppLayout>
      </body>
    </html>
  );
}
