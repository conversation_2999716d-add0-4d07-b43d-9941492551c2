import type { Metadata } from "next";
import { Inter, Roboto } from "next/font/google";
import "./globals.css";
import { AppLayout } from "@/components/layout/AppLayout";
import { NotificationProvider } from "@/contexts/NotificationContext";
import { ToastContainer } from "@/components/notifications/ToastContainer";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: 'swap',
});

const roboto = Roboto({
  subsets: ["latin"],
  variable: "--font-roboto",
  weight: ['300', '400', '500', '700'],
  display: 'swap',
});

export const metadata: Metadata = {
  title: "Bi-Express - Vente en Gros Nigeria-Dakar",
  description: "Application de gestion pour la vente en gros de tissus, cosmétiques et mèches entre le Nigeria et Dakar",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr" className={`${inter.variable} ${roboto.variable}`}>
      <body className="font-inter antialiased bg-gray-50 text-gray-900 text-sm min-h-screen">
        <NotificationProvider>
          <AppLayout>
            {children}
          </AppLayout>
          <ToastContainer />
        </NotificationProvider>
      </body>
    </html>
  );
}
