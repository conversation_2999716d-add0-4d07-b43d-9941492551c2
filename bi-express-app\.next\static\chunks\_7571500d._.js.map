{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/ProductFilters.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Search, Filter, AlertTriangle } from 'lucide-react'\n\nexport function ProductFilters() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState('')\n  const [selectedCity, setSelectedCity] = useState('')\n  const [showLowStock, setShowLowStock] = useState(false)\n  const [priceRange, setPriceRange] = useState('')\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4\">\n        {/* Search */}\n        <div className=\"lg:col-span-2 relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n          <input\n            type=\"text\"\n            placeholder=\"Rechercher un produit...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n        </div>\n\n        {/* Category Filter */}\n        <div>\n          <select\n            value={selectedCategory}\n            onChange={(e) => setSelectedCategory(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"\">Toutes catégories</option>\n            <option value=\"TISSUS\">Tissus</option>\n            <option value=\"COSMETIQUES\">Cosmétiques</option>\n            <option value=\"MECHES\">Mèches</option>\n          </select>\n        </div>\n\n        {/* City Filter */}\n        <div>\n          <select\n            value={selectedCity}\n            onChange={(e) => setSelectedCity(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"\">Toutes villes</option>\n            <option value=\"LAGOS\">Lagos</option>\n            <option value=\"ABUJA\">Abuja</option>\n            <option value=\"KANO\">Kano</option>\n          </select>\n        </div>\n\n        {/* Price Range */}\n        <div>\n          <select\n            value={priceRange}\n            onChange={(e) => setPriceRange(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"\">Tous prix</option>\n            <option value=\"0-10000\">0 - 10,000 CFA</option>\n            <option value=\"10000-50000\">10,000 - 50,000 CFA</option>\n            <option value=\"50000-100000\">50,000 - 100,000 CFA</option>\n            <option value=\"100000+\">100,000+ CFA</option>\n          </select>\n        </div>\n\n        {/* Actions */}\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={() => setShowLowStock(!showLowStock)}\n            className={`flex items-center px-3 py-2 rounded-md transition-colors ${\n              showLowStock\n                ? 'bg-red-100 text-red-700 border border-red-300'\n                : 'bg-gray-100 text-gray-700 border border-gray-300'\n            }`}\n          >\n            <AlertTriangle className=\"h-4 w-4 mr-1\" />\n            Stock faible\n          </button>\n        </div>\n      </div>\n\n      {/* Active Filters */}\n      {(searchTerm || selectedCategory || selectedCity || showLowStock || priceRange) && (\n        <div className=\"mt-4 flex flex-wrap items-center gap-2\">\n          <span className=\"text-sm text-gray-500\">Filtres actifs:</span>\n          \n          {searchTerm && (\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n              Recherche: {searchTerm}\n              <button\n                onClick={() => setSearchTerm('')}\n                className=\"ml-1 text-blue-600 hover:text-blue-800\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n          \n          {selectedCategory && (\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800\">\n              Catégorie: {selectedCategory}\n              <button\n                onClick={() => setSelectedCategory('')}\n                className=\"ml-1 text-purple-600 hover:text-purple-800\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n          \n          {selectedCity && (\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n              Ville: {selectedCity}\n              <button\n                onClick={() => setSelectedCity('')}\n                className=\"ml-1 text-green-600 hover:text-green-800\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n          \n          {showLowStock && (\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n              Stock faible\n              <button\n                onClick={() => setShowLowStock(false)}\n                className=\"ml-1 text-red-600 hover:text-red-800\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n          \n          {priceRange && (\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n              Prix: {priceRange} CFA\n              <button\n                onClick={() => setPriceRange('')}\n                className=\"ml-1 text-yellow-600 hover:text-yellow-800\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n          \n          <button\n            onClick={() => {\n              setSearchTerm('')\n              setSelectedCategory('')\n              setSelectedCity('')\n              setShowLowStock(false)\n              setPriceRange('')\n            }}\n            className=\"text-xs text-gray-500 hover:text-gray-700 underline\"\n          >\n            Effacer tous les filtres\n          </button>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;kCAKd,6LAAC;kCACC,cAAA,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4BACnD,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,6LAAC;oCAAO,OAAM;8CAAS;;;;;;8CACvB,6LAAC;oCAAO,OAAM;8CAAc;;;;;;8CAC5B,6LAAC;oCAAO,OAAM;8CAAS;;;;;;;;;;;;;;;;;kCAK3B,6LAAC;kCACC,cAAA,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4BAC/C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,6LAAC;oCAAO,OAAM;8CAAQ;;;;;;8CACtB,6LAAC;oCAAO,OAAM;8CAAQ;;;;;;8CACtB,6LAAC;oCAAO,OAAM;8CAAO;;;;;;;;;;;;;;;;;kCAKzB,6LAAC;kCACC,cAAA,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,6LAAC;oCAAO,OAAM;8CAAU;;;;;;8CACxB,6LAAC;oCAAO,OAAM;8CAAc;;;;;;8CAC5B,6LAAC;oCAAO,OAAM;8CAAe;;;;;;8CAC7B,6LAAC;oCAAO,OAAM;8CAAU;;;;;;;;;;;;;;;;;kCAK5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS,IAAM,gBAAgB,CAAC;4BAChC,WAAW,CAAC,yDAAyD,EACnE,eACI,kDACA,oDACJ;;8CAEF,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;YAO/C,CAAC,cAAc,oBAAoB,gBAAgB,gBAAgB,UAAU,mBAC5E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;oBAEvC,4BACC,6LAAC;wBAAK,WAAU;;4BAAoG;4BACtG;0CACZ,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAU;0CACX;;;;;;;;;;;;oBAMJ,kCACC,6LAAC;wBAAK,WAAU;;4BAAwG;4BAC1G;0CACZ,6LAAC;gCACC,SAAS,IAAM,oBAAoB;gCACnC,WAAU;0CACX;;;;;;;;;;;;oBAMJ,8BACC,6LAAC;wBAAK,WAAU;;4BAAsG;4BAC5G;0CACR,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;;;;;;;oBAMJ,8BACC,6LAAC;wBAAK,WAAU;;4BAAkG;0CAEhH,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;;;;;;;oBAMJ,4BACC,6LAAC;wBAAK,WAAU;;4BAAwG;4BAC/G;4BAAW;0CAClB,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAU;0CACX;;;;;;;;;;;;kCAML,6LAAC;wBACC,SAAS;4BACP,cAAc;4BACd,oBAAoB;4BACpB,gBAAgB;4BAChB,gBAAgB;4BAChB,cAAc;wBAChB;wBACA,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAOX;GAlKgB;KAAA", "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/AddProductButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Plus } from 'lucide-react'\n\nexport function AddProductButton() {\n  const [isOpen, setIsOpen] = useState(false)\n\n  return (\n    <>\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n      >\n        <Plus className=\"h-4 w-4 mr-2\" />\n        Ajouter un produit\n      </button>\n\n      {/* Modal placeholder - à implémenter plus tard */}\n      {isOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\n            <h2 className=\"text-lg font-semibold mb-4\">Ajouter un produit</h2>\n            \n            <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Nom du produit\n                  </label>\n                  <input\n                    type=\"text\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"Ex: Wax Hollandais Premium\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Catégorie\n                  </label>\n                  <select className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n                    <option value=\"\">Sélectionner une catégorie</option>\n                    <option value=\"TISSUS\">Tissus</option>\n                    <option value=\"COSMETIQUES\">Cosmétiques</option>\n                    <option value=\"MECHES\">Mèches</option>\n                  </select>\n                </div>\n              </div>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Prix fournisseur (NGN)\n                  </label>\n                  <input\n                    type=\"number\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"15000\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Taux logistique (%)\n                  </label>\n                  <input\n                    type=\"number\"\n                    defaultValue=\"30\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Marge (%)\n                  </label>\n                  <input\n                    type=\"number\"\n                    defaultValue=\"20\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n              \n              <div className=\"bg-blue-50 p-4 rounded-lg\">\n                <h3 className=\"text-sm font-medium text-blue-900 mb-2\">Calcul automatique des prix</h3>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-blue-700\">Prix de revient:</span>\n                    <span className=\"font-medium ml-2\">19,500 NGN</span>\n                  </div>\n                  <div>\n                    <span className=\"text-blue-700\">Prix de vente:</span>\n                    <span className=\"font-medium ml-2\">23,400 NGN</span>\n                  </div>\n                  <div>\n                    <span className=\"text-blue-700\">Bénéfice unitaire:</span>\n                    <span className=\"font-medium ml-2 text-green-600\">3,900 NGN</span>\n                  </div>\n                  <div>\n                    <span className=\"text-blue-700\">Prix CFA:</span>\n                    <span className=\"font-medium ml-2\">~15,600 CFA</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"flex justify-end space-x-3 mt-6\">\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors\"\n              >\n                Annuler\n              </button>\n              <button className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\">\n                Ajouter le produit\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE;;0BACE,6LAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAU;;kCAEV,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;YAKlC,wBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAE3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC;4DAAO,OAAM;sEAAG;;;;;;sEACjB,6LAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,6LAAC;4DAAO,OAAM;sEAAc;;;;;;sEAC5B,6LAAC;4DAAO,OAAM;sEAAS;;;;;;;;;;;;;;;;;;;;;;;;8CAK7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,cAAa;oDACb,WAAU;;;;;;;;;;;;sDAId,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,cAAa;oDACb,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;;;;;;;8DAErC,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;;;;;;;8DAErC,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAkC;;;;;;;;;;;;8DAEpD,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCAAO,WAAU;8CAAkF;;;;;;;;;;;;;;;;;;;;;;;;;AASlH;GAvHgB;KAAA", "debugId": null}}, {"offset": {"line": 803, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}