@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #111827; /* gray-900 for 4.5:1 contrast */
  --text-primary: #111827; /* gray-900 */
  --text-secondary: #374151; /* gray-700 */
  --text-muted: #6b7280; /* gray-500 */
  --border-color: #e5e7eb; /* gray-200 */
  --border-focus: #3b82f6; /* blue-500 */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-inter: var(--font-inter);
  --font-roboto: var(--font-roboto);
  --font-sans: var(--font-inter), var(--font-roboto), ui-sans-serif, system-ui, sans-serif;
}

/* Ensure minimum font sizes for accessibility */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), var(--font-roboto), ui-sans-serif, system-ui, sans-serif;
  font-size: 14px; /* Minimum 14px */
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography scale with minimum 14px */
.text-xs { font-size: 14px !important; } /* Override to ensure minimum 14px */
.text-sm { font-size: 14px !important; }
.text-base { font-size: 16px; }
.text-lg { font-size: 18px; }
.text-xl { font-size: 20px; }
.text-2xl { font-size: 24px; }
.text-3xl { font-size: 30px; }

/* High contrast colors for accessibility (4.5:1 ratio) */
.text-gray-900 { color: #111827 !important; } /* Primary text */
.text-gray-800 { color: #1f2937 !important; }
.text-gray-700 { color: #374151 !important; } /* Secondary text */
.text-gray-600 { color: #4b5563 !important; }
.text-gray-500 { color: #6b7280 !important; } /* Muted text */

/* Ensure buttons and interactive elements have proper contrast */
.btn-primary {
  background-color: #3b82f6; /* blue-500 */
  color: #ffffff;
  border: 1px solid #3b82f6;
}

.btn-primary:hover {
  background-color: #2563eb; /* blue-600 */
  border-color: #2563eb;
}

/* Focus states for accessibility */
.focus\:ring-2:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px var(--border-focus);
}

/* Ensure proper spacing and readability */
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
