'use client'

import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer 
} from 'recharts'

// Données d'exemple pour le graphique
const salesData = [
  { month: 'Jan', revenue: 2400000, profit: 480000 },
  { month: 'Fév', revenue: 1398000, profit: 279600 },
  { month: 'Mar', revenue: 9800000, profit: 1960000 },
  { month: 'Avr', revenue: 3908000, profit: 781600 },
  { month: 'Mai', revenue: 4800000, profit: 960000 },
  { month: 'Jun', revenue: 3800000, profit: 760000 },
  { month: 'Jul', revenue: 4300000, profit: 860000 },
]

export function SalesChart() {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Évolution des ventes</h3>
        <p className="text-sm text-gray-600 mt-1">
          Chi<PERSON><PERSON> d'affaires et bénéfices des 7 derniers mois
        </p>
      </div>
      
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={salesData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="month" 
              stroke="#6b7280"
              fontSize={12}
            />
            <YAxis 
              stroke="#6b7280"
              fontSize={12}
              tickFormatter={(value) => `${(value / 1000000).toFixed(1)}M`}
            />
            <Tooltip 
              formatter={(value: number, name: string) => [
                `${(value / 1000000).toFixed(2)}M CFA`,
                name === 'revenue' ? 'Chiffre d\'affaires' : 'Bénéfices'
              ]}
              labelStyle={{ color: '#374151' }}
              contentStyle={{ 
                backgroundColor: 'white', 
                border: '1px solid #e5e7eb',
                borderRadius: '8px'
              }}
            />
            <Line 
              type="monotone" 
              dataKey="revenue" 
              stroke="#3b82f6" 
              strokeWidth={3}
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
            />
            <Line 
              type="monotone" 
              dataKey="profit" 
              stroke="#10b981" 
              strokeWidth={3}
              dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
      
      <div className="flex items-center justify-center space-x-6 mt-4">
        <div className="flex items-center">
          <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
          <span className="text-sm text-gray-600">Chiffre d'affaires</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
          <span className="text-sm text-gray-600">Bénéfices</span>
        </div>
      </div>
    </div>
  )
}
