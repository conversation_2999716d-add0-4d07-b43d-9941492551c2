{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/actions/shipments.ts"], "sourcesContent": ["'use server'\n\nimport { prisma } from '@/lib/prisma'\nimport { revalidatePath } from 'next/cache'\nimport { \n  generateTrackingNumber, \n  calculateTransportCosts, \n  calculateDeliveryTime,\n  validateShipmentData \n} from '@/lib/logistics'\nimport { TransportMode, SupplierCity, ShipmentStatus, TrackingEventType } from '@/types'\n\n/**\n * Crée une nouvelle expédition pour une commande\n */\nexport async function createShipment(data: {\n  orderId: string\n  carrierId: string\n  weight: number\n  transportMode: TransportMode\n  originCity: SupplierCity\n  fuelSurcharge?: number\n  notes?: string\n}) {\n  try {\n    // Validation des données\n    const errors = validateShipmentData(data)\n    if (errors.length > 0) {\n      return { success: false, error: errors.join(', ') }\n    }\n\n    // Vérifier que la commande existe et n'a pas déjà d'expédition\n    const existingOrder = await prisma.order.findUnique({\n      where: { id: data.orderId },\n      include: { shipment: true }\n    })\n\n    if (!existingOrder) {\n      return { success: false, error: 'Commande introuvable' }\n    }\n\n    if (existingOrder.shipment) {\n      return { success: false, error: 'Cette commande a déjà une expédition' }\n    }\n\n    // Générer le numéro de suivi\n    const trackingNumber = generateTrackingNumber(data.transportMode, data.originCity)\n\n    // Calculer les coûts et délais\n    const transportCost = calculateTransportCosts(\n      data.weight,\n      data.transportMode,\n      data.originCity,\n      data.fuelSurcharge || 0\n    )\n\n    const estimatedDelivery = calculateDeliveryTime(\n      data.transportMode,\n      data.originCity,\n      existingOrder.orderDate\n    )\n\n    // Créer l'expédition\n    const shipment = await prisma.shipment.create({\n      data: {\n        trackingNumber,\n        orderId: data.orderId,\n        carrierId: data.carrierId,\n        status: 'PENDING',\n        transportMode: data.transportMode,\n        originCity: data.originCity,\n        destinationCity: 'DAKAR',\n        weight: data.weight,\n        transportCost,\n        fuelSurcharge: data.fuelSurcharge || 0,\n        estimatedDelivery,\n        notes: data.notes\n      },\n      include: {\n        carrier: true,\n        order: true\n      }\n    })\n\n    // Créer l'événement de suivi initial\n    await prisma.trackingEvent.create({\n      data: {\n        shipmentId: shipment.id,\n        eventType: 'PICKUP_SCHEDULED',\n        location: data.originCity,\n        description: `Enlèvement programmé chez ${shipment.carrier.name}`,\n        timestamp: new Date()\n      }\n    })\n\n    // Mettre à jour le statut de la commande\n    await prisma.order.update({\n      where: { id: data.orderId },\n      data: { status: 'SHIPPED' }\n    })\n\n    revalidatePath('/logistics')\n    revalidatePath('/orders')\n    \n    return { success: true, shipment }\n  } catch (error) {\n    console.error('Erreur lors de la création de l\\'expédition:', error)\n    return { success: false, error: 'Erreur lors de la création de l\\'expédition' }\n  }\n}\n\n/**\n * Met à jour le statut d'une expédition\n */\nexport async function updateShipmentStatus(\n  shipmentId: string,\n  status: ShipmentStatus,\n  location?: string,\n  notes?: string\n) {\n  try {\n    const shipment = await prisma.shipment.findUnique({\n      where: { id: shipmentId },\n      include: {\n        orders: true,\n        shipmentRequests: true\n      }\n    })\n\n    if (!shipment) {\n      return { success: false, error: 'Expédition introuvable' }\n    }\n\n    // Mettre à jour l'expédition\n    const updatedShipment = await prisma.shipment.update({\n      where: { id: shipmentId },\n      data: {\n        status,\n        currentLocation: location,\n        ...(status === 'PICKED_UP' && { pickupDate: new Date() }),\n        ...(status === 'DELIVERED' && { deliveryDate: new Date() }),\n        ...(notes && { notes })\n      }\n    })\n\n    // Créer un événement de suivi\n    const eventTypeMap: Record<ShipmentStatus, TrackingEventType> = {\n      PENDING: 'PICKUP_SCHEDULED',\n      PICKED_UP: 'PICKED_UP',\n      IN_TRANSIT: 'IN_TRANSIT',\n      CUSTOMS: 'CUSTOMS_CLEARANCE',\n      OUT_FOR_DELIVERY: 'OUT_FOR_DELIVERY',\n      DELIVERED: 'DELIVERED',\n      DELAYED: 'DELAYED',\n      CANCELLED: 'DELIVERY_FAILED'\n    }\n\n    await prisma.trackingEvent.create({\n      data: {\n        shipmentId,\n        eventType: eventTypeMap[status],\n        location: location || shipment.currentLocation || shipment.originCity,\n        description: notes || `Statut mis à jour: ${status}`,\n        timestamp: new Date()\n      }\n    })\n\n    // Mettre à jour le statut de la commande si nécessaire\n    if (status === 'DELIVERED') {\n      await prisma.order.update({\n        where: { id: shipment.orderId },\n        data: { \n          status: 'DELIVERED',\n          deliveredAt: new Date()\n        }\n      })\n    }\n\n    revalidatePath('/logistics')\n    revalidatePath('/orders')\n    \n    return { success: true, shipment: updatedShipment }\n  } catch (error) {\n    console.error('Erreur lors de la mise à jour du statut:', error)\n    return { success: false, error: 'Erreur lors de la mise à jour du statut' }\n  }\n}\n\n/**\n * Récupère toutes les expéditions avec filtres\n */\nexport async function getShipments(filters?: {\n  status?: ShipmentStatus\n  carrierId?: string\n  transportMode?: TransportMode\n  dateFrom?: Date\n  dateTo?: Date\n}) {\n  try {\n    const where: any = {}\n\n    if (filters?.status) where.status = filters.status\n    if (filters?.carrierId) where.carrierId = filters.carrierId\n    if (filters?.transportMode) where.transportMode = filters.transportMode\n    if (filters?.dateFrom || filters?.dateTo) {\n      where.createdAt = {}\n      if (filters.dateFrom) where.createdAt.gte = filters.dateFrom\n      if (filters.dateTo) where.createdAt.lte = filters.dateTo\n    }\n\n    const shipments = await prisma.shipment.findMany({\n      where,\n      include: {\n        carrier: true,\n        orders: {\n          include: {\n            customer: true,\n            supplier: true\n          }\n        },\n        shipmentRequests: {\n          include: {\n            customer: true\n          }\n        },\n        trackingEvents: {\n          orderBy: { timestamp: 'desc' }\n        },\n        shipmentTracking: {\n          orderBy: { timestamp: 'desc' }\n        }\n      },\n      orderBy: { createdAt: 'desc' }\n    })\n\n    return shipments\n  } catch (error) {\n    console.error('Erreur lors de la récupération des expéditions:', error)\n    return []\n  }\n}\n\n/**\n * Récupère une expédition par son numéro de suivi\n */\nexport async function getShipmentByTracking(trackingNumber: string) {\n  try {\n    const shipment = await prisma.shipment.findUnique({\n      where: { trackingNumber },\n      include: {\n        carrier: true,\n        order: {\n          include: {\n            customer: true,\n            supplier: true,\n            orderItems: {\n              include: {\n                product: true\n              }\n            }\n          }\n        },\n        trackingEvents: {\n          orderBy: { timestamp: 'asc' }\n        }\n      }\n    })\n\n    return shipment\n  } catch (error) {\n    console.error('Erreur lors de la récupération de l\\'expédition:', error)\n    return null\n  }\n}\n\n/**\n * Ajoute un événement de suivi manuel\n */\nexport async function addTrackingEvent(\n  shipmentId: string,\n  eventType: TrackingEventType,\n  location: string,\n  description: string\n) {\n  try {\n    const event = await prisma.trackingEvent.create({\n      data: {\n        shipmentId,\n        eventType,\n        location,\n        description,\n        timestamp: new Date()\n      }\n    })\n\n    revalidatePath('/logistics')\n    \n    return { success: true, event }\n  } catch (error) {\n    console.error('Erreur lors de l\\'ajout de l\\'événement:', error)\n    return { success: false, error: 'Erreur lors de l\\'ajout de l\\'événement' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAqPsB,wBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/logistics.ts"], "sourcesContent": ["import { TransportMode, SupplierCity, ShipmentStatus } from '@/types'\n\n/**\n * Calcule les coûts de transport selon le mode et le poids\n */\nexport function calculateTransportCosts(\n  weight: number,\n  mode: TransportMode,\n  originCity: SupplierCity,\n  fuelSurcharge: number = 0\n): number {\n  // Tarifs de base par kg selon le mode de transport\n  const baseRates = {\n    ROAD: {\n      LAGOS: 500,   // 500 XOF par kg depuis Lagos\n      ABUJA: 600,   // 600 XOF par kg depuis Abuja\n      KANO: 700     // 700 XOF par kg depuis Kano\n    },\n    AIR_EXPRESS: {\n      LAGOS: 2000,  // 2000 XOF par kg depuis Lagos\n      ABUJA: 2200,  // 2200 XOF par kg depuis Abuja\n      KANO: 2500    // 2500 XOF par kg depuis Kano\n    }\n  }\n\n  const baseRate = baseRates[mode][originCity]\n  const baseCost = weight * baseRate\n  const fuelCost = baseCost * (fuelSurcharge / 100)\n  \n  return baseCost + fuelCost\n}\n\n/**\n * Calcule la durée estimée de livraison\n */\nexport function calculateDeliveryTime(\n  mode: TransportMode,\n  originCity: SupplierCity,\n  orderDate: Date = new Date()\n): Date {\n  // Durées en heures selon le mode et la ville d'origine\n  const deliveryTimes = {\n    ROAD: {\n      LAGOS: 120,   // 5 jours\n      ABUJA: 144,   // 6 jours\n      KANO: 168     // 7 jours\n    },\n    AIR_EXPRESS: {\n      LAGOS: 24,    // 1 jour\n      ABUJA: 36,    // 1.5 jours\n      KANO: 48      // 2 jours\n    }\n  }\n\n  const hoursToAdd = deliveryTimes[mode][originCity]\n  const estimatedDelivery = new Date(orderDate)\n  estimatedDelivery.setHours(estimatedDelivery.getHours() + hoursToAdd)\n  \n  return estimatedDelivery\n}\n\n/**\n * Génère un numéro de suivi unique\n */\nexport function generateTrackingNumber(\n  mode: TransportMode,\n  originCity: SupplierCity\n): string {\n  const prefix = mode === 'ROAD' ? 'RD' : 'AE'\n  const cityCode = originCity.substring(0, 2)\n  const timestamp = Date.now().toString().slice(-6)\n  const random = Math.random().toString(36).substring(2, 5).toUpperCase()\n  \n  return `${prefix}${cityCode}${timestamp}${random}`\n}\n\n/**\n * Détermine le statut de livraison basé sur les dates\n */\nexport function getDeliveryStatus(\n  estimatedDelivery: Date,\n  actualDelivery?: Date\n): 'ON_TIME' | 'DELAYED' | 'EARLY' | 'PENDING' {\n  if (!actualDelivery) return 'PENDING'\n  \n  const timeDiff = actualDelivery.getTime() - estimatedDelivery.getTime()\n  const hoursDiff = timeDiff / (1000 * 60 * 60)\n  \n  if (hoursDiff <= 0) return 'EARLY'\n  if (hoursDiff <= 24) return 'ON_TIME' // Tolérance de 24h\n  return 'DELAYED'\n}\n\n/**\n * Calcule le taux de ponctualité d'un transporteur\n */\nexport function calculateOnTimeRate(\n  totalDeliveries: number,\n  onTimeDeliveries: number\n): number {\n  if (totalDeliveries === 0) return 0\n  return Math.round((onTimeDeliveries / totalDeliveries) * 100)\n}\n\n/**\n * Obtient la couleur du statut d'expédition\n */\nexport function getShipmentStatusColor(status: ShipmentStatus): string {\n  const colors = {\n    PENDING: 'bg-gray-100 text-gray-800',\n    PICKED_UP: 'bg-blue-100 text-blue-800',\n    IN_TRANSIT: 'bg-yellow-100 text-yellow-800',\n    CUSTOMS: 'bg-orange-100 text-orange-800',\n    OUT_FOR_DELIVERY: 'bg-purple-100 text-purple-800',\n    DELIVERED: 'bg-green-100 text-green-800',\n    DELAYED: 'bg-red-100 text-red-800',\n    CANCELLED: 'bg-gray-100 text-gray-800'\n  }\n  \n  return colors[status] || 'bg-gray-100 text-gray-800'\n}\n\n/**\n * Obtient le libellé français du statut d'expédition\n */\nexport function getShipmentStatusLabel(status: ShipmentStatus): string {\n  const labels = {\n    PENDING: 'En attente',\n    PICKED_UP: 'Enlevée',\n    IN_TRANSIT: 'En transit',\n    CUSTOMS: 'En douane',\n    OUT_FOR_DELIVERY: 'En livraison',\n    DELIVERED: 'Livrée',\n    DELAYED: 'Retardée',\n    CANCELLED: 'Annulée'\n  }\n  \n  return labels[status] || status\n}\n\n/**\n * Calcule les statistiques logistiques\n */\nexport function calculateLogisticsKPIs(shipments: any[]) {\n  const total = shipments.length\n  const delivered = shipments.filter(s => s.status === 'DELIVERED').length\n  const inTransit = shipments.filter(s => \n    ['PICKED_UP', 'IN_TRANSIT', 'CUSTOMS', 'OUT_FOR_DELIVERY'].includes(s.status)\n  ).length\n  \n  // Calcul du temps moyen de livraison (en heures)\n  const deliveredShipments = shipments.filter(s => s.deliveryDate && s.pickupDate)\n  const averageDeliveryTime = deliveredShipments.length > 0\n    ? deliveredShipments.reduce((sum, s) => {\n        const diff = new Date(s.deliveryDate).getTime() - new Date(s.pickupDate).getTime()\n        return sum + (diff / (1000 * 60 * 60))\n      }, 0) / deliveredShipments.length\n    : 0\n  \n  // Calcul du taux de ponctualité\n  const onTimeDeliveries = deliveredShipments.filter(s => {\n    const status = getDeliveryStatus(new Date(s.estimatedDelivery), new Date(s.deliveryDate))\n    return status === 'ON_TIME' || status === 'EARLY'\n  }).length\n  \n  const onTimeRate = deliveredShipments.length > 0\n    ? (onTimeDeliveries / deliveredShipments.length) * 100\n    : 0\n  \n  // Coût total de transport\n  const totalTransportCosts = shipments.reduce((sum, s) => sum + (s.transportCost || 0), 0)\n  \n  return {\n    totalShipments: total,\n    inTransitShipments: inTransit,\n    deliveredShipments: delivered,\n    averageDeliveryTime: Math.round(averageDeliveryTime),\n    onTimeDeliveryRate: Math.round(onTimeRate),\n    totalTransportCosts\n  }\n}\n\n/**\n * Valide les données d'une expédition\n */\nexport function validateShipmentData(data: any): string[] {\n  const errors: string[] = []\n  \n  if (!data.orderId) errors.push('ID de commande requis')\n  if (!data.carrierId) errors.push('Transporteur requis')\n  if (!data.weight || data.weight <= 0) errors.push('Poids valide requis')\n  if (!data.transportMode) errors.push('Mode de transport requis')\n  if (!data.originCity) errors.push('Ville d\\'origine requise')\n  \n  return errors\n}\n\n/**\n * Formate une durée en heures vers un texte lisible\n */\nexport function formatDuration(hours: number): string {\n  if (hours < 24) {\n    return `${Math.round(hours)}h`\n  } else {\n    const days = Math.floor(hours / 24)\n    const remainingHours = Math.round(hours % 24)\n    return remainingHours > 0 ? `${days}j ${remainingHours}h` : `${days}j`\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAKO,SAAS,wBACd,MAAc,EACd,IAAmB,EACnB,UAAwB,EACxB,gBAAwB,CAAC;IAEzB,mDAAmD;IACnD,MAAM,YAAY;QAChB,MAAM;YACJ,OAAO;YACP,OAAO;YACP,MAAM,IAAQ,6BAA6B;QAC7C;QACA,aAAa;YACX,OAAO;YACP,OAAO;YACP,MAAM,KAAQ,8BAA8B;QAC9C;IACF;IAEA,MAAM,WAAW,SAAS,CAAC,KAAK,CAAC,WAAW;IAC5C,MAAM,WAAW,SAAS;IAC1B,MAAM,WAAW,WAAW,CAAC,gBAAgB,GAAG;IAEhD,OAAO,WAAW;AACpB;AAKO,SAAS,sBACd,IAAmB,EACnB,UAAwB,EACxB,YAAkB,IAAI,MAAM;IAE5B,uDAAuD;IACvD,MAAM,gBAAgB;QACpB,MAAM;YACJ,OAAO;YACP,OAAO;YACP,MAAM,IAAQ,UAAU;QAC1B;QACA,aAAa;YACX,OAAO;YACP,OAAO;YACP,MAAM,GAAQ,UAAU;QAC1B;IACF;IAEA,MAAM,aAAa,aAAa,CAAC,KAAK,CAAC,WAAW;IAClD,MAAM,oBAAoB,IAAI,KAAK;IACnC,kBAAkB,QAAQ,CAAC,kBAAkB,QAAQ,KAAK;IAE1D,OAAO;AACT;AAKO,SAAS,uBACd,IAAmB,EACnB,UAAwB;IAExB,MAAM,SAAS,SAAS,SAAS,OAAO;IACxC,MAAM,WAAW,WAAW,SAAS,CAAC,GAAG;IACzC,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW;IAErE,OAAO,GAAG,SAAS,WAAW,YAAY,QAAQ;AACpD;AAKO,SAAS,kBACd,iBAAuB,EACvB,cAAqB;IAErB,IAAI,CAAC,gBAAgB,OAAO;IAE5B,MAAM,WAAW,eAAe,OAAO,KAAK,kBAAkB,OAAO;IACrE,MAAM,YAAY,WAAW,CAAC,OAAO,KAAK,EAAE;IAE5C,IAAI,aAAa,GAAG,OAAO;IAC3B,IAAI,aAAa,IAAI,OAAO,UAAU,mBAAmB;;IACzD,OAAO;AACT;AAKO,SAAS,oBACd,eAAuB,EACvB,gBAAwB;IAExB,IAAI,oBAAoB,GAAG,OAAO;IAClC,OAAO,KAAK,KAAK,CAAC,AAAC,mBAAmB,kBAAmB;AAC3D;AAKO,SAAS,uBAAuB,MAAsB;IAC3D,MAAM,SAAS;QACb,SAAS;QACT,WAAW;QACX,YAAY;QACZ,SAAS;QACT,kBAAkB;QAClB,WAAW;QACX,SAAS;QACT,WAAW;IACb;IAEA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAKO,SAAS,uBAAuB,MAAsB;IAC3D,MAAM,SAAS;QACb,SAAS;QACT,WAAW;QACX,YAAY;QACZ,SAAS;QACT,kBAAkB;QAClB,WAAW;QACX,SAAS;QACT,WAAW;IACb;IAEA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAKO,SAAS,uBAAuB,SAAgB;IACrD,MAAM,QAAQ,UAAU,MAAM;IAC9B,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;IACxE,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,IACjC;YAAC;YAAa;YAAc;YAAW;SAAmB,CAAC,QAAQ,CAAC,EAAE,MAAM,GAC5E,MAAM;IAER,iDAAiD;IACjD,MAAM,qBAAqB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,IAAI,EAAE,UAAU;IAC/E,MAAM,sBAAsB,mBAAmB,MAAM,GAAG,IACpD,mBAAmB,MAAM,CAAC,CAAC,KAAK;QAC9B,MAAM,OAAO,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;QAChF,OAAO,MAAO,OAAO,CAAC,OAAO,KAAK,EAAE;IACtC,GAAG,KAAK,mBAAmB,MAAM,GACjC;IAEJ,gCAAgC;IAChC,MAAM,mBAAmB,mBAAmB,MAAM,CAAC,CAAA;QACjD,MAAM,SAAS,kBAAkB,IAAI,KAAK,EAAE,iBAAiB,GAAG,IAAI,KAAK,EAAE,YAAY;QACvF,OAAO,WAAW,aAAa,WAAW;IAC5C,GAAG,MAAM;IAET,MAAM,aAAa,mBAAmB,MAAM,GAAG,IAC3C,AAAC,mBAAmB,mBAAmB,MAAM,GAAI,MACjD;IAEJ,0BAA0B;IAC1B,MAAM,sBAAsB,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,aAAa,IAAI,CAAC,GAAG;IAEvF,OAAO;QACL,gBAAgB;QAChB,oBAAoB;QACpB,oBAAoB;QACpB,qBAAqB,KAAK,KAAK,CAAC;QAChC,oBAAoB,KAAK,KAAK,CAAC;QAC/B;IACF;AACF;AAKO,SAAS,qBAAqB,IAAS;IAC5C,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,KAAK,OAAO,EAAE,OAAO,IAAI,CAAC;IAC/B,IAAI,CAAC,KAAK,SAAS,EAAE,OAAO,IAAI,CAAC;IACjC,IAAI,CAAC,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO,IAAI,CAAC;IAClD,IAAI,CAAC,KAAK,aAAa,EAAE,OAAO,IAAI,CAAC;IACrC,IAAI,CAAC,KAAK,UAAU,EAAE,OAAO,IAAI,CAAC;IAElC,OAAO;AACT;AAKO,SAAS,eAAe,KAAa;IAC1C,IAAI,QAAQ,IAAI;QACd,OAAO,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC;IAChC,OAAO;QACL,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ;QAChC,MAAM,iBAAiB,KAAK,KAAK,CAAC,QAAQ;QAC1C,OAAO,iBAAiB,IAAI,GAAG,KAAK,EAAE,EAAE,eAAe,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;IACxE;AACF", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/ShipmentStatusBadge.tsx"], "sourcesContent": ["import { getShipmentStatusColor, getShipmentStatusLabel } from '@/lib/logistics'\nimport { ShipmentStatus } from '@/types'\n\ninterface ShipmentStatusBadgeProps {\n  status: ShipmentStatus\n}\n\nexport function ShipmentStatusBadge({ status }: ShipmentStatusBadgeProps) {\n  const colorClass = getShipmentStatusColor(status)\n  const label = getShipmentStatusLabel(status)\n\n  return (\n    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>\n      {label}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOO,SAAS,oBAAoB,EAAE,MAAM,EAA4B;IACtE,MAAM,aAAa,CAAA,GAAA,uHAAA,CAAA,yBAAsB,AAAD,EAAE;IAC1C,MAAM,QAAQ,CAAA,GAAA,uHAAA,CAAA,yBAAsB,AAAD,EAAE;IAErC,qBACE,8OAAC;QAAK,WAAW,CAAC,wEAAwE,EAAE,YAAY;kBACrG;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/TrackingButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Search, X } from 'lucide-react'\nimport { getShipmentByTracking } from '@/lib/actions/shipments'\nimport { ShipmentStatusBadge } from './ShipmentStatusBadge'\n\ninterface TrackingButtonProps {\n  trackingNumber: string\n}\n\nexport function TrackingButton({ trackingNumber }: TrackingButtonProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const [shipmentData, setShipmentData] = useState<any>(null)\n  const [loading, setLoading] = useState(false)\n\n  const handleTrack = async () => {\n    if (!isOpen) {\n      setIsOpen(true)\n      setLoading(true)\n      \n      try {\n        const data = await getShipmentByTracking(trackingNumber)\n        setShipmentData(data)\n      } catch (error) {\n        console.error('Erreur lors du suivi:', error)\n      } finally {\n        setLoading(false)\n      }\n    } else {\n      setIsOpen(false)\n      setShipmentData(null)\n    }\n  }\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={handleTrack}\n        className=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n      >\n        {isOpen ? (\n          <>\n            <X className=\"h-3 w-3 mr-1\" />\n            Fermer\n          </>\n        ) : (\n          <>\n            <Search className=\"h-3 w-3 mr-1\" />\n            Suivre\n          </>\n        )}\n      </button>\n\n      {isOpen && (\n        <div className=\"absolute right-0 top-8 w-96 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-10\">\n          <div className=\"flex items-center justify-between mb-3\">\n            <h4 className=\"text-sm font-medium text-gray-900\">\n              Suivi: {trackingNumber}\n            </h4>\n            <button\n              onClick={() => setIsOpen(false)}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <X className=\"h-4 w-4\" />\n            </button>\n          </div>\n\n          {loading ? (\n            <div className=\"text-center py-4\">\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto\"></div>\n              <p className=\"text-sm text-gray-500 mt-2\">Chargement...</p>\n            </div>\n          ) : shipmentData ? (\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">Statut:</span>\n                <ShipmentStatusBadge status={shipmentData.status} />\n              </div>\n              \n              <div className=\"border-t pt-3\">\n                <h5 className=\"text-xs font-medium text-gray-900 mb-2\">\n                  Événements de suivi\n                </h5>\n                <div className=\"space-y-2 max-h-40 overflow-y-auto\">\n                  {shipmentData.trackingEvents.map((event: any, index: number) => (\n                    <div key={event.id} className=\"text-xs\">\n                      <div className=\"flex items-start space-x-2\">\n                        <div className={`w-2 h-2 rounded-full mt-1 ${\n                          index === 0 ? 'bg-blue-500' : 'bg-gray-300'\n                        }`} />\n                        <div className=\"flex-1\">\n                          <p className=\"font-medium text-gray-900\">\n                            {event.description}\n                          </p>\n                          <p className=\"text-gray-500\">\n                            {new Date(event.timestamp).toLocaleString('fr-FR')}\n                          </p>\n                          <p className=\"text-gray-500\">{event.location}</p>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center py-4\">\n              <p className=\"text-sm text-gray-500\">\n                Aucune information de suivi disponible\n              </p>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAWO,SAAS,eAAe,EAAE,cAAc,EAAuB;IACpE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,cAAc;QAClB,IAAI,CAAC,QAAQ;YACX,UAAU;YACV,WAAW;YAEX,IAAI;gBACF,MAAM,OAAO,MAAM,CAAA,GAAA,6JAAA,CAAA,wBAAqB,AAAD,EAAE;gBACzC,gBAAgB;YAClB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;YACzC,SAAU;gBACR,WAAW;YACb;QACF,OAAO;YACL,UAAU;YACV,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,SAAS;gBACT,WAAU;0BAET,uBACC;;sCACE,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;wBAAiB;;iDAIhC;;sCACE,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;YAMxC,wBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAoC;oCACxC;;;;;;;0CAEV,8OAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAIhB,wBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;+BAE1C,6BACF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC,sJAAA,CAAA,sBAAmB;wCAAC,QAAQ,aAAa,MAAM;;;;;;;;;;;;0CAGlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAI,WAAU;kDACZ,aAAa,cAAc,CAAC,GAAG,CAAC,CAAC,OAAY,sBAC5C,8OAAC;gDAAmB,WAAU;0DAC5B,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,0BAA0B,EACzC,UAAU,IAAI,gBAAgB,eAC9B;;;;;;sEACF,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EACV,MAAM,WAAW;;;;;;8EAEpB,8OAAC;oEAAE,WAAU;8EACV,IAAI,KAAK,MAAM,SAAS,EAAE,cAAc,CAAC;;;;;;8EAE5C,8OAAC;oEAAE,WAAU;8EAAiB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;+CAZxC,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;6CAqB1B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}, {"offset": {"line": 474, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/AddCarrierButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Plus, Truck, X } from 'lucide-react'\n\nexport function AddCarrierButton() {\n  const [isOpen, setIsOpen] = useState(false)\n\n  return (\n    <>\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n      >\n        <Plus className=\"h-4 w-4 mr-2\" />\n        Ajouter transporteur\n      </button>\n\n      {isOpen && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Truck className=\"h-5 w-5 text-blue-600\" />\n                <h3 className=\"text-lg font-medium text-gray-900\">\n                  Ajouter un transporteur\n                </h3>\n              </div>\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n\n            <div className=\"text-center py-8\">\n              <Truck className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-600 mb-4\">\n                Fonctionnalité en cours de développement\n              </p>\n              <p className=\"text-sm text-gray-500\">\n                Le formulaire d'ajout de transporteur sera bientôt disponible.\n              </p>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Fermer\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE;;0BACE,8OAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAU;;kCAEV,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;YAIlC,wBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;;8CAIpD,8OAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/LogisticsFilters.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Filter, X } from 'lucide-react'\nimport { ShipmentStatus, TransportMode } from '@/types'\n\nconst statusOptions = [\n  { value: 'PENDING', label: 'En attente' },\n  { value: 'PICKED_UP', label: 'Enlevée' },\n  { value: 'IN_TRANSIT', label: 'En transit' },\n  { value: 'CUSTOMS', label: 'En douane' },\n  { value: 'OUT_FOR_DELIVERY', label: 'En livraison' },\n  { value: 'DELIVERED', label: 'Livrée' },\n  { value: 'DELAYED', label: 'Retardée' },\n  { value: 'CANCELLED', label: 'Annulée' }\n]\n\nconst transportModeOptions = [\n  { value: 'ROAD', label: 'Routier' },\n  { value: 'AIR_EXPRESS', label: 'Aérien Express' }\n]\n\nexport function LogisticsFilters() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [filters, setFilters] = useState({\n    status: '',\n    transportMode: '',\n    dateFrom: '',\n    dateTo: '',\n    search: ''\n  })\n\n  const activeFiltersCount = Object.values(filters).filter(Boolean).length\n\n  const handleFilterChange = (key: string, value: string) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value\n    }))\n  }\n\n  const clearFilters = () => {\n    setFilters({\n      status: '',\n      transportMode: '',\n      dateFrom: '',\n      dateTo: '',\n      search: ''\n    })\n  }\n\n  const clearFilter = (key: string) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: ''\n    }))\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center space-x-2\">\n          <Filter className=\"h-5 w-5 text-gray-400\" />\n          <h3 className=\"text-sm font-medium text-gray-900\">Filtres</h3>\n          {activeFiltersCount > 0 && (\n            <span className=\"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n              {activeFiltersCount}\n            </span>\n          )}\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          {activeFiltersCount > 0 && (\n            <button\n              onClick={clearFilters}\n              className=\"text-sm text-gray-500 hover:text-gray-700\"\n            >\n              Effacer tout\n            </button>\n          )}\n          <button\n            onClick={() => setIsOpen(!isOpen)}\n            className=\"text-sm text-blue-600 hover:text-blue-700\"\n          >\n            {isOpen ? 'Masquer' : 'Afficher'}\n          </button>\n        </div>\n      </div>\n\n      {/* Filtres actifs */}\n      {activeFiltersCount > 0 && (\n        <div className=\"flex flex-wrap gap-2 mb-4\">\n          {filters.status && (\n            <div className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800\">\n              Statut: {statusOptions.find(s => s.value === filters.status)?.label}\n              <button\n                onClick={() => clearFilter('status')}\n                className=\"ml-2 text-gray-500 hover:text-gray-700\"\n              >\n                <X className=\"h-3 w-3\" />\n              </button>\n            </div>\n          )}\n          {filters.transportMode && (\n            <div className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800\">\n              Mode: {transportModeOptions.find(t => t.value === filters.transportMode)?.label}\n              <button\n                onClick={() => clearFilter('transportMode')}\n                className=\"ml-2 text-gray-500 hover:text-gray-700\"\n              >\n                <X className=\"h-3 w-3\" />\n              </button>\n            </div>\n          )}\n          {filters.search && (\n            <div className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800\">\n              Recherche: {filters.search}\n              <button\n                onClick={() => clearFilter('search')}\n                className=\"ml-2 text-gray-500 hover:text-gray-700\"\n              >\n                <X className=\"h-3 w-3\" />\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Formulaire de filtres */}\n      {isOpen && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Recherche\n            </label>\n            <input\n              type=\"text\"\n              value={filters.search}\n              onChange={(e) => handleFilterChange('search', e.target.value)}\n              placeholder=\"Numéro de suivi, client...\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Statut\n            </label>\n            <select\n              value={filters.status}\n              onChange={(e) => handleFilterChange('status', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm\"\n            >\n              <option value=\"\">Tous les statuts</option>\n              {statusOptions.map(option => (\n                <option key={option.value} value={option.value}>\n                  {option.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Mode de transport\n            </label>\n            <select\n              value={filters.transportMode}\n              onChange={(e) => handleFilterChange('transportMode', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm\"\n            >\n              <option value=\"\">Tous les modes</option>\n              {transportModeOptions.map(option => (\n                <option key={option.value} value={option.value}>\n                  {option.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Date de début\n            </label>\n            <input\n              type=\"date\"\n              value={filters.dateFrom}\n              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Date de fin\n            </label>\n            <input\n              type=\"date\"\n              value={filters.dateTo}\n              onChange={(e) => handleFilterChange('dateTo', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm\"\n            />\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAMA,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAW,OAAO;IAAa;IACxC;QAAE,OAAO;QAAa,OAAO;IAAU;IACvC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAW,OAAO;IAAY;IACvC;QAAE,OAAO;QAAoB,OAAO;IAAe;IACnD;QAAE,OAAO;QAAa,OAAO;IAAS;IACtC;QAAE,OAAO;QAAW,OAAO;IAAW;IACtC;QAAE,OAAO;QAAa,OAAO;IAAU;CACxC;AAED,MAAM,uBAAuB;IAC3B;QAAE,OAAO;QAAQ,OAAO;IAAU;IAClC;QAAE,OAAO;QAAe,OAAO;IAAiB;CACjD;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,QAAQ;QACR,eAAe;QACf,UAAU;QACV,QAAQ;QACR,QAAQ;IACV;IAEA,MAAM,qBAAqB,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC,SAAS,MAAM;IAExE,MAAM,qBAAqB,CAAC,KAAa;QACvC,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;YACT,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,WAAW;YACT,QAAQ;YACR,eAAe;YACf,UAAU;YACV,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;YACT,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;4BACjD,qBAAqB,mBACpB,8OAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;kCAIP,8OAAC;wBAAI,WAAU;;4BACZ,qBAAqB,mBACpB,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAIH,8OAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,SAAS,YAAY;;;;;;;;;;;;;;;;;;YAM3B,qBAAqB,mBACpB,8OAAC;gBAAI,WAAU;;oBACZ,QAAQ,MAAM,kBACb,8OAAC;wBAAI,WAAU;;4BAAoF;4BACxF,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,QAAQ,MAAM,GAAG;0CAC9D,8OAAC;gCACC,SAAS,IAAM,YAAY;gCAC3B,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAIlB,QAAQ,aAAa,kBACpB,8OAAC;wBAAI,WAAU;;4BAAoF;4BAC1F,qBAAqB,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,QAAQ,aAAa,GAAG;0CAC1E,8OAAC;gCACC,SAAS,IAAM,YAAY;gCAC3B,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAIlB,QAAQ,MAAM,kBACb,8OAAC;wBAAI,WAAU;;4BAAoF;4BACrF,QAAQ,MAAM;0CAC1B,8OAAC;gCACC,SAAS,IAAM,YAAY;gCAC3B,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAQtB,wBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,QAAQ,MAAM;gCACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;gCAC5D,aAAY;gCACZ,WAAU;;;;;;;;;;;;kCAId,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,OAAO,QAAQ,MAAM;gCACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;gCAC5D,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,cAAc,GAAG,CAAC,CAAA,uBACjB,8OAAC;4CAA0B,OAAO,OAAO,KAAK;sDAC3C,OAAO,KAAK;2CADF,OAAO,KAAK;;;;;;;;;;;;;;;;;kCAO/B,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,OAAO,QAAQ,aAAa;gCAC5B,UAAU,CAAC,IAAM,mBAAmB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCACnE,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,qBAAqB,GAAG,CAAC,CAAA,uBACxB,8OAAC;4CAA0B,OAAO,OAAO,KAAK;sDAC3C,OAAO,KAAK;2CADF,OAAO,KAAK;;;;;;;;;;;;;;;;;kCAO/B,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,QAAQ,QAAQ;gCACvB,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC9D,WAAU;;;;;;;;;;;;kCAId,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,QAAQ,MAAM;gCACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;gCAC5D,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAOxB", "debugId": null}}, {"offset": {"line": 1060, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/CreateShipmentButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Plus, Package, X } from 'lucide-react'\n\nexport function CreateShipmentButton() {\n  const [isOpen, setIsOpen] = useState(false)\n\n  return (\n    <>\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n      >\n        <Plus className=\"h-4 w-4 mr-2\" />\n        Nouvelle expédition\n      </button>\n\n      {isOpen && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Package className=\"h-5 w-5 text-blue-600\" />\n                <h3 className=\"text-lg font-medium text-gray-900\">\n                  Créer une expédition\n                </h3>\n              </div>\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n\n            <div className=\"text-center py-8\">\n              <Package className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-600 mb-4\">\n                Fonctionnalité en cours de développement\n              </p>\n              <p className=\"text-sm text-gray-500\">\n                Le formulaire de création d'expédition sera bientôt disponible.\n              </p>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                Fermer\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE;;0BACE,8OAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAU;;kCAEV,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;YAIlC,wBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;;8CAIpD,8OAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 1215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/tabs.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport * as TabsPrimitive from '@radix-ui/react-tabs'\nimport { cn } from '@/lib/utils'\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      'inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground',\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm',\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      'mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}]}