{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: 'NGN' | 'XOF' = 'XOF'): string {\n  const symbols = {\n    NGN: '₦',\n    XOF: 'CFA'\n  }\n  \n  return `${amount.toLocaleString('fr-FR')} ${symbols[currency]}`\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric'\n  }).format(date)\n}\n\nexport function formatDateTime(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  }).format(date)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAA0B,KAAK;IAC5E,MAAM,UAAU;QACd,KAAK;QACL,KAAK;IACP;IAEA,OAAO,GAAG,OAAO,cAAc,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,SAAS,EAAE;AACjE;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;IACR,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAU;IACvC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/badge-component.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {\n  children: React.ReactNode\n  className?: string\n  variant?: 'default' | 'secondary' | 'destructive' | 'outline'\n}\n\nexport function Badge({ children, className, variant = 'default', ...props }: BadgeProps) {\n  const variantClasses = {\n    default: 'bg-blue-100 text-blue-800 border-blue-200',\n    secondary: 'bg-gray-100 text-gray-800 border-gray-200',\n    destructive: 'bg-red-100 text-red-800 border-red-200',\n    outline: 'bg-transparent text-gray-700 border-gray-300'\n  }\n\n  return (\n    <span \n      className={cn(\n        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border',\n        variantClasses[variant],\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAmB;IACtF,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,aAAa;QACb,SAAS;IACX;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kFACA,cAAc,CAAC,QAAQ,EACvB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/suppliers/SuppliersList.tsx"], "sourcesContent": ["import { prisma } from '@/lib/prisma'\nimport { Badge } from '@/components/ui/badge-component'\nimport { Star, Phone, Mail, MapPin, Package } from 'lucide-react'\n\nasync function getSuppliers() {\n  return await prisma.supplier.findMany({\n    where: { isActive: true },\n    include: {\n      products: {\n        where: { isActive: true }\n      },\n      orders: {\n        take: 5,\n        orderBy: { createdAt: 'desc' }\n      }\n    },\n    orderBy: { rating: 'desc' }\n  })\n}\n\nconst cityColors = {\n  LAGOS: 'bg-blue-100 text-blue-800',\n  ABUJA: 'bg-green-100 text-green-800',\n  KANO: 'bg-purple-100 text-purple-800'\n}\n\nconst cityLabels = {\n  LAGOS: 'Lagos',\n  ABUJA: 'Abuja',\n  KANO: 'Kano'\n}\n\nfunction StarRating({ rating }: { rating: number }) {\n  return (\n    <div className=\"flex items-center space-x-1\">\n      {[1, 2, 3, 4, 5].map((star) => (\n        <Star\n          key={star}\n          className={`h-4 w-4 ${\n            star <= rating\n              ? 'text-yellow-400 fill-current'\n              : 'text-gray-300'\n          }`}\n        />\n      ))}\n      <span className=\"text-sm text-gray-600 ml-1\">({rating.toFixed(1)})</span>\n    </div>\n  )\n}\n\nexport async function SuppliersList() {\n  const suppliers = await getSuppliers()\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6\">\n        {suppliers.map((supplier) => (\n          <div key={supplier.id} className=\"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow\">\n            {/* Header */}\n            <div className=\"flex items-start justify-between mb-4\">\n              <div className=\"flex-1\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                  {supplier.name}\n                </h3>\n                <Badge className={cityColors[supplier.city]}>\n                  {cityLabels[supplier.city]}\n                </Badge>\n              </div>\n              <div className=\"text-right\">\n                <StarRating rating={supplier.rating} />\n              </div>\n            </div>\n\n            {/* Contact Info */}\n            <div className=\"space-y-2 mb-4\">\n              <div className=\"flex items-center text-sm text-gray-600\">\n                <Phone className=\"h-4 w-4 mr-2\" />\n                {supplier.phone}\n              </div>\n              {supplier.email && (\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <Mail className=\"h-4 w-4 mr-2\" />\n                  {supplier.email}\n                </div>\n              )}\n              <div className=\"flex items-center text-sm text-gray-600\">\n                <MapPin className=\"h-4 w-4 mr-2\" />\n                {supplier.address}\n              </div>\n            </div>\n\n            {/* Specialties */}\n            <div className=\"mb-4\">\n              <p className=\"text-sm font-medium text-gray-700 mb-2\">Spécialités:</p>\n              <p className=\"text-sm text-gray-600\">{supplier.specialties}</p>\n            </div>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-2 gap-4 pt-4 border-t border-gray-200\">\n              <div className=\"text-center\">\n                <div className=\"flex items-center justify-center mb-1\">\n                  <Package className=\"h-4 w-4 text-gray-400 mr-1\" />\n                </div>\n                <p className=\"text-lg font-semibold text-gray-900\">\n                  {supplier.products.length}\n                </p>\n                <p className=\"text-xs text-gray-500\">Produits</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"flex items-center justify-center mb-1\">\n                  <Package className=\"h-4 w-4 text-gray-400 mr-1\" />\n                </div>\n                <p className=\"text-lg font-semibold text-gray-900\">\n                  {supplier.orders.length}\n                </p>\n                <p className=\"text-xs text-gray-500\">Commandes</p>\n              </div>\n            </div>\n\n            {/* Actions */}\n            <div className=\"mt-4 flex space-x-2\">\n              <button className=\"flex-1 bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors\">\n                Voir détails\n              </button>\n              <button className=\"px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors\">\n                Modifier\n              </button>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {suppliers.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Package className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n          <p className=\"text-gray-500 text-lg\">Aucun fournisseur trouvé</p>\n          <p className=\"text-gray-400 text-sm mt-1\">\n            Commencez par ajouter votre premier fournisseur\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;AAEA,eAAe;IACb,OAAO,MAAM,oHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;QACpC,OAAO;YAAE,UAAU;QAAK;QACxB,SAAS;YACP,UAAU;gBACR,OAAO;oBAAE,UAAU;gBAAK;YAC1B;YACA,QAAQ;gBACN,MAAM;gBACN,SAAS;oBAAE,WAAW;gBAAO;YAC/B;QACF;QACA,SAAS;YAAE,QAAQ;QAAO;IAC5B;AACF;AAEA,MAAM,aAAa;IACjB,OAAO;IACP,OAAO;IACP,MAAM;AACR;AAEA,MAAM,aAAa;IACjB,OAAO;IACP,OAAO;IACP,MAAM;AACR;AAEA,SAAS,WAAW,EAAE,MAAM,EAAsB;IAChD,qBACE,8OAAC;QAAI,WAAU;;YACZ;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,8OAAC,kMAAA,CAAA,OAAI;oBAEH,WAAW,CAAC,QAAQ,EAClB,QAAQ,SACJ,iCACA,iBACJ;mBALG;;;;;0BAQT,8OAAC;gBAAK,WAAU;;oBAA6B;oBAAE,OAAO,OAAO,CAAC;oBAAG;;;;;;;;;;;;;AAGvE;AAEO,eAAe;IACpB,MAAM,YAAY,MAAM;IAExB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;wBAAsB,WAAU;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,SAAS,IAAI;;;;;;0DAEhB,8OAAC,8IAAA,CAAA,QAAK;gDAAC,WAAW,UAAU,CAAC,SAAS,IAAI,CAAC;0DACxC,UAAU,CAAC,SAAS,IAAI,CAAC;;;;;;;;;;;;kDAG9B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAW,QAAQ,SAAS,MAAM;;;;;;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,SAAS,KAAK;;;;;;;oCAEhB,SAAS,KAAK,kBACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,SAAS,KAAK;;;;;;;kDAGnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CACjB,SAAS,OAAO;;;;;;;;;;;;;0CAKrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAyC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAyB,SAAS,WAAW;;;;;;;;;;;;0CAI5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,8OAAC;gDAAE,WAAU;0DACV,SAAS,QAAQ,CAAC,MAAM;;;;;;0DAE3B,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,8OAAC;gDAAE,WAAU;0DACV,SAAS,MAAM,CAAC,MAAM;;;;;;0DAEzB,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAKzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAA6G;;;;;;kDAG/H,8OAAC;wCAAO,WAAU;kDAAmH;;;;;;;;;;;;;uBAnE/H,SAAS,EAAE;;;;;;;;;;YA2ExB,UAAU,MAAM,KAAK,mBACpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wMAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/suppliers/SupplierFilters.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SupplierFilters = registerClientReference(\n    function() { throw new Error(\"Attempted to call SupplierFilters() from the server but SupplierFilters is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/suppliers/SupplierFilters.tsx <module evaluation>\",\n    \"SupplierFilters\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8EACA", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/suppliers/SupplierFilters.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SupplierFilters = registerClientReference(\n    function() { throw new Error(\"Attempted to call SupplierFilters() from the server but SupplierFilters is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/suppliers/SupplierFilters.tsx\",\n    \"SupplierFilters\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA", "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/suppliers/AddSupplierButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AddSupplierButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call AddSupplierButton() from the server but AddSupplierButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/suppliers/AddSupplierButton.tsx <module evaluation>\",\n    \"AddSupplierButton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,gFACA", "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/suppliers/AddSupplierButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AddSupplierButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call AddSupplierButton() from the server but AddSupplierButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/suppliers/AddSupplierButton.tsx\",\n    \"AddSupplierButton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,4DACA", "debugId": null}}, {"offset": {"line": 579, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 589, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/suppliers/page.tsx"], "sourcesContent": ["import { Suspense } from 'react'\nimport { SuppliersList } from '@/components/suppliers/SuppliersList'\nimport { SupplierFilters } from '@/components/suppliers/SupplierFilters'\nimport { AddSupplierButton } from '@/components/suppliers/AddSupplierButton'\nimport { Plus } from 'lucide-react'\n\nexport default function SuppliersPage() {\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between border-b border-gray-200 pb-4\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Fournisseurs</h1>\n          <p className=\"text-gray-600 mt-1\">\n            Gérez vos fournisseurs au Nigeria (Lagos, Abuja, Kano)\n          </p>\n        </div>\n        <AddSupplierButton />\n      </div>\n\n      {/* Filters */}\n      <Suspense fallback={<div className=\"animate-pulse h-16 bg-gray-200 rounded-lg\" />}>\n        <SupplierFilters />\n      </Suspense>\n\n      {/* Suppliers List */}\n      <Suspense fallback={<div className=\"animate-pulse h-96 bg-gray-200 rounded-lg\" />}>\n        <SuppliersList />\n      </Suspense>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAGe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,8OAAC,oJAAA,CAAA,oBAAiB;;;;;;;;;;;0BAIpB,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;oBAAI,WAAU;;;;;;0BACjC,cAAA,8OAAC,kJAAA,CAAA,kBAAe;;;;;;;;;;0BAIlB,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;oBAAI,WAAU;;;;;;0BACjC,cAAA,8OAAC,gJAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;AAItB", "debugId": null}}]}