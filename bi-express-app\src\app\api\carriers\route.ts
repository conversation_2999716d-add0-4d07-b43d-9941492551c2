import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const transportMode = searchParams.get('transportMode')
    const city = searchParams.get('city')
    const search = searchParams.get('search')

    const where: any = {}

    if (transportMode) {
      where.transportModes = {
        has: transportMode
      }
    }

    if (city) {
      where.city = city
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } }
      ]
    }

    const carriers = await prisma.carrier.findMany({
      where,
      include: {
        shipments: {
          select: {
            id: true,
            status: true,
            totalTransportCost: true,
            createdAt: true
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 5
        },
        _count: {
          select: {
            shipments: true
          }
        }
      },
      orderBy: {
        rating: 'desc'
      }
    })

    return NextResponse.json(carriers)
  } catch (error) {
    console.error('Erreur lors de la récupération des transporteurs:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la récupération des transporteurs' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const {
      name,
      email,
      phone,
      address,
      city,
      country,
      transportModes,
      vehicleTypes,
      capacity,
      pricePerKg,
      rating,
      isActive,
      notes
    } = body

    // Validation des champs obligatoires
    if (!name || !phone || !city || !transportModes || transportModes.length === 0) {
      return NextResponse.json(
        { error: 'Les champs nom, téléphone, ville et modes de transport sont obligatoires' },
        { status: 400 }
      )
    }

    // Vérifier l'unicité de l'email s'il est fourni
    if (email) {
      const existingEmail = await prisma.carrier.findFirst({
        where: { email }
      })

      if (existingEmail) {
        return NextResponse.json(
          { error: 'Cette adresse email est déjà utilisée' },
          { status: 400 }
        )
      }
    }

    // Vérifier l'unicité du téléphone
    const existingPhone = await prisma.carrier.findFirst({
      where: { phone }
    })

    if (existingPhone) {
      return NextResponse.json(
        { error: 'Ce numéro de téléphone est déjà utilisé' },
        { status: 400 }
      )
    }

    const carrier = await prisma.carrier.create({
      data: {
        name,
        email: email || null,
        phone,
        address: address || '',
        city,
        transportModes,
        capacity: capacity ? parseFloat(capacity) : null,
        rating: rating ? parseFloat(rating) : 5.0,
        isActive: isActive !== false
      }
    })

    return NextResponse.json(carrier, { status: 201 })
  } catch (error) {
    console.error('Erreur lors de la création du transporteur:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la création du transporteur' },
      { status: 500 }
    )
  }
}
