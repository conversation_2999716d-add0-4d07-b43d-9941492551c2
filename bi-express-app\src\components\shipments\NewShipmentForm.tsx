'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge-component'
import { Truck, Plane, Package, MapPin, Calendar } from 'lucide-react'

interface Order {
  id: string
  orderNumber: string
  customer: {
    name: string
    city: string
    type: 'LOGISTICS' | 'COMMERCE'
  }
  totalAmount: number
  status: string
  createdAt: string
}

interface Carrier {
  id: string
  name: string
  transportModes: string[]
  city: string
  rating: number
}

interface ShipmentFormData {
  carrierId: string
  transportMode: 'TRUCK' | 'AIR'
  orderIds: string[]
  pickupDate: string
  estimatedDelivery: string
  pickupAddress: string
  deliveryAddress: string
  notes: string
  priority: 'LOW' | 'MEDIUM' | 'HIGH'
}

export function NewShipmentForm() {
  const router = useRouter()
  const [orders, setOrders] = useState<Order[]>([])
  const [carriers, setCarriers] = useState<Carrier[]>([])
  const [selectedOrders, setSelectedOrders] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState<ShipmentFormData>({
    carrierId: '',
    transportMode: 'TRUCK',
    orderIds: [],
    pickupDate: '',
    estimatedDelivery: '',
    pickupAddress: '',
    deliveryAddress: 'Dakar, Sénégal',
    notes: '',
    priority: 'MEDIUM'
  })

  // Charger les commandes en attente et les transporteurs
  useEffect(() => {
    const loadData = async () => {
      try {
        const [ordersRes, carriersRes] = await Promise.all([
          fetch('/api/orders?status=CONFIRMED'),
          fetch('/api/carriers')
        ])
        
        if (ordersRes.ok) {
          const ordersData = await ordersRes.json()
          setOrders(ordersData)
        }
        
        if (carriersRes.ok) {
          const carriersData = await carriersRes.json()
          setCarriers(carriersData)
        }
      } catch (error) {
        console.error('Erreur lors du chargement des données:', error)
      }
    }

    loadData()
  }, [])

  // Mettre à jour les commandes sélectionnées dans le formulaire
  useEffect(() => {
    setFormData(prev => ({ ...prev, orderIds: selectedOrders }))
  }, [selectedOrders])

  const handleInputChange = (field: keyof ShipmentFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const toggleOrderSelection = (orderId: string) => {
    setSelectedOrders(prev =>
      prev.includes(orderId)
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    )
  }

  const calculateTotalWeight = () => {
    return selectedOrders.length * 25 // Estimation moyenne de 25kg par commande
  }

  const calculateEstimatedCost = () => {
    const baseRate = formData.transportMode === 'AIR' ? 150 : 75 // FCFA par kg
    return calculateTotalWeight() * baseRate
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.carrierId || selectedOrders.length === 0 || !formData.pickupDate) {
      alert('Veuillez remplir tous les champs obligatoires et sélectionner au moins une commande')
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/shipments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        router.push('/shipments')
      } else {
        alert('Erreur lors de la création de l\'expédition')
      }
    } catch (error) {
      console.error('Erreur:', error)
      alert('Erreur lors de la création de l\'expédition')
    } finally {
      setIsLoading(false)
    }
  }

  const filteredCarriers = carriers.filter(carrier =>
    carrier.transportModes.includes(formData.transportMode)
  )

  return (
    <form onSubmit={handleSubmit} className="max-w-6xl mx-auto">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Configuration de l'expédition */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Truck className="h-5 w-5" />
              Configuration de l'expédition
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Mode de transport *
                </label>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant={formData.transportMode === 'TRUCK' ? 'default' : 'outline'}
                    onClick={() => handleInputChange('transportMode', 'TRUCK')}
                    className="flex-1"
                  >
                    <Truck className="h-4 w-4 mr-2" />
                    Routier
                  </Button>
                  <Button
                    type="button"
                    variant={formData.transportMode === 'AIR' ? 'default' : 'outline'}
                    onClick={() => handleInputChange('transportMode', 'AIR')}
                    className="flex-1"
                  >
                    <Plane className="h-4 w-4 mr-2" />
                    Aérien
                  </Button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Transporteur *
                </label>
                <select
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={formData.carrierId}
                  onChange={(e) => handleInputChange('carrierId', e.target.value)}
                >
                  <option value="">Sélectionner un transporteur</option>
                  {filteredCarriers.map(carrier => (
                    <option key={carrier.id} value={carrier.id}>
                      {carrier.name} - {carrier.city} (★ {carrier.rating.toFixed(1)})
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date d'enlèvement *
                </label>
                <input
                  type="date"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={formData.pickupDate}
                  onChange={(e) => handleInputChange('pickupDate', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Livraison estimée
                </label>
                <input
                  type="date"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={formData.estimatedDelivery}
                  onChange={(e) => handleInputChange('estimatedDelivery', e.target.value)}
                  min={formData.pickupDate}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Adresse d'enlèvement
              </label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.pickupAddress}
                onChange={(e) => handleInputChange('pickupAddress', e.target.value)}
                placeholder="Adresse complète d'enlèvement"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Priorité
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.priority}
                onChange={(e) => handleInputChange('priority', e.target.value as 'LOW' | 'MEDIUM' | 'HIGH')}
              >
                <option value="LOW">Basse</option>
                <option value="MEDIUM">Moyenne</option>
                <option value="HIGH">Haute</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Instructions spéciales pour l'expédition..."
              />
            </div>
          </CardContent>
        </Card>

        {/* Résumé et estimation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Résumé
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="text-sm text-blue-700 mb-1">Commandes sélectionnées</div>
              <div className="text-lg font-bold text-blue-900">{selectedOrders.length}</div>
            </div>

            <div className="bg-green-50 p-3 rounded-lg">
              <div className="text-sm text-green-700 mb-1">Poids estimé</div>
              <div className="text-lg font-bold text-green-900">{calculateTotalWeight()} kg</div>
            </div>

            <div className="bg-purple-50 p-3 rounded-lg">
              <div className="text-sm text-purple-700 mb-1">Coût estimé</div>
              <div className="text-lg font-bold text-purple-900">
                {calculateEstimatedCost().toLocaleString()} FCFA
              </div>
            </div>

            {selectedOrders.length > 0 && (
              <Button
                type="submit"
                disabled={isLoading || !formData.carrierId || !formData.pickupDate}
                className="w-full"
              >
                {isLoading ? 'Création...' : 'Créer l\'expédition'}
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Sélection des commandes */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Commandes à expédier ({orders.length} disponibles)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
              {orders.map(order => (
                <div
                  key={order.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedOrders.includes(order.id)
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => toggleOrderSelection(order.id)}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div className="font-medium">{order.orderNumber}</div>
                    <Badge variant={order.customer.type === 'COMMERCE' ? 'default' : 'secondary'}>
                      {order.customer.type}
                    </Badge>
                  </div>
                  <div className="text-sm text-gray-600 mb-1">{order.customer.name}</div>
                  <div className="text-sm text-gray-600 mb-2">{order.customer.city}</div>
                  <div className="font-semibold text-blue-600">
                    {order.totalAmount.toLocaleString()} FCFA
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {new Date(order.createdAt).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Boutons d'action */}
      <div className="flex justify-end gap-4 mt-6">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
        >
          Annuler
        </Button>
      </div>
    </form>
  )
}
