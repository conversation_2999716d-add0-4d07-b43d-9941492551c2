{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'\n  size?: 'default' | 'sm' | 'lg' | 'icon'\n}\n\nexport function Button({ \n  className, \n  variant = 'default', \n  size = 'default', \n  ...props \n}: ButtonProps) {\n  const variantClasses = {\n    default: 'bg-blue-600 text-white hover:bg-blue-700',\n    destructive: 'bg-red-600 text-white hover:bg-red-700',\n    outline: 'border border-gray-300 bg-transparent hover:bg-gray-50',\n    secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200',\n    ghost: 'hover:bg-gray-100',\n    link: 'text-blue-600 underline-offset-4 hover:underline'\n  }\n\n  const sizeClasses = {\n    default: 'h-10 px-4 py-2',\n    sm: 'h-9 rounded-md px-3',\n    lg: 'h-11 rounded-md px-8',\n    icon: 'h-10 w-10'\n  }\n\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none',\n        variantClasses[variant],\n        sizeClasses[size],\n        className\n      )}\n      {...props}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,GAAG,OACS;IACZ,MAAM,iBAAiB;QACrB,SAAS;QACT,aAAa;QACb,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iPACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/badge-component.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {\n  children: React.ReactNode\n  className?: string\n  variant?: 'default' | 'secondary' | 'destructive' | 'outline'\n}\n\nexport function Badge({ children, className, variant = 'default', ...props }: BadgeProps) {\n  const variantClasses = {\n    default: 'bg-blue-100 text-blue-800 border-blue-200',\n    secondary: 'bg-gray-100 text-gray-800 border-gray-200',\n    destructive: 'bg-red-100 text-red-800 border-red-200',\n    outline: 'bg-transparent text-gray-700 border-gray-300'\n  }\n\n  return (\n    <span \n      className={cn(\n        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border',\n        variantClasses[variant],\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAmB;IACtF,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,aAAa;QACb,SAAS;IACX;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kFACA,cAAc,CAAC,QAAQ,EACvB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/progress.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {\n  value?: number\n  max?: number\n  className?: string\n}\n\nexport function Progress({ value = 0, max = 100, className, ...props }: ProgressProps) {\n  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)\n\n  return (\n    <div\n      className={cn(\n        \"relative h-4 w-full overflow-hidden rounded-full bg-gray-200\",\n        className\n      )}\n      {...props}\n    >\n      <div\n        className=\"h-full bg-blue-600 transition-all duration-300 ease-in-out\"\n        style={{ width: `${percentage}%` }}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,SAAS,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,EAAE,SAAS,EAAE,GAAG,OAAsB;IACnF,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,AAAC,QAAQ,MAAO,KAAK,IAAI;IAE9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC;YACC,WAAU;YACV,OAAO;gBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;YAAC;;;;;;;;;;;AAIzC", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/tabs.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport * as TabsPrimitive from '@radix-ui/react-tabs'\nimport { cn } from '@/lib/utils'\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      'inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground',\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm',\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      'mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/consolidation.ts"], "sourcesContent": ["\nimport { TransportMode, ShipmentType, CustomerType } from '@prisma/client'\n\n// Types pour la consolidation\nexport interface ConsolidationRequest {\n  id: string\n  customerId: string\n  customerType: CustomerType\n  weight: number\n  volume: number\n  originCity: string\n  destinationCity: string\n  transportMode: TransportMode\n  urgency: 'STANDARD' | 'EXPRESS' | 'URGENT'\n  requestedPickupDate: Date\n  declaredValue?: number\n  description: string\n}\n\nexport interface ConsolidationGroup {\n  id: string\n  transportMode: TransportMode\n  route: string\n  departureDate: Date\n  maxWeight: number\n  maxVolume: number\n  currentWeight: number\n  currentVolume: number\n  loadingRate: number\n  requests: ConsolidationRequest[]\n  estimatedCost: number\n  costPerClient: Array<{\n    requestId: string\n    allocatedCost: number\n    savings: number\n  }>\n}\n\nexport interface ConsolidationResult {\n  groups: ConsolidationGroup[]\n  totalSavings: number\n  optimizationScore: number\n  recommendations: string[]\n}\n\n// Configuration des capacités par mode de transport\nconst TRANSPORT_CAPACITIES = {\n  ROAD: {\n    maxWeight: 25000, // 25 tonnes\n    maxVolume: 100,   // 100 m³\n    costPerKm: 450    // CFA par km\n  },\n  AIR_EXPRESS: {\n    maxWeight: 5000,  // 5 tonnes\n    maxVolume: 25,    // 25 m³\n    costPerKm: 1200   // CFA par km\n  }\n}\n\n// Distances entre villes (en km)\nconst ROUTE_DISTANCES = {\n  'LAGOS_DAKAR': 3200,\n  'ABUJA_DAKAR': 3800,\n  'KANO_DAKAR': 2800\n}\n\n/**\n * Optimise la consolidation des demandes de transport\n */\nexport function optimizeConsolidation(\n  requests: ConsolidationRequest[],\n  maxDaysWindow: number = 3\n): ConsolidationResult {\n  // Grouper par mode de transport et route\n  const groupedRequests = groupRequestsByRoute(requests)\n  \n  // Créer les groupes de consolidation optimaux\n  const groups: ConsolidationGroup[] = []\n  let totalSavings = 0\n  const recommendations: string[] = []\n\n  for (const [routeKey, routeRequests] of Object.entries(groupedRequests)) {\n    const [origin, destination] = routeKey.split('_')\n    const transportMode = routeRequests[0].transportMode\n    \n    // Optimiser les groupes pour cette route\n    const routeGroups = optimizeRouteGroups(\n      routeRequests,\n      transportMode,\n      `${origin}_${destination}`,\n      maxDaysWindow\n    )\n    \n    groups.push(...routeGroups)\n    \n    // Calculer les économies pour cette route\n    const routeSavings = calculateRouteSavings(routeRequests, routeGroups)\n    totalSavings += routeSavings\n    \n    // Générer des recommandations\n    if (routeGroups.length > 1) {\n      recommendations.push(\n        `Route ${origin}-${destination}: ${routeGroups.length} groupes créés, économies de ${routeSavings.toLocaleString()} CFA`\n      )\n    }\n  }\n\n  // Calculer le score d'optimisation\n  const optimizationScore = calculateOptimizationScore(groups)\n\n  // Recommandations générales\n  if (totalSavings > 100000) {\n    recommendations.push(`Économies totales importantes: ${totalSavings.toLocaleString()} CFA`)\n  }\n  \n  if (optimizationScore < 0.7) {\n    recommendations.push('Considérer un délai de consolidation plus long pour améliorer l\\'optimisation')\n  }\n\n  return {\n    groups,\n    totalSavings,\n    optimizationScore,\n    recommendations\n  }\n}\n\n/**\n * Groupe les demandes par route et mode de transport\n */\nfunction groupRequestsByRoute(\n  requests: ConsolidationRequest[]\n): Record<string, ConsolidationRequest[]> {\n  const groups: Record<string, ConsolidationRequest[]> = {}\n\n  for (const request of requests) {\n    const routeKey = `${request.originCity}_${request.destinationCity}_${request.transportMode}`\n    \n    if (!groups[routeKey]) {\n      groups[routeKey] = []\n    }\n    \n    groups[routeKey].push(request)\n  }\n\n  return groups\n}\n\n/**\n * Optimise les groupes pour une route donnée\n */\nfunction optimizeRouteGroups(\n  requests: ConsolidationRequest[],\n  transportMode: TransportMode,\n  route: string,\n  maxDaysWindow: number\n): ConsolidationGroup[] {\n  const capacity = TRANSPORT_CAPACITIES[transportMode]\n  const groups: ConsolidationGroup[] = []\n  \n  // Trier par date de départ souhaitée\n  const sortedRequests = [...requests].sort(\n    (a, b) => a.requestedPickupDate.getTime() - b.requestedPickupDate.getTime()\n  )\n\n  let currentGroup: ConsolidationRequest[] = []\n  let currentWeight = 0\n  let currentVolume = 0\n  let groupStartDate = sortedRequests[0]?.requestedPickupDate\n\n  for (const request of sortedRequests) {\n    const daysDiff = groupStartDate \n      ? Math.abs(request.requestedPickupDate.getTime() - groupStartDate.getTime()) / (1000 * 60 * 60 * 24)\n      : 0\n\n    // Vérifier si on peut ajouter cette demande au groupe actuel\n    const canAddToGroup = \n      daysDiff <= maxDaysWindow &&\n      currentWeight + request.weight <= capacity.maxWeight &&\n      currentVolume + request.volume <= capacity.maxVolume\n\n    if (canAddToGroup && currentGroup.length > 0) {\n      // Ajouter au groupe actuel\n      currentGroup.push(request)\n      currentWeight += request.weight\n      currentVolume += request.volume\n    } else {\n      // Finaliser le groupe actuel s'il existe\n      if (currentGroup.length > 0) {\n        groups.push(createConsolidationGroup(currentGroup, transportMode, route))\n      }\n      \n      // Commencer un nouveau groupe\n      currentGroup = [request]\n      currentWeight = request.weight\n      currentVolume = request.volume\n      groupStartDate = request.requestedPickupDate\n    }\n  }\n\n  // Finaliser le dernier groupe\n  if (currentGroup.length > 0) {\n    groups.push(createConsolidationGroup(currentGroup, transportMode, route))\n  }\n\n  return groups\n}\n\n/**\n * Crée un groupe de consolidation\n */\nfunction createConsolidationGroup(\n  requests: ConsolidationRequest[],\n  transportMode: TransportMode,\n  route: string\n): ConsolidationGroup {\n  const capacity = TRANSPORT_CAPACITIES[transportMode]\n  const totalWeight = requests.reduce((sum, r) => sum + r.weight, 0)\n  const totalVolume = requests.reduce((sum, r) => sum + r.volume, 0)\n  \n  // Date de départ = date la plus tardive du groupe\n  const departureDate = new Date(\n    Math.max(...requests.map(r => r.requestedPickupDate.getTime()))\n  )\n\n  // Calculer le coût total du groupe\n  const distance = ROUTE_DISTANCES[route as keyof typeof ROUTE_DISTANCES] || 3000\n  const estimatedCost = distance * capacity.costPerKm\n\n  // Répartir les coûts proportionnellement au poids\n  const costPerClient = requests.map(request => {\n    const weightRatio = request.weight / totalWeight\n    const allocatedCost = estimatedCost * weightRatio\n    \n    // Calculer les économies (estimation basée sur le coût individuel)\n    const individualCost = estimatedCost * 0.8 // Estimation d'économie de 20%\n    const savings = individualCost - allocatedCost\n\n    return {\n      requestId: request.id,\n      allocatedCost: Math.round(allocatedCost),\n      savings: Math.round(savings)\n    }\n  })\n\n  return {\n    id: `CG-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n    transportMode,\n    route,\n    departureDate,\n    maxWeight: capacity.maxWeight,\n    maxVolume: capacity.maxVolume,\n    currentWeight: totalWeight,\n    currentVolume: totalVolume,\n    loadingRate: Math.max(totalWeight / capacity.maxWeight, totalVolume / capacity.maxVolume),\n    requests,\n    estimatedCost,\n    costPerClient\n  }\n}\n\n/**\n * Calcule les économies pour une route\n */\nfunction calculateRouteSavings(\n  originalRequests: ConsolidationRequest[],\n  optimizedGroups: ConsolidationGroup[]\n): number {\n  // Coût individuel estimé\n  const individualCosts = originalRequests.reduce((sum, request) => {\n    const route = `${request.originCity}_${request.destinationCity}`\n    const distance = ROUTE_DISTANCES[route as keyof typeof ROUTE_DISTANCES] || 3000\n    const capacity = TRANSPORT_CAPACITIES[request.transportMode]\n    return sum + (distance * capacity.costPerKm * 0.8) // Coût individuel estimé\n  }, 0)\n\n  // Coût consolidé\n  const consolidatedCosts = optimizedGroups.reduce((sum, group) => sum + group.estimatedCost, 0)\n\n  return Math.max(0, individualCosts - consolidatedCosts)\n}\n\n/**\n * Calcule le score d'optimisation (0-1)\n */\nfunction calculateOptimizationScore(groups: ConsolidationGroup[]): number {\n  if (groups.length === 0) return 0\n\n  const totalScore = groups.reduce((sum, group) => {\n    // Score basé sur le taux de chargement et le nombre de clients\n    const loadingScore = group.loadingRate\n    const clientScore = Math.min(group.requests.length / 5, 1) // Optimal à 5 clients\n    return sum + (loadingScore * 0.6 + clientScore * 0.4)\n  }, 0)\n\n  return totalScore / groups.length\n}\n\n/**\n * Trouve les opportunités de consolidation pour une nouvelle demande\n */\nexport function findConsolidationOpportunities(\n  newRequest: ConsolidationRequest,\n  existingGroups: ConsolidationGroup[]\n): Array<{\n  group: ConsolidationGroup\n  compatibility: number\n  estimatedSavings: number\n}> {\n  const opportunities = []\n\n  for (const group of existingGroups) {\n    // Vérifier la compatibilité\n    const isCompatibleRoute = group.route === `${newRequest.originCity}_${newRequest.destinationCity}`\n    const isCompatibleMode = group.transportMode === newRequest.transportMode\n    \n    if (!isCompatibleRoute || !isCompatibleMode) continue\n\n    // Vérifier la capacité\n    const hasWeightCapacity = group.currentWeight + newRequest.weight <= group.maxWeight\n    const hasVolumeCapacity = group.currentVolume + newRequest.volume <= group.maxVolume\n    \n    if (!hasWeightCapacity || !hasVolumeCapacity) continue\n\n    // Calculer la compatibilité temporelle\n    const daysDiff = Math.abs(\n      newRequest.requestedPickupDate.getTime() - group.departureDate.getTime()\n    ) / (1000 * 60 * 60 * 24)\n    \n    const timeCompatibility = Math.max(0, 1 - daysDiff / 7) // Décroît sur 7 jours\n\n    // Score de compatibilité global\n    const compatibility = timeCompatibility * 0.5 + \n                         (hasWeightCapacity ? 0.25 : 0) + \n                         (hasVolumeCapacity ? 0.25 : 0)\n\n    // Estimation des économies\n    const estimatedSavings = group.estimatedCost * 0.15 // 15% d'économie estimée\n\n    if (compatibility > 0.3) { // Seuil minimum de compatibilité\n      opportunities.push({\n        group,\n        compatibility,\n        estimatedSavings\n      })\n    }\n  }\n\n  // Trier par compatibilité décroissante\n  return opportunities.sort((a, b) => b.compatibility - a.compatibility)\n}\n\n"], "names": [], "mappings": ";;;;AA6CA,oDAAoD;AACpD,MAAM,uBAAuB;IAC3B,MAAM;QACJ,WAAW;QACX,WAAW;QACX,WAAW,IAAO,aAAa;IACjC;IACA,aAAa;QACX,WAAW;QACX,WAAW;QACX,WAAW,KAAO,aAAa;IACjC;AACF;AAEA,iCAAiC;AACjC,MAAM,kBAAkB;IACtB,eAAe;IACf,eAAe;IACf,cAAc;AAChB;AAKO,SAAS,sBACd,QAAgC,EAChC,gBAAwB,CAAC;IAEzB,yCAAyC;IACzC,MAAM,kBAAkB,qBAAqB;IAE7C,8CAA8C;IAC9C,MAAM,SAA+B,EAAE;IACvC,IAAI,eAAe;IACnB,MAAM,kBAA4B,EAAE;IAEpC,KAAK,MAAM,CAAC,UAAU,cAAc,IAAI,OAAO,OAAO,CAAC,iBAAkB;QACvE,MAAM,CAAC,QAAQ,YAAY,GAAG,SAAS,KAAK,CAAC;QAC7C,MAAM,gBAAgB,aAAa,CAAC,EAAE,CAAC,aAAa;QAEpD,yCAAyC;QACzC,MAAM,cAAc,oBAClB,eACA,eACA,GAAG,OAAO,CAAC,EAAE,aAAa,EAC1B;QAGF,OAAO,IAAI,IAAI;QAEf,0CAA0C;QAC1C,MAAM,eAAe,sBAAsB,eAAe;QAC1D,gBAAgB;QAEhB,8BAA8B;QAC9B,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,gBAAgB,IAAI,CAClB,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,YAAY,EAAE,EAAE,YAAY,MAAM,CAAC,6BAA6B,EAAE,aAAa,cAAc,GAAG,IAAI,CAAC;QAE5H;IACF;IAEA,mCAAmC;IACnC,MAAM,oBAAoB,2BAA2B;IAErD,4BAA4B;IAC5B,IAAI,eAAe,QAAQ;QACzB,gBAAgB,IAAI,CAAC,CAAC,+BAA+B,EAAE,aAAa,cAAc,GAAG,IAAI,CAAC;IAC5F;IAEA,IAAI,oBAAoB,KAAK;QAC3B,gBAAgB,IAAI,CAAC;IACvB;IAEA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAEA;;CAEC,GACD,SAAS,qBACP,QAAgC;IAEhC,MAAM,SAAiD,CAAC;IAExD,KAAK,MAAM,WAAW,SAAU;QAC9B,MAAM,WAAW,GAAG,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,eAAe,CAAC,CAAC,EAAE,QAAQ,aAAa,EAAE;QAE5F,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YACrB,MAAM,CAAC,SAAS,GAAG,EAAE;QACvB;QAEA,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;IACxB;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,oBACP,QAAgC,EAChC,aAA4B,EAC5B,KAAa,EACb,aAAqB;IAErB,MAAM,WAAW,oBAAoB,CAAC,cAAc;IACpD,MAAM,SAA+B,EAAE;IAEvC,qCAAqC;IACrC,MAAM,iBAAiB;WAAI;KAAS,CAAC,IAAI,CACvC,CAAC,GAAG,IAAM,EAAE,mBAAmB,CAAC,OAAO,KAAK,EAAE,mBAAmB,CAAC,OAAO;IAG3E,IAAI,eAAuC,EAAE;IAC7C,IAAI,gBAAgB;IACpB,IAAI,gBAAgB;IACpB,IAAI,iBAAiB,cAAc,CAAC,EAAE,EAAE;IAExC,KAAK,MAAM,WAAW,eAAgB;QACpC,MAAM,WAAW,iBACb,KAAK,GAAG,CAAC,QAAQ,mBAAmB,CAAC,OAAO,KAAK,eAAe,OAAO,MAAM,CAAC,OAAO,KAAK,KAAK,EAAE,IACjG;QAEJ,6DAA6D;QAC7D,MAAM,gBACJ,YAAY,iBACZ,gBAAgB,QAAQ,MAAM,IAAI,SAAS,SAAS,IACpD,gBAAgB,QAAQ,MAAM,IAAI,SAAS,SAAS;QAEtD,IAAI,iBAAiB,aAAa,MAAM,GAAG,GAAG;YAC5C,2BAA2B;YAC3B,aAAa,IAAI,CAAC;YAClB,iBAAiB,QAAQ,MAAM;YAC/B,iBAAiB,QAAQ,MAAM;QACjC,OAAO;YACL,yCAAyC;YACzC,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,OAAO,IAAI,CAAC,yBAAyB,cAAc,eAAe;YACpE;YAEA,8BAA8B;YAC9B,eAAe;gBAAC;aAAQ;YACxB,gBAAgB,QAAQ,MAAM;YAC9B,gBAAgB,QAAQ,MAAM;YAC9B,iBAAiB,QAAQ,mBAAmB;QAC9C;IACF;IAEA,8BAA8B;IAC9B,IAAI,aAAa,MAAM,GAAG,GAAG;QAC3B,OAAO,IAAI,CAAC,yBAAyB,cAAc,eAAe;IACpE;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,yBACP,QAAgC,EAChC,aAA4B,EAC5B,KAAa;IAEb,MAAM,WAAW,oBAAoB,CAAC,cAAc;IACpD,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAChE,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAEhE,kDAAkD;IAClD,MAAM,gBAAgB,IAAI,KACxB,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,mBAAmB,CAAC,OAAO;IAG7D,mCAAmC;IACnC,MAAM,WAAW,eAAe,CAAC,MAAsC,IAAI;IAC3E,MAAM,gBAAgB,WAAW,SAAS,SAAS;IAEnD,kDAAkD;IAClD,MAAM,gBAAgB,SAAS,GAAG,CAAC,CAAA;QACjC,MAAM,cAAc,QAAQ,MAAM,GAAG;QACrC,MAAM,gBAAgB,gBAAgB;QAEtC,mEAAmE;QACnE,MAAM,iBAAiB,gBAAgB,IAAI,+BAA+B;;QAC1E,MAAM,UAAU,iBAAiB;QAEjC,OAAO;YACL,WAAW,QAAQ,EAAE;YACrB,eAAe,KAAK,KAAK,CAAC;YAC1B,SAAS,KAAK,KAAK,CAAC;QACtB;IACF;IAEA,OAAO;QACL,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QACjE;QACA;QACA;QACA,WAAW,SAAS,SAAS;QAC7B,WAAW,SAAS,SAAS;QAC7B,eAAe;QACf,eAAe;QACf,aAAa,KAAK,GAAG,CAAC,cAAc,SAAS,SAAS,EAAE,cAAc,SAAS,SAAS;QACxF;QACA;QACA;IACF;AACF;AAEA;;CAEC,GACD,SAAS,sBACP,gBAAwC,EACxC,eAAqC;IAErC,yBAAyB;IACzB,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAC,KAAK;QACpD,MAAM,QAAQ,GAAG,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,eAAe,EAAE;QAChE,MAAM,WAAW,eAAe,CAAC,MAAsC,IAAI;QAC3E,MAAM,WAAW,oBAAoB,CAAC,QAAQ,aAAa,CAAC;QAC5D,OAAO,MAAO,WAAW,SAAS,SAAS,GAAG,IAAK,yBAAyB;;IAC9E,GAAG;IAEH,iBAAiB;IACjB,MAAM,oBAAoB,gBAAgB,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,aAAa,EAAE;IAE5F,OAAO,KAAK,GAAG,CAAC,GAAG,kBAAkB;AACvC;AAEA;;CAEC,GACD,SAAS,2BAA2B,MAA4B;IAC9D,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,MAAM,aAAa,OAAO,MAAM,CAAC,CAAC,KAAK;QACrC,+DAA+D;QAC/D,MAAM,eAAe,MAAM,WAAW;QACtC,MAAM,cAAc,KAAK,GAAG,CAAC,MAAM,QAAQ,CAAC,MAAM,GAAG,GAAG,GAAG,sBAAsB;;QACjF,OAAO,MAAM,CAAC,eAAe,MAAM,cAAc,GAAG;IACtD,GAAG;IAEH,OAAO,aAAa,OAAO,MAAM;AACnC;AAKO,SAAS,+BACd,UAAgC,EAChC,cAAoC;IAMpC,MAAM,gBAAgB,EAAE;IAExB,KAAK,MAAM,SAAS,eAAgB;QAClC,4BAA4B;QAC5B,MAAM,oBAAoB,MAAM,KAAK,KAAK,GAAG,WAAW,UAAU,CAAC,CAAC,EAAE,WAAW,eAAe,EAAE;QAClG,MAAM,mBAAmB,MAAM,aAAa,KAAK,WAAW,aAAa;QAEzE,IAAI,CAAC,qBAAqB,CAAC,kBAAkB;QAE7C,uBAAuB;QACvB,MAAM,oBAAoB,MAAM,aAAa,GAAG,WAAW,MAAM,IAAI,MAAM,SAAS;QACpF,MAAM,oBAAoB,MAAM,aAAa,GAAG,WAAW,MAAM,IAAI,MAAM,SAAS;QAEpF,IAAI,CAAC,qBAAqB,CAAC,mBAAmB;QAE9C,uCAAuC;QACvC,MAAM,WAAW,KAAK,GAAG,CACvB,WAAW,mBAAmB,CAAC,OAAO,KAAK,MAAM,aAAa,CAAC,OAAO,MACpE,CAAC,OAAO,KAAK,KAAK,EAAE;QAExB,MAAM,oBAAoB,KAAK,GAAG,CAAC,GAAG,IAAI,WAAW,GAAG,sBAAsB;;QAE9E,gCAAgC;QAChC,MAAM,gBAAgB,oBAAoB,MACrB,CAAC,oBAAoB,OAAO,CAAC,IAC7B,CAAC,oBAAoB,OAAO,CAAC;QAElD,2BAA2B;QAC3B,MAAM,mBAAmB,MAAM,aAAa,GAAG,KAAK,yBAAyB;;QAE7E,IAAI,gBAAgB,KAAK;YACvB,cAAc,IAAI,CAAC;gBACjB;gBACA;gBACA;YACF;QACF;IACF;IAEA,uCAAuC;IACvC,OAAO,cAAc,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;AACvE", "debugId": null}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/ConsolidationManager.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge-component'\nimport { Progress } from '@/components/ui/progress'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { \n  Truck, \n  Package, \n  Users, \n  TrendingUp, \n  Calendar,\n  MapPin,\n  Weight,\n  DollarSign,\n  AlertCircle,\n  CheckCircle,\n  Clock\n} from 'lucide-react'\nimport { optimizeConsolidation, type ConsolidationRequest, type ConsolidationGroup } from '@/lib/consolidation'\n\n// Données de démonstration\nconst mockRequests: ConsolidationRequest[] = [\n  {\n    id: 'REQ-001',\n    customerId: 'CUST-001',\n    customerType: 'LOGISTICS',\n    weight: 450,\n    volume: 2.5,\n    originCity: 'LAGOS',\n    destinationCity: 'DAKAR',\n    transportMode: 'ROAD',\n    urgency: 'STANDARD',\n    requestedPickupDate: new Date('2024-01-15'),\n    declaredValue: 850000,\n    description: 'Équipements électroniques'\n  },\n  {\n    id: 'REQ-002',\n    customerId: 'CUST-002',\n    customerType: 'COMMERCE',\n    weight: 320,\n    volume: 1.8,\n    originCity: 'LAGOS',\n    destinationCity: 'DAKAR',\n    transportMode: 'ROAD',\n    urgency: 'STANDARD',\n    requestedPickupDate: new Date('2024-01-16'),\n    declaredValue: 650000,\n    description: 'Produits cosmétiques'\n  },\n  {\n    id: 'REQ-003',\n    customerId: 'CUST-003',\n    customerType: 'LOGISTICS',\n    weight: 180,\n    volume: 1.2,\n    originCity: 'LAGOS',\n    destinationCity: 'DAKAR',\n    transportMode: 'ROAD',\n    urgency: 'EXPRESS',\n    requestedPickupDate: new Date('2024-01-17'),\n    declaredValue: 420000,\n    description: 'Pièces automobiles'\n  },\n  {\n    id: 'REQ-004',\n    customerId: 'CUST-004',\n    customerType: 'LOGISTICS',\n    weight: 75,\n    volume: 0.8,\n    originCity: 'ABUJA',\n    destinationCity: 'DAKAR',\n    transportMode: 'AIR_EXPRESS',\n    urgency: 'URGENT',\n    requestedPickupDate: new Date('2024-01-15'),\n    declaredValue: 180000,\n    description: 'Documents urgents'\n  }\n]\n\nexport function ConsolidationManager() {\n  const [requests, setRequests] = useState<ConsolidationRequest[]>(mockRequests)\n  const [optimizationResult, setOptimizationResult] = useState<any>(null)\n  const [isOptimizing, setIsOptimizing] = useState(false)\n\n  useEffect(() => {\n    optimizeRequests()\n  }, [requests])\n\n  const optimizeRequests = async () => {\n    setIsOptimizing(true)\n    try {\n      const result = optimizeConsolidation(requests, 3)\n      setOptimizationResult(result)\n    } catch (error) {\n      console.error('Erreur optimisation:', error)\n    } finally {\n      setIsOptimizing(false)\n    }\n  }\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'XOF',\n      minimumFractionDigits: 0\n    }).format(amount)\n  }\n\n  const getUrgencyBadge = (urgency: string) => {\n    const config = {\n      STANDARD: { label: 'Standard', color: 'bg-green-100 text-green-800' },\n      EXPRESS: { label: 'Express', color: 'bg-orange-100 text-orange-800' },\n      URGENT: { label: 'Urgent', color: 'bg-red-100 text-red-800' }\n    }\n    const urgencyConfig = config[urgency as keyof typeof config] || config.STANDARD\n    return <Badge className={urgencyConfig.color}>{urgencyConfig.label}</Badge>\n  }\n\n  const getCustomerTypeBadge = (type: string) => {\n    const config = {\n      LOGISTICS: { label: 'Logistique', color: 'bg-blue-100 text-blue-800' },\n      COMMERCE: { label: 'Commerce', color: 'bg-purple-100 text-purple-800' }\n    }\n    const typeConfig = config[type as keyof typeof config] || config.LOGISTICS\n    return <Badge className={typeConfig.color}>{typeConfig.label}</Badge>\n  }\n\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* En-tête */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            Gestion des Consolidations\n          </h1>\n          <p className=\"text-gray-600\">\n            Optimisation intelligente des expéditions multi-clients\n          </p>\n        </div>\n        <Button onClick={optimizeRequests} disabled={isOptimizing}>\n          {isOptimizing ? 'Optimisation...' : 'Réoptimiser'}\n        </Button>\n      </div>\n\n      {/* Métriques de performance */}\n      {optimizationResult && (\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-green-100 rounded-lg\">\n                  <DollarSign className=\"h-6 w-6 text-green-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Économies totales</p>\n                  <p className=\"text-2xl font-bold text-green-600\">\n                    {formatCurrency(optimizationResult.totalSavings)}\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <Truck className=\"h-6 w-6 text-blue-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Groupes créés</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {optimizationResult.groups.length}\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-purple-100 rounded-lg\">\n                  <TrendingUp className=\"h-6 w-6 text-purple-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Score d'optimisation</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {(optimizationResult.optimizationScore * 100).toFixed(0)}%\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-yellow-100 rounded-lg\">\n                  <Users className=\"h-6 w-6 text-yellow-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Clients impactés</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {requests.length}\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Contenu principal */}\n      <Tabs defaultValue=\"groups\" className=\"space-y-6\">\n        <TabsList>\n          <TabsTrigger value=\"groups\">Groupes optimisés</TabsTrigger>\n          <TabsTrigger value=\"requests\">Demandes en attente</TabsTrigger>\n          <TabsTrigger value=\"recommendations\">Recommandations</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"groups\">\n          {optimizationResult?.groups.length > 0 ? (\n            <div className=\"space-y-4\">\n              {optimizationResult.groups.map((group: ConsolidationGroup) => (\n                <Card key={group.id}>\n                  <CardHeader>\n                    <div className=\"flex justify-between items-start\">\n                      <div>\n                        <CardTitle className=\"flex items-center\">\n                          <Truck className=\"h-5 w-5 mr-2 text-blue-600\" />\n                          Groupe {group.id}\n                        </CardTitle>\n                        <CardDescription>\n                          {group.route.replace('_', ' → ')} • {group.transportMode === 'ROAD' ? 'Routier' : 'Aérien'}\n                        </CardDescription>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-2xl font-bold text-gray-900\">\n                          {formatCurrency(group.estimatedCost)}\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          Départ: {group.departureDate.toLocaleDateString('fr-FR')}\n                        </div>\n                      </div>\n                    </div>\n                  </CardHeader>\n                  <CardContent>\n                    {/* Métriques du groupe */}\n                    <div className=\"grid grid-cols-3 gap-4 mb-6\">\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl font-bold text-blue-600\">\n                          {group.requests.length}\n                        </div>\n                        <div className=\"text-sm text-gray-500\">Clients</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl font-bold text-green-600\">\n                          {group.currentWeight.toLocaleString()} kg\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          / {group.maxWeight.toLocaleString()} kg\n                        </div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl font-bold text-purple-600\">\n                          {(group.loadingRate * 100).toFixed(0)}%\n                        </div>\n                        <div className=\"text-sm text-gray-500\">Taux de charge</div>\n                      </div>\n                    </div>\n\n                    {/* Barre de progression */}\n                    <div className=\"mb-6\">\n                      <div className=\"flex justify-between text-sm text-gray-600 mb-2\">\n                        <span>Capacité utilisée</span>\n                        <span>{(group.loadingRate * 100).toFixed(1)}%</span>\n                      </div>\n                      <Progress value={group.loadingRate * 100} className=\"h-2\" />\n                    </div>\n\n                    {/* Liste des demandes */}\n                    <div className=\"space-y-3\">\n                      <h4 className=\"font-medium text-gray-900\">Demandes consolidées</h4>\n                      {group.requests.map((request) => {\n                        const clientCost = group.costPerClient.find(c => c.requestId === request.id)\n                        return (\n                          <div key={request.id} className=\"border border-gray-200 rounded-lg p-3\">\n                            <div className=\"flex justify-between items-start\">\n                              <div className=\"flex-1\">\n                                <div className=\"flex items-center space-x-2 mb-1\">\n                                  <span className=\"font-medium text-sm\">{request.id}</span>\n                                  {getCustomerTypeBadge(request.customerType)}\n                                  {getUrgencyBadge(request.urgency)}\n                                </div>\n                                <p className=\"text-sm text-gray-600 mb-1\">{request.description}</p>\n                                <div className=\"flex items-center space-x-4 text-xs text-gray-500\">\n                                  <span className=\"flex items-center\">\n                                    <Weight className=\"h-3 w-3 mr-1\" />\n                                    {request.weight} kg\n                                  </span>\n                                  <span className=\"flex items-center\">\n                                    <Package className=\"h-3 w-3 mr-1\" />\n                                    {request.volume} m³\n                                  </span>\n                                  <span className=\"flex items-center\">\n                                    <Calendar className=\"h-3 w-3 mr-1\" />\n                                    {request.requestedPickupDate.toLocaleDateString('fr-FR')}\n                                  </span>\n                                </div>\n                              </div>\n                              <div className=\"text-right\">\n                                <div className=\"font-bold text-sm\">\n                                  {formatCurrency(clientCost?.allocatedCost || 0)}\n                                </div>\n                                {clientCost?.savings && clientCost.savings > 0 && (\n                                  <div className=\"text-xs text-green-600\">\n                                    Économie: {formatCurrency(clientCost.savings)}\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n                          </div>\n                        )\n                      })}\n                    </div>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          ) : (\n            <Card>\n              <CardContent className=\"text-center py-8\">\n                <Truck className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" />\n                <p className=\"text-gray-500\">Aucun groupe de consolidation disponible</p>\n              </CardContent>\n            </Card>\n          )}\n        </TabsContent>\n\n        <TabsContent value=\"requests\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Demandes en attente de consolidation</CardTitle>\n              <CardDescription>\n                {requests.length} demande{requests.length > 1 ? 's' : ''} à traiter\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {requests.map((request) => (\n                  <div key={request.id} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex justify-between items-start\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-2 mb-2\">\n                          <span className=\"font-medium\">{request.id}</span>\n                          {getCustomerTypeBadge(request.customerType)}\n                          {getUrgencyBadge(request.urgency)}\n                        </div>\n                        <p className=\"text-gray-600 mb-2\">{request.description}</p>\n                        <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                          <span className=\"flex items-center\">\n                            <MapPin className=\"h-4 w-4 mr-1\" />\n                            {request.originCity} → {request.destinationCity}\n                          </span>\n                          <span className=\"flex items-center\">\n                            <Weight className=\"h-4 w-4 mr-1\" />\n                            {request.weight} kg\n                          </span>\n                          <span className=\"flex items-center\">\n                            <Calendar className=\"h-4 w-4 mr-1\" />\n                            {request.requestedPickupDate.toLocaleDateString('fr-FR')}\n                          </span>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"font-bold text-lg\">\n                          {formatCurrency(request.declaredValue || 0)}\n                        </div>\n                        <div className=\"text-sm text-gray-500\">Valeur déclarée</div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"recommendations\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <AlertCircle className=\"h-5 w-5 mr-2 text-orange-600\" />\n                Recommandations d'optimisation\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              {optimizationResult?.recommendations.length > 0 ? (\n                <div className=\"space-y-3\">\n                  {optimizationResult.recommendations.map((recommendation: string, index: number) => (\n                    <div key={index} className=\"flex items-start space-x-3 p-3 bg-blue-50 rounded-lg\">\n                      <CheckCircle className=\"h-5 w-5 text-blue-600 mt-0.5\" />\n                      <p className=\"text-blue-800\">{recommendation}</p>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-8\">\n                  <CheckCircle className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" />\n                  <p className=\"text-gray-500\">Aucune recommandation disponible</p>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AArBA;;;;;;;;;;AAuBA,2BAA2B;AAC3B,MAAM,eAAuC;IAC3C;QACE,IAAI;QACJ,YAAY;QACZ,cAAc;QACd,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,iBAAiB;QACjB,eAAe;QACf,SAAS;QACT,qBAAqB,IAAI,KAAK;QAC9B,eAAe;QACf,aAAa;IACf;IACA;QACE,IAAI;QACJ,YAAY;QACZ,cAAc;QACd,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,iBAAiB;QACjB,eAAe;QACf,SAAS;QACT,qBAAqB,IAAI,KAAK;QAC9B,eAAe;QACf,aAAa;IACf;IACA;QACE,IAAI;QACJ,YAAY;QACZ,cAAc;QACd,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,iBAAiB;QACjB,eAAe;QACf,SAAS;QACT,qBAAqB,IAAI,KAAK;QAC9B,eAAe;QACf,aAAa;IACf;IACA;QACE,IAAI;QACJ,YAAY;QACZ,cAAc;QACd,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,iBAAiB;QACjB,eAAe;QACf,SAAS;QACT,qBAAqB,IAAI,KAAK;QAC9B,eAAe;QACf,aAAa;IACf;CACD;AAEM,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACjE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,CAAA,GAAA,2HAAA,CAAA,wBAAqB,AAAD,EAAE,UAAU;YAC/C,sBAAsB;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAS;YACb,UAAU;gBAAE,OAAO;gBAAY,OAAO;YAA8B;YACpE,SAAS;gBAAE,OAAO;gBAAW,OAAO;YAAgC;YACpE,QAAQ;gBAAE,OAAO;gBAAU,OAAO;YAA0B;QAC9D;QACA,MAAM,gBAAgB,MAAM,CAAC,QAA+B,IAAI,OAAO,QAAQ;QAC/E,qBAAO,8OAAC,8IAAA,CAAA,QAAK;YAAC,WAAW,cAAc,KAAK;sBAAG,cAAc,KAAK;;;;;;IACpE;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,SAAS;YACb,WAAW;gBAAE,OAAO;gBAAc,OAAO;YAA4B;YACrE,UAAU;gBAAE,OAAO;gBAAY,OAAO;YAAgC;QACxE;QACA,MAAM,aAAa,MAAM,CAAC,KAA4B,IAAI,OAAO,SAAS;QAC1E,qBAAO,8OAAC,8IAAA,CAAA,QAAK;YAAC,WAAW,WAAW,KAAK;sBAAG,WAAW,KAAK;;;;;;IAC9D;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAI/B,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAkB,UAAU;kCAC1C,eAAe,oBAAoB;;;;;;;;;;;;YAKvC,oCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,eAAe,mBAAmB,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOzD,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,mBAAmB,MAAM,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3C,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;oDACV,CAAC,mBAAmB,iBAAiB,GAAG,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOnE,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU9B,8OAAC,gIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAS,WAAU;;kCACpC,8OAAC,gIAAA,CAAA,WAAQ;;0CACP,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAS;;;;;;0CAC5B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;0CAC9B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAkB;;;;;;;;;;;;kCAGvC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCAChB,oBAAoB,OAAO,SAAS,kBACnC,8OAAC;4BAAI,WAAU;sCACZ,mBAAmB,MAAM,CAAC,GAAG,CAAC,CAAC,sBAC9B,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;;kFACnB,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAA+B;oEACxC,MAAM,EAAE;;;;;;;0EAElB,8OAAC,gIAAA,CAAA,kBAAe;;oEACb,MAAM,KAAK,CAAC,OAAO,CAAC,KAAK;oEAAO;oEAAI,MAAM,aAAa,KAAK,SAAS,YAAY;;;;;;;;;;;;;kEAGtF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,eAAe,MAAM,aAAa;;;;;;0EAErC,8OAAC;gEAAI,WAAU;;oEAAwB;oEAC5B,MAAM,aAAa,CAAC,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;sDAKxD,8OAAC,gIAAA,CAAA,cAAW;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACZ,MAAM,QAAQ,CAAC,MAAM;;;;;;8EAExB,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;wEACZ,MAAM,aAAa,CAAC,cAAc;wEAAG;;;;;;;8EAExC,8OAAC;oEAAI,WAAU;;wEAAwB;wEAClC,MAAM,SAAS,CAAC,cAAc;wEAAG;;;;;;;;;;;;;sEAGxC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;wEACZ,CAAC,MAAM,WAAW,GAAG,GAAG,EAAE,OAAO,CAAC;wEAAG;;;;;;;8EAExC,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAK3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;;wEAAM,CAAC,MAAM,WAAW,GAAG,GAAG,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;sEAE9C,8OAAC,oIAAA,CAAA,WAAQ;4DAAC,OAAO,MAAM,WAAW,GAAG;4DAAK,WAAU;;;;;;;;;;;;8DAItD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA4B;;;;;;wDACzC,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC;4DACnB,MAAM,aAAa,MAAM,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,QAAQ,EAAE;4DAC3E,qBACE,8OAAC;gEAAqB,WAAU;0EAC9B,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAK,WAAU;sGAAuB,QAAQ,EAAE;;;;;;wFAChD,qBAAqB,QAAQ,YAAY;wFACzC,gBAAgB,QAAQ,OAAO;;;;;;;8FAElC,8OAAC;oFAAE,WAAU;8FAA8B,QAAQ,WAAW;;;;;;8FAC9D,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAK,WAAU;;8GACd,8OAAC,sMAAA,CAAA,SAAM;oGAAC,WAAU;;;;;;gGACjB,QAAQ,MAAM;gGAAC;;;;;;;sGAElB,8OAAC;4FAAK,WAAU;;8GACd,8OAAC,wMAAA,CAAA,UAAO;oGAAC,WAAU;;;;;;gGAClB,QAAQ,MAAM;gGAAC;;;;;;;sGAElB,8OAAC;4FAAK,WAAU;;8GACd,8OAAC,0MAAA,CAAA,WAAQ;oGAAC,WAAU;;;;;;gGACnB,QAAQ,mBAAmB,CAAC,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;sFAItD,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;8FACZ,eAAe,YAAY,iBAAiB;;;;;;gFAE9C,YAAY,WAAW,WAAW,OAAO,GAAG,mBAC3C,8OAAC;oFAAI,WAAU;;wFAAyB;wFAC3B,eAAe,WAAW,OAAO;;;;;;;;;;;;;;;;;;;+DA9B5C,QAAQ,EAAE;;;;;wDAqCxB;;;;;;;;;;;;;;mCAnGK,MAAM,EAAE;;;;;;;;;iDA0GvB,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;kCAMrC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;;gDACb,SAAS,MAAM;gDAAC;gDAAS,SAAS,MAAM,GAAG,IAAI,MAAM;gDAAG;;;;;;;;;;;;;8CAG7D,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gDAAqB,WAAU;0DAC9B,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAe,QAAQ,EAAE;;;;;;wEACxC,qBAAqB,QAAQ,YAAY;wEACzC,gBAAgB,QAAQ,OAAO;;;;;;;8EAElC,8OAAC;oEAAE,WAAU;8EAAsB,QAAQ,WAAW;;;;;;8EACtD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;;8FACd,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFACjB,QAAQ,UAAU;gFAAC;gFAAI,QAAQ,eAAe;;;;;;;sFAEjD,8OAAC;4EAAK,WAAU;;8FACd,8OAAC,sMAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFACjB,QAAQ,MAAM;gFAAC;;;;;;;sFAElB,8OAAC;4EAAK,WAAU;;8FACd,8OAAC,0MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;gFACnB,QAAQ,mBAAmB,CAAC,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;sEAItD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACZ,eAAe,QAAQ,aAAa,IAAI;;;;;;8EAE3C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;+CA5BnC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCAsC9B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAiC;;;;;;;;;;;;8CAI5D,8OAAC,gIAAA,CAAA,cAAW;8CACT,oBAAoB,gBAAgB,SAAS,kBAC5C,8OAAC;wCAAI,WAAU;kDACZ,mBAAmB,eAAe,CAAC,GAAG,CAAC,CAAC,gBAAwB,sBAC/D,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;wDAAE,WAAU;kEAAiB;;;;;;;+CAFtB;;;;;;;;;6DAOd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C", "debugId": null}}]}