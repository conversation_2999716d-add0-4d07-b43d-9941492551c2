// Service de conversion de devises NGN/XOF
export interface ExchangeRate {
  from: 'NGN' | 'XOF'
  to: 'NGN' | 'XOF'
  rate: number
  lastUpdated: Date
}

export interface CurrencyConversion {
  amount: number
  fromCurrency: 'NGN' | 'XOF'
  toCurrency: 'NGN' | 'XOF'
  convertedAmount: number
  exchangeRate: number
  timestamp: Date
}

// Cache des taux de change pour éviter trop d'appels API
let exchangeRateCache: {
  NGN_TO_XOF: { rate: number; timestamp: Date } | null
  XOF_TO_NGN: { rate: number; timestamp: Date } | null
} = {
  NGN_TO_XOF: null,
  XOF_TO_NGN: null
}

const CACHE_DURATION = 30 * 60 * 1000 // 30 minutes en millisecondes

// Taux de change par défaut (fallback) basés sur les taux moyens récents
const DEFAULT_RATES = {
  NGN_TO_XOF: 0.38, // 1 NGN ≈ 0.38 XOF
  XOF_TO_NGN: 2.63   // 1 XOF ≈ 2.63 NGN
}

/**
 * Récupère les taux de change depuis l'API ExchangeRate-API
 */
async function fetchExchangeRates(): Promise<{ NGN_TO_XOF: number; XOF_TO_NGN: number }> {
  try {
    // Utilisation de l'API gratuite ExchangeRate-API
    const response = await fetch('https://api.exchangerate-api.com/v4/latest/NGN', {
      next: { revalidate: 1800 } // Cache pendant 30 minutes
    })
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`)
    }
    
    const data = await response.json()
    
    // Vérifier si XOF est disponible dans les taux
    if (!data.rates || !data.rates.XOF) {
      throw new Error('XOF rate not available')
    }
    
    const ngnToXof = data.rates.XOF
    const xofToNgn = 1 / ngnToXof
    
    return {
      NGN_TO_XOF: ngnToXof,
      XOF_TO_NGN: xofToNgn
    }
  } catch (error) {
    console.warn('Erreur lors de la récupération des taux de change:', error)
    
    // Fallback vers les taux par défaut
    return {
      NGN_TO_XOF: DEFAULT_RATES.NGN_TO_XOF,
      XOF_TO_NGN: DEFAULT_RATES.XOF_TO_NGN
    }
  }
}

/**
 * Obtient le taux de change actuel avec mise en cache
 */
export async function getExchangeRate(from: 'NGN' | 'XOF', to: 'NGN' | 'XOF'): Promise<ExchangeRate> {
  if (from === to) {
    return {
      from,
      to,
      rate: 1,
      lastUpdated: new Date()
    }
  }
  
  const cacheKey = `${from}_TO_${to}` as keyof typeof exchangeRateCache
  const cached = exchangeRateCache[cacheKey]
  
  // Vérifier si le cache est encore valide
  if (cached && (Date.now() - cached.timestamp.getTime()) < CACHE_DURATION) {
    return {
      from,
      to,
      rate: cached.rate,
      lastUpdated: cached.timestamp
    }
  }
  
  // Récupérer les nouveaux taux
  try {
    const rates = await fetchExchangeRates()
    const now = new Date()
    
    // Mettre à jour le cache
    exchangeRateCache.NGN_TO_XOF = {
      rate: rates.NGN_TO_XOF,
      timestamp: now
    }
    exchangeRateCache.XOF_TO_NGN = {
      rate: rates.XOF_TO_NGN,
      timestamp: now
    }
    
    const rate = cacheKey === 'NGN_TO_XOF' ? rates.NGN_TO_XOF : rates.XOF_TO_NGN
    
    return {
      from,
      to,
      rate,
      lastUpdated: now
    }
  } catch (error) {
    console.error('Erreur lors de la récupération du taux de change:', error)
    
    // Utiliser les taux par défaut
    const rate = from === 'NGN' ? DEFAULT_RATES.NGN_TO_XOF : DEFAULT_RATES.XOF_TO_NGN
    
    return {
      from,
      to,
      rate,
      lastUpdated: new Date()
    }
  }
}

/**
 * Convertit un montant d'une devise à une autre
 */
export async function convertCurrency(
  amount: number,
  fromCurrency: 'NGN' | 'XOF',
  toCurrency: 'NGN' | 'XOF'
): Promise<CurrencyConversion> {
  const exchangeRate = await getExchangeRate(fromCurrency, toCurrency)
  const convertedAmount = amount * exchangeRate.rate
  
  return {
    amount,
    fromCurrency,
    toCurrency,
    convertedAmount: Math.round(convertedAmount * 100) / 100, // Arrondir à 2 décimales
    exchangeRate: exchangeRate.rate,
    timestamp: new Date()
  }
}

/**
 * Formate un montant avec la devise appropriée
 */
export function formatCurrency(amount: number, currency: 'NGN' | 'XOF'): string {
  const formatter = new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  })
  
  return formatter.format(amount)
}

/**
 * Affiche un montant dans les deux devises
 */
export async function formatDualCurrency(
  amount: number,
  baseCurrency: 'NGN' | 'XOF'
): Promise<string> {
  const otherCurrency = baseCurrency === 'NGN' ? 'XOF' : 'NGN'
  const conversion = await convertCurrency(amount, baseCurrency, otherCurrency)
  
  const baseFormatted = formatCurrency(amount, baseCurrency)
  const convertedFormatted = formatCurrency(conversion.convertedAmount, otherCurrency)
  
  return `${baseFormatted} (≈ ${convertedFormatted})`
}

/**
 * Obtient les taux de change actuels pour l'affichage
 */
export async function getCurrentRates(): Promise<{
  ngnToXof: number
  xofToNgn: number
  lastUpdated: Date
}> {
  const ngnRate = await getExchangeRate('NGN', 'XOF')
  const xofRate = await getExchangeRate('XOF', 'NGN')
  
  return {
    ngnToXof: ngnRate.rate,
    xofToNgn: xofRate.rate,
    lastUpdated: ngnRate.lastUpdated
  }
}

/**
 * Vide le cache des taux de change (utile pour forcer une mise à jour)
 */
export function clearExchangeRateCache(): void {
  exchangeRateCache = {
    NGN_TO_XOF: null,
    XOF_TO_NGN: null
  }
}

/**
 * Calcule la marge en tenant compte de la conversion de devise
 */
export async function calculateMarginWithCurrency(
  costPrice: number,
  costCurrency: 'NGN' | 'XOF',
  sellingPrice: number,
  sellingCurrency: 'NGN' | 'XOF'
): Promise<{
  marginAmount: number
  marginPercentage: number
  currency: 'NGN' | 'XOF'
}> {
  let normalizedCostPrice = costPrice
  let normalizedSellingPrice = sellingPrice
  let resultCurrency = sellingCurrency
  
  // Convertir le prix de coût dans la même devise que le prix de vente
  if (costCurrency !== sellingCurrency) {
    const conversion = await convertCurrency(costPrice, costCurrency, sellingCurrency)
    normalizedCostPrice = conversion.convertedAmount
  }
  
  const marginAmount = normalizedSellingPrice - normalizedCostPrice
  const marginPercentage = normalizedCostPrice > 0 
    ? (marginAmount / normalizedCostPrice) * 100 
    : 0
  
  return {
    marginAmount: Math.round(marginAmount * 100) / 100,
    marginPercentage: Math.round(marginPercentage * 100) / 100,
    currency: resultCurrency
  }
}
