{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/shipments/NewShipmentForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge-component'\nimport { Truck, Plane, Package, MapPin, Calendar } from 'lucide-react'\n\ninterface Order {\n  id: string\n  orderNumber: string\n  customer: {\n    name: string\n    city: string\n    type: 'LOGISTICS' | 'COMMERCE'\n  }\n  totalAmount: number\n  status: string\n  createdAt: string\n}\n\ninterface Carrier {\n  id: string\n  name: string\n  transportModes: string[]\n  city: string\n  rating: number\n}\n\ninterface ShipmentFormData {\n  carrierId: string\n  transportMode: 'TRUCK' | 'AIR'\n  orderIds: string[]\n  pickupDate: string\n  estimatedDelivery: string\n  pickupAddress: string\n  deliveryAddress: string\n  notes: string\n  priority: 'LOW' | 'MEDIUM' | 'HIGH'\n}\n\nexport function NewShipmentForm() {\n  const router = useRouter()\n  const [orders, setOrders] = useState<Order[]>([])\n  const [carriers, setCarriers] = useState<Carrier[]>([])\n  const [selectedOrders, setSelectedOrders] = useState<string[]>([])\n  const [isLoading, setIsLoading] = useState(false)\n  const [formData, setFormData] = useState<ShipmentFormData>({\n    carrierId: '',\n    transportMode: 'TRUCK',\n    orderIds: [],\n    pickupDate: '',\n    estimatedDelivery: '',\n    pickupAddress: '',\n    deliveryAddress: 'Dakar, Sénégal',\n    notes: '',\n    priority: 'MEDIUM'\n  })\n\n  // Charger les commandes en attente et les transporteurs\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        const [ordersRes, carriersRes] = await Promise.all([\n          fetch('/api/orders?status=CONFIRMED'),\n          fetch('/api/carriers')\n        ])\n        \n        if (ordersRes.ok) {\n          const ordersData = await ordersRes.json()\n          setOrders(ordersData)\n        }\n        \n        if (carriersRes.ok) {\n          const carriersData = await carriersRes.json()\n          setCarriers(carriersData)\n        }\n      } catch (error) {\n        console.error('Erreur lors du chargement des données:', error)\n      }\n    }\n\n    loadData()\n  }, [])\n\n  // Mettre à jour les commandes sélectionnées dans le formulaire\n  useEffect(() => {\n    setFormData(prev => ({ ...prev, orderIds: selectedOrders }))\n  }, [selectedOrders])\n\n  const handleInputChange = (field: keyof ShipmentFormData, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n  }\n\n  const toggleOrderSelection = (orderId: string) => {\n    setSelectedOrders(prev =>\n      prev.includes(orderId)\n        ? prev.filter(id => id !== orderId)\n        : [...prev, orderId]\n    )\n  }\n\n  const calculateTotalWeight = () => {\n    return selectedOrders.length * 25 // Estimation moyenne de 25kg par commande\n  }\n\n  const calculateEstimatedCost = () => {\n    const baseRate = formData.transportMode === 'AIR' ? 150 : 75 // FCFA par kg\n    return calculateTotalWeight() * baseRate\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!formData.carrierId || selectedOrders.length === 0 || !formData.pickupDate) {\n      alert('Veuillez remplir tous les champs obligatoires et sélectionner au moins une commande')\n      return\n    }\n\n    setIsLoading(true)\n    try {\n      const response = await fetch('/api/shipments', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(formData)\n      })\n\n      if (response.ok) {\n        router.push('/shipments')\n      } else {\n        alert('Erreur lors de la création de l\\'expédition')\n      }\n    } catch (error) {\n      console.error('Erreur:', error)\n      alert('Erreur lors de la création de l\\'expédition')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const filteredCarriers = carriers.filter(carrier =>\n    carrier.transportModes.includes(formData.transportMode)\n  )\n\n  return (\n    <form onSubmit={handleSubmit} className=\"max-w-6xl mx-auto\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Configuration de l'expédition */}\n        <Card className=\"lg:col-span-2\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Truck className=\"h-5 w-5\" />\n              Configuration de l'expédition\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Mode de transport *\n                </label>\n                <div className=\"flex gap-2\">\n                  <Button\n                    type=\"button\"\n                    variant={formData.transportMode === 'TRUCK' ? 'default' : 'outline'}\n                    onClick={() => handleInputChange('transportMode', 'TRUCK')}\n                    className=\"flex-1\"\n                  >\n                    <Truck className=\"h-4 w-4 mr-2\" />\n                    Routier\n                  </Button>\n                  <Button\n                    type=\"button\"\n                    variant={formData.transportMode === 'AIR' ? 'default' : 'outline'}\n                    onClick={() => handleInputChange('transportMode', 'AIR')}\n                    className=\"flex-1\"\n                  >\n                    <Plane className=\"h-4 w-4 mr-2\" />\n                    Aérien\n                  </Button>\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Transporteur *\n                </label>\n                <select\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  value={formData.carrierId}\n                  onChange={(e) => handleInputChange('carrierId', e.target.value)}\n                >\n                  <option value=\"\">Sélectionner un transporteur</option>\n                  {filteredCarriers.map(carrier => (\n                    <option key={carrier.id} value={carrier.id}>\n                      {carrier.name} - {carrier.city} (★ {carrier.rating.toFixed(1)})\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Date d'enlèvement *\n                </label>\n                <input\n                  type=\"date\"\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  value={formData.pickupDate}\n                  onChange={(e) => handleInputChange('pickupDate', e.target.value)}\n                  min={new Date().toISOString().split('T')[0]}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Livraison estimée\n                </label>\n                <input\n                  type=\"date\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  value={formData.estimatedDelivery}\n                  onChange={(e) => handleInputChange('estimatedDelivery', e.target.value)}\n                  min={formData.pickupDate}\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Adresse d'enlèvement\n              </label>\n              <input\n                type=\"text\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                value={formData.pickupAddress}\n                onChange={(e) => handleInputChange('pickupAddress', e.target.value)}\n                placeholder=\"Adresse complète d'enlèvement\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Priorité\n              </label>\n              <select\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                value={formData.priority}\n                onChange={(e) => handleInputChange('priority', e.target.value as 'LOW' | 'MEDIUM' | 'HIGH')}\n              >\n                <option value=\"LOW\">Basse</option>\n                <option value=\"MEDIUM\">Moyenne</option>\n                <option value=\"HIGH\">Haute</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Notes\n              </label>\n              <textarea\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                rows={3}\n                value={formData.notes}\n                onChange={(e) => handleInputChange('notes', e.target.value)}\n                placeholder=\"Instructions spéciales pour l'expédition...\"\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Résumé et estimation */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Package className=\"h-5 w-5\" />\n              Résumé\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"bg-blue-50 p-3 rounded-lg\">\n              <div className=\"text-sm text-blue-700 mb-1\">Commandes sélectionnées</div>\n              <div className=\"text-lg font-bold text-blue-900\">{selectedOrders.length}</div>\n            </div>\n\n            <div className=\"bg-green-50 p-3 rounded-lg\">\n              <div className=\"text-sm text-green-700 mb-1\">Poids estimé</div>\n              <div className=\"text-lg font-bold text-green-900\">{calculateTotalWeight()} kg</div>\n            </div>\n\n            <div className=\"bg-purple-50 p-3 rounded-lg\">\n              <div className=\"text-sm text-purple-700 mb-1\">Coût estimé</div>\n              <div className=\"text-lg font-bold text-purple-900\">\n                {calculateEstimatedCost().toLocaleString()} FCFA\n              </div>\n            </div>\n\n            {selectedOrders.length > 0 && (\n              <Button\n                type=\"submit\"\n                disabled={isLoading || !formData.carrierId || !formData.pickupDate}\n                className=\"w-full\"\n              >\n                {isLoading ? 'Création...' : 'Créer l\\'expédition'}\n              </Button>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Sélection des commandes */}\n        <Card className=\"lg:col-span-3\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Package className=\"h-5 w-5\" />\n              Commandes à expédier ({orders.length} disponibles)\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto\">\n              {orders.map(order => (\n                <div\n                  key={order.id}\n                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                    selectedOrders.includes(order.id)\n                      ? 'border-blue-500 bg-blue-50'\n                      : 'border-gray-200 hover:border-gray-300'\n                  }`}\n                  onClick={() => toggleOrderSelection(order.id)}\n                >\n                  <div className=\"flex justify-between items-start mb-2\">\n                    <div className=\"font-medium\">{order.orderNumber}</div>\n                    <Badge variant={order.customer.type === 'COMMERCE' ? 'default' : 'secondary'}>\n                      {order.customer.type}\n                    </Badge>\n                  </div>\n                  <div className=\"text-sm text-gray-600 mb-1\">{order.customer.name}</div>\n                  <div className=\"text-sm text-gray-600 mb-2\">{order.customer.city}</div>\n                  <div className=\"font-semibold text-blue-600\">\n                    {order.totalAmount.toLocaleString()} FCFA\n                  </div>\n                  <div className=\"text-xs text-gray-500 mt-1\">\n                    {new Date(order.createdAt).toLocaleDateString()}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Boutons d'action */}\n      <div className=\"flex justify-end gap-4 mt-6\">\n        <Button\n          type=\"button\"\n          variant=\"outline\"\n          onClick={() => router.back()}\n        >\n          Annuler\n        </Button>\n      </div>\n    </form>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAPA;;;;;;;;AA0CO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,WAAW;QACX,eAAe;QACf,UAAU,EAAE;QACZ,YAAY;QACZ,mBAAmB;QACnB,eAAe;QACf,iBAAiB;QACjB,OAAO;QACP,UAAU;IACZ;IAEA,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI;gBACF,MAAM,CAAC,WAAW,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;oBACjD,MAAM;oBACN,MAAM;iBACP;gBAED,IAAI,UAAU,EAAE,EAAE;oBAChB,MAAM,aAAa,MAAM,UAAU,IAAI;oBACvC,UAAU;gBACZ;gBAEA,IAAI,YAAY,EAAE,EAAE;oBAClB,MAAM,eAAe,MAAM,YAAY,IAAI;oBAC3C,YAAY;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0CAA0C;YAC1D;QACF;QAEA;IACF,GAAG,EAAE;IAEL,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAe,CAAC;IAC5D,GAAG;QAAC;KAAe;IAEnB,MAAM,oBAAoB,CAAC,OAA+B;QACxD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IAClD;IAEA,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB,CAAA,OAChB,KAAK,QAAQ,CAAC,WACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,WACzB;mBAAI;gBAAM;aAAQ;IAE1B;IAEA,MAAM,uBAAuB;QAC3B,OAAO,eAAe,MAAM,GAAG,GAAG,0CAA0C;;IAC9E;IAEA,MAAM,yBAAyB;QAC7B,MAAM,WAAW,SAAS,aAAa,KAAK,QAAQ,MAAM,GAAG,cAAc;;QAC3E,OAAO,yBAAyB;IAClC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,SAAS,IAAI,eAAe,MAAM,KAAK,KAAK,CAAC,SAAS,UAAU,EAAE;YAC9E,MAAM;YACN;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UACvC,QAAQ,cAAc,CAAC,QAAQ,CAAC,SAAS,aAAa;IAGxD,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIjC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAS,SAAS,aAAa,KAAK,UAAU,YAAY;gEAC1D,SAAS,IAAM,kBAAkB,iBAAiB;gEAClD,WAAU;;kFAEV,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGpC,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAS,SAAS,aAAa,KAAK,QAAQ,YAAY;gEACxD,SAAS,IAAM,kBAAkB,iBAAiB;gEAClD,WAAU;;kFAEV,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;0DAMxC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,QAAQ;wDACR,WAAU;wDACV,OAAO,SAAS,SAAS;wDACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;;0EAE9D,8OAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,iBAAiB,GAAG,CAAC,CAAA,wBACpB,8OAAC;oEAAwB,OAAO,QAAQ,EAAE;;wEACvC,QAAQ,IAAI;wEAAC;wEAAI,QAAQ,IAAI;wEAAC;wEAAK,QAAQ,MAAM,CAAC,OAAO,CAAC;wEAAG;;mEADnD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;kDAQ/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,QAAQ;wDACR,WAAU;wDACV,OAAO,SAAS,UAAU;wDAC1B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC/D,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;0DAI/C,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,OAAO,SAAS,iBAAiB;wDACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;wDACtE,KAAK,SAAS,UAAU;;;;;;;;;;;;;;;;;;kDAK9B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,OAAO,SAAS,aAAa;gDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAClE,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,WAAU;gDACV,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;;kEAE7D,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,8OAAC;wDAAO,OAAM;kEAAO;;;;;;;;;;;;;;;;;;kDAIzB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,WAAU;gDACV,MAAM;gDACN,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC1D,aAAY;;;;;;;;;;;;;;;;;;;;;;;;kCAOpB,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAInC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA6B;;;;;;0DAC5C,8OAAC;gDAAI,WAAU;0DAAmC,eAAe,MAAM;;;;;;;;;;;;kDAGzE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA8B;;;;;;0DAC7C,8OAAC;gDAAI,WAAU;;oDAAoC;oDAAuB;;;;;;;;;;;;;kDAG5E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,8OAAC;gDAAI,WAAU;;oDACZ,yBAAyB,cAAc;oDAAG;;;;;;;;;;;;;oCAI9C,eAAe,MAAM,GAAG,mBACvB,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU,aAAa,CAAC,SAAS,SAAS,IAAI,CAAC,SAAS,UAAU;wCAClE,WAAU;kDAET,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;kCAOrC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAY;wCACR,OAAO,MAAM;wCAAC;;;;;;;;;;;;0CAGzC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,OAAO,GAAG,CAAC,CAAA,sBACV,8OAAC;4CAEC,WAAW,CAAC,uDAAuD,EACjE,eAAe,QAAQ,CAAC,MAAM,EAAE,IAC5B,+BACA,yCACJ;4CACF,SAAS,IAAM,qBAAqB,MAAM,EAAE;;8DAE5C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAe,MAAM,WAAW;;;;;;sEAC/C,8OAAC,8IAAA,CAAA,QAAK;4DAAC,SAAS,MAAM,QAAQ,CAAC,IAAI,KAAK,aAAa,YAAY;sEAC9D,MAAM,QAAQ,CAAC,IAAI;;;;;;;;;;;;8DAGxB,8OAAC;oDAAI,WAAU;8DAA8B,MAAM,QAAQ,CAAC,IAAI;;;;;;8DAChE,8OAAC;oDAAI,WAAU;8DAA8B,MAAM,QAAQ,CAAC,IAAI;;;;;;8DAChE,8OAAC;oDAAI,WAAU;;wDACZ,MAAM,WAAW,CAAC,cAAc;wDAAG;;;;;;;8DAEtC,8OAAC;oDAAI,WAAU;8DACZ,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;;;;;;;2CApB1C,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA8BzB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,MAAK;oBACL,SAAQ;oBACR,SAAS,IAAM,OAAO,IAAI;8BAC3B;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 831, "column": 0}, "map": {"version": 3, "file": "plane.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/plane.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.******* 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z',\n      key: '1v9wt8',\n    },\n  ],\n];\n\n/**\n * @component @name Plane\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcuOCAxOS4yIDE2IDExbDMuNS0zLjVDMjEgNiAyMS41IDQgMjEgM2MtMS0uNS0zIDAtNC41IDEuNUwxMyA4IDQuOCA2LjJjLS41LS4xLS45LjEtMS4xLjVsLS4zLjVjLS4yLjUtLjEgMSAuMyAxLjNMOSAxMmwtMiAzSDRsLTEgMSAzIDIgMiAzIDEtMXYtM2wzLTIgMy41IDUuM2MuMy40LjguNSAxLjMuM2wuNS0uMmMuNC0uMy42LS43LjUtMS4yeiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plane\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plane = createLucideIcon('plane', __iconNode);\n\nexport default Plane;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}