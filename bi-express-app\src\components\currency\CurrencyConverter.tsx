'use client'

import { useState, useEffect } from 'react'
import { convertCurrency } from '@/lib/currency'
import { ArrowRightLeft, Calculator } from 'lucide-react'

interface CurrencyConverterProps {
  className?: string
  defaultAmount?: number
  defaultFromCurrency?: 'NGN' | 'XOF'
}

export function CurrencyConverter({ 
  className = '',
  defaultAmount = 1000,
  defaultFromCurrency = 'NGN'
}: CurrencyConverterProps) {
  const [amount, setAmount] = useState<string>(defaultAmount.toString())
  const [fromCurrency, setFromCurrency] = useState<'NGN' | 'XOF'>(defaultFromCurrency)
  const [toCurrency, setToCurrency] = useState<'NGN' | 'XOF'>(
    defaultFromCurrency === 'NGN' ? 'XOF' : 'NGN'
  )
  const [convertedAmount, setConvertedAmount] = useState<number | null>(null)
  const [exchangeRate, setExchangeRate] = useState<number | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const performConversion = async () => {
    const numAmount = parseFloat(amount)
    
    if (isNaN(numAmount) || numAmount <= 0) {
      setConvertedAmount(null)
      setExchangeRate(null)
      setError('Montant invalide')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const result = await convertCurrency(numAmount, fromCurrency, toCurrency)
      setConvertedAmount(result.convertedAmount)
      setExchangeRate(result.exchangeRate)
    } catch (err) {
      setError('Erreur de conversion')
      console.error('Erreur conversion:', err)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (amount && parseFloat(amount) > 0) {
      performConversion()
    }
  }, [amount, fromCurrency, toCurrency])

  const swapCurrencies = () => {
    setFromCurrency(toCurrency)
    setToCurrency(fromCurrency)
  }

  const formatCurrency = (value: number, currency: 'NGN' | 'XOF') => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(value)
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      <div className="flex items-center mb-4">
        <Calculator className="h-5 w-5 text-blue-600 mr-2" />
        <h3 className="text-lg font-medium text-gray-900">
          Convertisseur de devises
        </h3>
      </div>

      <div className="space-y-4">
        {/* Montant source */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Montant à convertir
          </label>
          <div className="flex space-x-2">
            <input
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Entrez le montant"
              min="0"
              step="0.01"
            />
            <select
              value={fromCurrency}
              onChange={(e) => setFromCurrency(e.target.value as 'NGN' | 'XOF')}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="NGN">NGN (Naira)</option>
              <option value="XOF">XOF (CFA)</option>
            </select>
          </div>
        </div>

        {/* Bouton d'échange */}
        <div className="flex justify-center">
          <button
            onClick={swapCurrencies}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-full transition-colors"
            title="Inverser les devises"
          >
            <ArrowRightLeft className="h-5 w-5" />
          </button>
        </div>

        {/* Résultat */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Montant converti
          </label>
          <div className="flex space-x-2">
            <div className="flex-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-md">
              {isLoading ? (
                <div className="animate-pulse">
                  <div className="h-5 bg-gray-200 rounded w-3/4"></div>
                </div>
              ) : error ? (
                <span className="text-red-500 text-sm">{error}</span>
              ) : convertedAmount !== null ? (
                <span className="text-lg font-medium text-gray-900">
                  {formatCurrency(convertedAmount, toCurrency)}
                </span>
              ) : (
                <span className="text-gray-400">Résultat</span>
              )}
            </div>
            <div className="px-3 py-2 bg-gray-100 border border-gray-300 rounded-md text-gray-600">
              {toCurrency === 'NGN' ? 'NGN (Naira)' : 'XOF (CFA)'}
            </div>
          </div>
        </div>

        {/* Taux de change */}
        {exchangeRate && !error && (
          <div className="bg-blue-50 p-3 rounded-md">
            <div className="text-sm text-blue-800">
              <strong>Taux de change:</strong> 1 {fromCurrency} = {exchangeRate.toFixed(4)} {toCurrency}
            </div>
            {convertedAmount && (
              <div className="text-xs text-blue-600 mt-1">
                {formatCurrency(parseFloat(amount), fromCurrency)} = {formatCurrency(convertedAmount, toCurrency)}
              </div>
            )}
          </div>
        )}

        {/* Exemples rapides */}
        <div className="border-t border-gray-200 pt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Conversions rapides</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <button
              onClick={() => setAmount('1000')}
              className="p-2 text-left bg-gray-50 hover:bg-gray-100 rounded border"
            >
              1 000 {fromCurrency}
            </button>
            <button
              onClick={() => setAmount('5000')}
              className="p-2 text-left bg-gray-50 hover:bg-gray-100 rounded border"
            >
              5 000 {fromCurrency}
            </button>
            <button
              onClick={() => setAmount('10000')}
              className="p-2 text-left bg-gray-50 hover:bg-gray-100 rounded border"
            >
              10 000 {fromCurrency}
            </button>
            <button
              onClick={() => setAmount('50000')}
              className="p-2 text-left bg-gray-50 hover:bg-gray-100 rounded border"
            >
              50 000 {fromCurrency}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
