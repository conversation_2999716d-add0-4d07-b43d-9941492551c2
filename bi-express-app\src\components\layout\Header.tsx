'use client'

import { Menu } from 'lucide-react'
import { UserMenu } from '@/components/auth/UserMenu'
import { NotificationBell } from '@/components/notifications/NotificationBell'
import { GlobalSearch } from '@/components/search/GlobalSearch'

interface HeaderProps {
  onMenuClick?: () => void
}

export function Header({ onMenuClick }: HeaderProps) {
  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Côté gauche - Menu mobile et recherche */}
        <div className="flex items-center space-x-4 flex-1">
          {/* Bouton menu mobile */}
          <button
            onClick={onMenuClick}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
          >
            <Menu className="h-5 w-5" />
          </button>

          {/* Barre de recherche globale */}
          <GlobalSearch className="max-w-md w-full" />
        </div>

        {/* <PERSON>ôté droit - Notifications et menu utilisateur */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <NotificationBell />

          {/* Menu utilisateur */}
          <UserMenu />
        </div>
      </div>
    </header>
  )
}
