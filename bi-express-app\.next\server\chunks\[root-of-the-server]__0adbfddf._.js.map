{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/api/products/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const search = searchParams.get('search')\n    const category = searchParams.get('category')\n    const supplierId = searchParams.get('supplierId')\n\n    const where: any = {}\n\n    if (search) {\n      where.OR = [\n        { name: { contains: search, mode: 'insensitive' } },\n        { description: { contains: search, mode: 'insensitive' } },\n        { sku: { contains: search, mode: 'insensitive' } }\n      ]\n    }\n\n    if (category) {\n      where.category = category\n    }\n\n    if (supplierId) {\n      where.supplierId = supplierId\n    }\n\n    const products = await prisma.product.findMany({\n      where,\n      include: {\n        supplier: {\n          select: {\n            id: true,\n            name: true,\n            city: true\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'desc'\n      }\n    })\n\n    return NextResponse.json(products)\n  } catch (error) {\n    console.error('Erreur lors de la récupération des produits:', error)\n    return NextResponse.json(\n      { error: 'Erreur lors de la récupération des produits' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    \n    const {\n      name,\n      description,\n      category,\n      supplierId,\n      supplierPrice,\n      logisticsCost,\n      margin,\n      sellingPrice,\n      minQuantity,\n      weight,\n      dimensions,\n      sku,\n      tags,\n      isActive\n    } = body\n\n    // Validation des champs obligatoires\n    if (!name || !category || !supplierId || !supplierPrice) {\n      return NextResponse.json(\n        { error: 'Les champs nom, catégorie, fournisseur et prix fournisseur sont obligatoires' },\n        { status: 400 }\n      )\n    }\n\n    // Vérifier que le fournisseur existe\n    const supplier = await prisma.supplier.findUnique({\n      where: { id: supplierId }\n    })\n\n    if (!supplier) {\n      return NextResponse.json(\n        { error: 'Fournisseur non trouvé' },\n        { status: 404 }\n      )\n    }\n\n    // Générer un SKU si non fourni\n    const finalSku = sku || `${category.substring(0, 3).toUpperCase()}-${supplier.name.substring(0, 3).toUpperCase()}-${Math.random().toString(36).substring(2, 6).toUpperCase()}`\n\n    // Vérifier l'unicité du SKU\n    const existingSku = await prisma.product.findUnique({\n      where: { sku: finalSku }\n    })\n\n    if (existingSku) {\n      return NextResponse.json(\n        { error: 'Ce SKU existe déjà' },\n        { status: 400 }\n      )\n    }\n\n    const product = await prisma.product.create({\n      data: {\n        name,\n        description: description || '',\n        category,\n        supplierId,\n        supplierPrice: parseFloat(supplierPrice),\n        logisticsCost: parseFloat(logisticsCost) || 0,\n        margin: parseFloat(margin) || 30,\n        sellingPrice: parseFloat(sellingPrice),\n        minQuantity: parseInt(minQuantity) || 1,\n        weight: parseFloat(weight) || 0,\n        dimensions: dimensions || '',\n        sku: finalSku,\n        tags: tags || [],\n        isActive: isActive !== false\n      },\n      include: {\n        supplier: {\n          select: {\n            id: true,\n            name: true,\n            city: true\n          }\n        }\n      }\n    })\n\n    return NextResponse.json(product, { status: 201 })\n  } catch (error) {\n    console.error('Erreur lors de la création du produit:', error)\n    return NextResponse.json(\n      { error: 'Erreur lors de la création du produit' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,aAAa,aAAa,GAAG,CAAC;QAEpC,MAAM,QAAa,CAAC;QAEpB,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBAAE,MAAM;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBAClD;oBAAE,aAAa;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACzD;oBAAE,KAAK;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;aAClD;QACH;QAEA,IAAI,UAAU;YACZ,MAAM,QAAQ,GAAG;QACnB;QAEA,IAAI,YAAY;YACd,MAAM,UAAU,GAAG;QACrB;QAEA,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7C;YACA,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,MAAM;oBACR;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA8C,GACvD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,MAAM,EACJ,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,UAAU,EACV,aAAa,EACb,aAAa,EACb,MAAM,EACN,YAAY,EACZ,WAAW,EACX,MAAM,EACN,UAAU,EACV,GAAG,EACH,IAAI,EACJ,QAAQ,EACT,GAAG;QAEJ,qCAAqC;QACrC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,eAAe;YACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+E,GACxF;gBAAE,QAAQ;YAAI;QAElB;QAEA,qCAAqC;QACrC,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE,IAAI;YAAW;QAC1B;QAEA,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,MAAM,WAAW,OAAO,GAAG,SAAS,SAAS,CAAC,GAAG,GAAG,WAAW,GAAG,CAAC,EAAE,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,WAAW,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW,IAAI;QAE9K,4BAA4B;QAC5B,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAClD,OAAO;gBAAE,KAAK;YAAS;QACzB;QAEA,IAAI,aAAa;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqB,GAC9B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,MAAM;gBACJ;gBACA,aAAa,eAAe;gBAC5B;gBACA;gBACA,eAAe,WAAW;gBAC1B,eAAe,WAAW,kBAAkB;gBAC5C,QAAQ,WAAW,WAAW;gBAC9B,cAAc,WAAW;gBACzB,aAAa,SAAS,gBAAgB;gBACtC,QAAQ,WAAW,WAAW;gBAC9B,YAAY,cAAc;gBAC1B,KAAK;gBACL,MAAM,QAAQ,EAAE;gBAChB,UAAU,aAAa;YACzB;YACA,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,MAAM;oBACR;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,SAAS;YAAE,QAAQ;QAAI;IAClD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwC,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}