{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/reports/page.tsx"], "sourcesContent": ["export default function ReportsPage() {\n  return (\n    <div className=\"p-6\">\n      <div className=\"border-b border-gray-200 pb-4 mb-6\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">Rapports</h1>\n        <p className=\"text-gray-600 mt-1\">\n          Analyses et statistiques de votre activité\n        </p>\n      </div>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {/* Rapport des ventes */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center mr-3\">\n              <span className=\"text-green-600 text-lg\">📊</span>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900\">Rapport des ventes</h3>\n              <p className=\"text-sm text-gray-600\">Analyse des performances</p>\n            </div>\n          </div>\n          <div className=\"text-sm text-gray-500 mb-4\">\n            <ul className=\"space-y-1\">\n              <li>• Chiffre d'affaires par période</li>\n              <li>• Évolution des bénéfices</li>\n              <li>• Top produits vendus</li>\n              <li>• Performance par fournisseur</li>\n            </ul>\n          </div>\n          <button className=\"w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors\">\n            Générer le rapport\n          </button>\n        </div>\n\n        {/* Rapport des stocks */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3\">\n              <span className=\"text-blue-600 text-lg\">📦</span>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900\">Rapport des stocks</h3>\n              <p className=\"text-sm text-gray-600\">État des inventaires</p>\n            </div>\n          </div>\n          <div className=\"text-sm text-gray-500 mb-4\">\n            <ul className=\"space-y-1\">\n              <li>• Valeur totale du stock</li>\n              <li>• Produits en rupture</li>\n              <li>• Rotation des stocks</li>\n              <li>• Alertes de réapprovisionnement</li>\n            </ul>\n          </div>\n          <button className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\">\n            Générer le rapport\n          </button>\n        </div>\n\n        {/* Rapport logistique */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3\">\n              <span className=\"text-purple-600 text-lg\">🚛</span>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900\">Rapport logistique</h3>\n              <p className=\"text-sm text-gray-600\">Performance des livraisons</p>\n            </div>\n          </div>\n          <div className=\"text-sm text-gray-500 mb-4\">\n            <ul className=\"space-y-1\">\n              <li>• Délais de livraison moyens</li>\n              <li>• Coûts de transport</li>\n              <li>• Taux de livraison à temps</li>\n              <li>• Comparaison routier vs aérien</li>\n            </ul>\n          </div>\n          <button className=\"w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors\">\n            Générer le rapport\n          </button>\n        </div>\n\n        {/* Rapport clients */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"h-10 w-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3\">\n              <span className=\"text-orange-600 text-lg\">👥</span>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900\">Rapport clients</h3>\n              <p className=\"text-sm text-gray-600\">Analyse de la clientèle</p>\n            </div>\n          </div>\n          <div className=\"text-sm text-gray-500 mb-4\">\n            <ul className=\"space-y-1\">\n              <li>• Clients les plus actifs</li>\n              <li>• Panier moyen par client</li>\n              <li>• Fréquence de commande</li>\n              <li>• Fidélisation client</li>\n            </ul>\n          </div>\n          <button className=\"w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors\">\n            Générer le rapport\n          </button>\n        </div>\n\n        {/* Rapport financier */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"h-10 w-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-3\">\n              <span className=\"text-indigo-600 text-lg\">💰</span>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900\">Rapport financier</h3>\n              <p className=\"text-sm text-gray-600\">Analyse des finances</p>\n            </div>\n          </div>\n          <div className=\"text-sm text-gray-500 mb-4\">\n            <ul className=\"space-y-1\">\n              <li>• Compte de résultat</li>\n              <li>• Marges par catégorie</li>\n              <li>• Évolution des coûts</li>\n              <li>• Rentabilité par ville</li>\n            </ul>\n          </div>\n          <button className=\"w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors\">\n            Générer le rapport\n          </button>\n        </div>\n\n        {/* Rapport personnalisé */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"h-10 w-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3\">\n              <span className=\"text-gray-600 text-lg\">⚙️</span>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900\">Rapport personnalisé</h3>\n              <p className=\"text-sm text-gray-600\">Créer un rapport sur mesure</p>\n            </div>\n          </div>\n          <div className=\"text-sm text-gray-500 mb-4\">\n            <ul className=\"space-y-1\">\n              <li>• Sélection des métriques</li>\n              <li>• Période personnalisée</li>\n              <li>• Filtres avancés</li>\n              <li>• Export Excel/PDF</li>\n            </ul>\n          </div>\n          <button className=\"w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors\">\n            Créer un rapport\n          </button>\n        </div>\n      </div>\n\n      <div className=\"mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n          Rapports automatiques\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n            <div>\n              <p className=\"font-medium text-gray-900\">Rapport hebdomadaire</p>\n              <p className=\"text-sm text-gray-600\">Envoyé chaque lundi matin</p>\n            </div>\n            <label className=\"relative inline-flex items-center cursor-pointer\">\n              <input type=\"checkbox\" className=\"sr-only peer\" defaultChecked />\n              <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n            </label>\n          </div>\n          \n          <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n            <div>\n              <p className=\"font-medium text-gray-900\">Rapport mensuel</p>\n              <p className=\"text-sm text-gray-600\">Envoyé le 1er de chaque mois</p>\n            </div>\n            <label className=\"relative inline-flex items-center cursor-pointer\">\n              <input type=\"checkbox\" className=\"sr-only peer\" />\n              <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n            </label>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAKpC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;kDAE3C,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;0CAGR,8OAAC;gCAAO,WAAU;0CAA2F;;;;;;;;;;;;kCAM/G,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;kDAE1C,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;0CAGR,8OAAC;gCAAO,WAAU;0CAAyF;;;;;;;;;;;;kCAM7G,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA0B;;;;;;;;;;;kDAE5C,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;0CAGR,8OAAC;gCAAO,WAAU;0CAA6F;;;;;;;;;;;;kCAMjH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA0B;;;;;;;;;;;kDAE5C,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;0CAGR,8OAAC;gCAAO,WAAU;0CAA6F;;;;;;;;;;;;kCAMjH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA0B;;;;;;;;;;;kDAE5C,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;0CAGR,8OAAC;gCAAO,WAAU;0CAA6F;;;;;;;;;;;;kCAMjH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;kDAE1C,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;0CAGR,8OAAC;gCAAO,WAAU;0CAAyF;;;;;;;;;;;;;;;;;;0BAM/G,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4B;;;;;;0DACzC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;gDAAe,cAAc;;;;;;0DAC9D,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;0CAInB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4B;;;;;;0DACzC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;;;;;;0DACjC,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B", "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,IAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}