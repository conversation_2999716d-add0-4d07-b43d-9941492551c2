// Types pour l'application Bi-Express

export type SupplierCity = 'LAGOS' | 'ABUJA' | 'KANO';
export type ProductCategory = 'TISSUS' | 'COSMETIQUES' | 'MECHES';
export type OrderStatus = 'PENDING' | 'CONFIRMED' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED';
export type TransportMode = 'ROAD' | 'AIR_EXPRESS';
export type ShipmentStatus = 'PENDING' | 'PICKED_UP' | 'IN_TRANSIT' | 'CUSTOMS' | 'OUT_FOR_DELIVERY' | 'DELIVERED' | 'DELAYED' | 'CANCELLED';
export type TrackingEventType = 'PICKUP_SCHEDULED' | 'PICKED_UP' | 'DEPARTED' | 'IN_TRANSIT' | 'ARRIVED_HUB' | 'CUSTOMS_CLEARANCE' | 'OUT_FOR_DELIVERY' | 'DELIVERED' | 'DELIVERY_FAILED' | 'DELAYED';

export interface DashboardStats {
  totalRevenue: number;
  totalOrders: number;
  totalProfit: number;
  averageMargin: number;
  topProducts: ProductSales[];
  recentOrders: OrderSummary[];
  supplierPerformance: SupplierStats[];
}

export interface ProductSales {
  id: string;
  name: string;
  category: ProductCategory;
  totalSold: number;
  revenue: number;
  profit: number;
}

export interface OrderSummary {
  id: string;
  orderNumber: string;
  customerName: string;
  totalAmount: number;
  status: OrderStatus;
  orderDate: Date;
}

export interface SupplierStats {
  id: string;
  name: string;
  city: SupplierCity;
  totalOrders: number;
  averageRating: number;
  totalRevenue: number;
}

export interface ProductFormData {
  name: string;
  description?: string;
  category: ProductCategory;
  supplierId: string;
  supplierPrice: number;
  margin: number;
  stockQuantity: number;
  minStockAlert: number;
  
  // Spécificités selon la catégorie
  fabricType?: string;
  width?: number;
  color?: string;
  pattern?: string;
  brand?: string;
  volume?: number;
  origin?: string;
  length?: number;
  texture?: string;
  hairType?: string;
  
  weight?: number;
  dimensions?: string;
  imageUrl?: string;
}

export interface OrderFormData {
  customerId: string;
  supplierId: string;
  transportMode: TransportMode;
  deliveryAddress: string;
  items: OrderItemData[];
}

export interface OrderItemData {
  productId: string;
  quantity: number;
}

// Interfaces pour la logistique avancée
export interface Carrier {
  id: string;
  name: string;
  phone: string;
  email?: string;
  address: string;
  city: SupplierCity;
  transportModes: string;
  capacity?: number;
  rating: number;
  onTimeRate: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Shipment {
  id: string;
  trackingNumber: string;
  orderId: string;
  carrierId: string;
  status: ShipmentStatus;
  transportMode: TransportMode;
  originCity: SupplierCity;
  destinationCity: string;
  currentLocation?: string;
  pickupDate?: Date;
  departureDate?: Date;
  arrivalDate?: Date;
  deliveryDate?: Date;
  estimatedDelivery: Date;
  weight: number;
  transportCost: number;
  fuelSurcharge?: number;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  carrier: Carrier;
  trackingEvents: TrackingEvent[];
}

export interface TrackingEvent {
  id: string;
  shipmentId: string;
  eventType: TrackingEventType;
  location: string;
  description: string;
  timestamp: Date;
}

export interface CarrierEvaluation {
  id: string;
  carrierId: string;
  shipmentId?: string;
  punctuality: number;
  condition: number;
  communication: number;
  overall: number;
  comment?: string;
  createdAt: Date;
}

export interface LogisticsStats {
  totalShipments: number;
  inTransitShipments: number;
  deliveredShipments: number;
  averageDeliveryTime: number;
  onTimeDeliveryRate: number;
  totalTransportCosts: number;
  carrierPerformance: CarrierPerformance[];
}

export interface CarrierPerformance {
  carrierId: string;
  carrierName: string;
  totalShipments: number;
  onTimeRate: number;
  averageRating: number;
  totalCost: number;
}

export interface FilterOptions {
  category?: ProductCategory;
  city?: SupplierCity;
  status?: OrderStatus;
  dateFrom?: Date;
  dateTo?: Date;
  minPrice?: number;
  maxPrice?: number;
}
