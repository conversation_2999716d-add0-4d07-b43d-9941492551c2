// Types pour l'application Bi-Express

export type SupplierCity = 'LAGOS' | 'ABUJA' | 'KANO';
export type ProductCategory = 'TISSUS' | 'COSMETIQUES' | 'MECHES';
export type OrderStatus = 'PENDING' | 'CONFIRMED' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED';
export type TransportMode = 'ROAD' | 'AIR_EXPRESS';

export interface DashboardStats {
  totalRevenue: number;
  totalOrders: number;
  totalProfit: number;
  averageMargin: number;
  topProducts: ProductSales[];
  recentOrders: OrderSummary[];
  supplierPerformance: SupplierStats[];
}

export interface ProductSales {
  id: string;
  name: string;
  category: ProductCategory;
  totalSold: number;
  revenue: number;
  profit: number;
}

export interface OrderSummary {
  id: string;
  orderNumber: string;
  customerName: string;
  totalAmount: number;
  status: OrderStatus;
  orderDate: Date;
}

export interface SupplierStats {
  id: string;
  name: string;
  city: SupplierCity;
  totalOrders: number;
  averageRating: number;
  totalRevenue: number;
}

export interface ProductFormData {
  name: string;
  description?: string;
  category: ProductCategory;
  supplierId: string;
  supplierPrice: number;
  margin: number;
  stockQuantity: number;
  minStockAlert: number;
  
  // Spécificités selon la catégorie
  fabricType?: string;
  width?: number;
  color?: string;
  pattern?: string;
  brand?: string;
  volume?: number;
  origin?: string;
  length?: number;
  texture?: string;
  hairType?: string;
  
  weight?: number;
  dimensions?: string;
  imageUrl?: string;
}

export interface OrderFormData {
  customerId: string;
  supplierId: string;
  transportMode: TransportMode;
  deliveryAddress: string;
  items: OrderItemData[];
}

export interface OrderItemData {
  productId: string;
  quantity: number;
}

export interface FilterOptions {
  category?: ProductCategory;
  city?: SupplierCity;
  status?: OrderStatus;
  dateFrom?: Date;
  dateTo?: Date;
  minPrice?: number;
  maxPrice?: number;
}
