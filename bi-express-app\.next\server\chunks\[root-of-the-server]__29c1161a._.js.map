{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/api/carriers/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const transportMode = searchParams.get('transportMode')\n    const city = searchParams.get('city')\n    const search = searchParams.get('search')\n\n    const where: any = {}\n\n    if (transportMode) {\n      where.transportModes = {\n        has: transportMode\n      }\n    }\n\n    if (city) {\n      where.city = city\n    }\n\n    if (search) {\n      where.OR = [\n        { name: { contains: search, mode: 'insensitive' } },\n        { email: { contains: search, mode: 'insensitive' } },\n        { phone: { contains: search, mode: 'insensitive' } }\n      ]\n    }\n\n    const carriers = await prisma.carrier.findMany({\n      where,\n      include: {\n        shipments: {\n          select: {\n            id: true,\n            status: true,\n            totalTransportCost: true,\n            createdAt: true\n          },\n          orderBy: {\n            createdAt: 'desc'\n          },\n          take: 5\n        },\n        _count: {\n          select: {\n            shipments: true\n          }\n        }\n      },\n      orderBy: {\n        rating: 'desc'\n      }\n    })\n\n    return NextResponse.json(carriers)\n  } catch (error) {\n    console.error('Erreur lors de la récupération des transporteurs:', error)\n    return NextResponse.json(\n      { error: 'Erreur lors de la récupération des transporteurs' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    \n    const {\n      name,\n      email,\n      phone,\n      address,\n      city,\n      country,\n      transportModes,\n      vehicleTypes,\n      capacity,\n      pricePerKg,\n      rating,\n      isActive,\n      notes\n    } = body\n\n    // Validation des champs obligatoires\n    if (!name || !phone || !city || !transportModes || transportModes.length === 0) {\n      return NextResponse.json(\n        { error: 'Les champs nom, téléphone, ville et modes de transport sont obligatoires' },\n        { status: 400 }\n      )\n    }\n\n    // Vérifier l'unicité de l'email s'il est fourni\n    if (email) {\n      const existingEmail = await prisma.carrier.findFirst({\n        where: { email }\n      })\n\n      if (existingEmail) {\n        return NextResponse.json(\n          { error: 'Cette adresse email est déjà utilisée' },\n          { status: 400 }\n        )\n      }\n    }\n\n    // Vérifier l'unicité du téléphone\n    const existingPhone = await prisma.carrier.findFirst({\n      where: { phone }\n    })\n\n    if (existingPhone) {\n      return NextResponse.json(\n        { error: 'Ce numéro de téléphone est déjà utilisé' },\n        { status: 400 }\n      )\n    }\n\n    const carrier = await prisma.carrier.create({\n      data: {\n        name,\n        email: email || null,\n        phone,\n        address: address || '',\n        city,\n        transportModes,\n        capacity: capacity ? parseFloat(capacity) : null,\n        rating: rating ? parseFloat(rating) : 5.0,\n        isActive: isActive !== false\n      }\n    })\n\n    return NextResponse.json(carrier, { status: 201 })\n  } catch (error) {\n    console.error('Erreur lors de la création du transporteur:', error)\n    return NextResponse.json(\n      { error: 'Erreur lors de la création du transporteur' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,gBAAgB,aAAa,GAAG,CAAC;QACvC,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,MAAM,QAAa,CAAC;QAEpB,IAAI,eAAe;YACjB,MAAM,cAAc,GAAG;gBACrB,KAAK;YACP;QACF;QAEA,IAAI,MAAM;YACR,MAAM,IAAI,GAAG;QACf;QAEA,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBAAE,MAAM;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBAClD;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACnD;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;aACpD;QACH;QAEA,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7C;YACA,SAAS;gBACP,WAAW;oBACT,QAAQ;wBACN,IAAI;wBACJ,QAAQ;wBACR,oBAAoB;wBACpB,WAAW;oBACb;oBACA,SAAS;wBACP,WAAW;oBACb;oBACA,MAAM;gBACR;gBACA,QAAQ;oBACN,QAAQ;wBACN,WAAW;oBACb;gBACF;YACF;YACA,SAAS;gBACP,QAAQ;YACV;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QACnE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAmD,GAC5D;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,MAAM,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,OAAO,EACP,IAAI,EACJ,OAAO,EACP,cAAc,EACd,YAAY,EACZ,QAAQ,EACR,UAAU,EACV,MAAM,EACN,QAAQ,EACR,KAAK,EACN,GAAG;QAEJ,qCAAqC;QACrC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,kBAAkB,eAAe,MAAM,KAAK,GAAG;YAC9E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2E,GACpF;gBAAE,QAAQ;YAAI;QAElB;QAEA,gDAAgD;QAChD,IAAI,OAAO;YACT,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,SAAS,CAAC;gBACnD,OAAO;oBAAE;gBAAM;YACjB;YAEA,IAAI,eAAe;gBACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAwC,GACjD;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,kCAAkC;QAClC,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YACnD,OAAO;gBAAE;YAAM;QACjB;QAEA,IAAI,eAAe;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0C,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,MAAM;gBACJ;gBACA,OAAO,SAAS;gBAChB;gBACA,SAAS,WAAW;gBACpB;gBACA;gBACA,UAAU,WAAW,WAAW,YAAY;gBAC5C,QAAQ,SAAS,WAAW,UAAU;gBACtC,UAAU,aAAa;YACzB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,SAAS;YAAE,QAAQ;QAAI;IAClD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA6C,GACtD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}