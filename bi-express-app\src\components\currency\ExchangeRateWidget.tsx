'use client'

import { useState, useEffect } from 'react'
import { getCurrentRates, clearExchangeRateCache } from '@/lib/currency'
import { RefreshCw, TrendingUp, TrendingDown, Clock } from 'lucide-react'

interface ExchangeRateWidgetProps {
  className?: string
  showRefreshButton?: boolean
}

export function ExchangeRateWidget({ 
  className = '', 
  showRefreshButton = true 
}: ExchangeRateWidgetProps) {
  const [rates, setRates] = useState<{
    ngnToXof: number
    xofToNgn: number
    lastUpdated: Date
  } | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchRates = async (forceRefresh = false) => {
    try {
      setError(null)
      if (forceRefresh) {
        setIsRefreshing(true)
        clearExchangeRateCache()
      } else {
        setIsLoading(true)
      }

      const currentRates = await getCurrentRates()
      setRates(currentRates)
    } catch (err) {
      setError('Erreur lors du chargement des taux')
      console.error('Erreur taux de change:', err)
    } finally {
      setIsLoading(false)
      setIsRefreshing(false)
    }
  }

  useEffect(() => {
    fetchRates()
    
    // Actualiser automatiquement toutes les 30 minutes
    const interval = setInterval(() => {
      fetchRates(true)
    }, 30 * 60 * 1000)

    return () => clearInterval(interval)
  }, [])

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(date)
  }

  const formatRate = (rate: number) => {
    return rate.toFixed(4)
  }

  if (isLoading && !rates) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-6 bg-gray-200 rounded w-3/4 mb-1"></div>
          <div className="h-6 bg-gray-200 rounded w-3/4"></div>
        </div>
      </div>
    )
  }

  if (error && !rates) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-red-200 p-4 ${className}`}>
        <div className="text-red-600 text-sm">
          <p className="font-medium">Erreur de chargement</p>
          <p>{error}</p>
          <button
            onClick={() => fetchRates(true)}
            className="mt-2 text-xs underline hover:no-underline"
          >
            Réessayer
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900 flex items-center">
          <TrendingUp className="h-4 w-4 mr-2 text-green-600" />
          Taux de change
        </h3>
        {showRefreshButton && (
          <button
            onClick={() => fetchRates(true)}
            disabled={isRefreshing}
            className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
            title="Actualiser les taux"
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          </button>
        )}
      </div>

      {rates && (
        <div className="space-y-3">
          {/* NGN vers XOF */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">1 NGN</span>
              <span className="text-xs text-gray-400">→</span>
              <span className="text-sm font-medium text-blue-600">
                {formatRate(rates.ngnToXof)} XOF
              </span>
            </div>
            <TrendingUp className="h-3 w-3 text-green-500" />
          </div>

          {/* XOF vers NGN */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">1 XOF</span>
              <span className="text-xs text-gray-400">→</span>
              <span className="text-sm font-medium text-blue-600">
                {formatRate(rates.xofToNgn)} NGN
              </span>
            </div>
            <TrendingDown className="h-3 w-3 text-red-500" />
          </div>

          {/* Dernière mise à jour */}
          <div className="pt-2 border-t border-gray-100">
            <div className="flex items-center text-xs text-gray-500">
              <Clock className="h-3 w-3 mr-1" />
              <span>Mis à jour à {formatTime(rates.lastUpdated)}</span>
            </div>
          </div>
        </div>
      )}

      {error && rates && (
        <div className="mt-2 text-xs text-amber-600 bg-amber-50 p-2 rounded">
          ⚠️ Utilisation des taux en cache - {error}
        </div>
      )}
    </div>
  )
}
