{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: 'NGN' | 'XOF' = 'XOF'): string {\n  const symbols = {\n    NGN: '₦',\n    XOF: 'CFA'\n  }\n  \n  return `${amount.toLocaleString('fr-FR')} ${symbols[currency]}`\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric'\n  }).format(date)\n}\n\nexport function formatDateTime(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  }).format(date)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAA0B,KAAK;IAC5E,MAAM,UAAU;QACd,KAAK;QACL,KAAK;IACP;IAEA,OAAO,GAAG,OAAO,cAAc,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,SAAS,EAAE;AACjE;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;IACR,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAU;IACvC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/Badge.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface BadgeProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function Badge({ children, className }: BadgeProps) {\n  return (\n    <span className={cn(\n      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',\n      className\n    )}>\n      {children}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOO,SAAS,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAc;IACvD,qBACE,8OAAC;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,2EACA;kBAEC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/orders/OrdersList.tsx"], "sourcesContent": ["import { prisma } from '@/lib/prisma'\nimport { formatCurrency, formatDateTime } from '@/lib/utils'\nimport { Badge } from '@/components/ui/Badge'\nimport { \n  Package, \n  Truck, \n  Clock, \n  CheckCircle, \n  XCircle,\n  Eye,\n  Edit,\n  MapPin,\n  Calendar\n} from 'lucide-react'\n\nasync function getOrders() {\n  return await prisma.order.findMany({\n    include: {\n      customer: true,\n      supplier: true,\n      orderItems: {\n        include: {\n          product: true\n        }\n      }\n    },\n    orderBy: { createdAt: 'desc' }\n  })\n}\n\nconst statusColors = {\n  PENDING: 'bg-yellow-100 text-yellow-800',\n  CONFIRMED: 'bg-blue-100 text-blue-800',\n  SHIPPED: 'bg-purple-100 text-purple-800',\n  DELIVERED: 'bg-green-100 text-green-800',\n  CANCELLED: 'bg-red-100 text-red-800'\n}\n\nconst statusLabels = {\n  PENDING: 'En attente',\n  CONFIRMED: 'Confirmée',\n  SHIPPED: 'Expédiée',\n  DELIVERED: 'Livrée',\n  CANCELLED: 'Annulée'\n}\n\nconst statusIcons = {\n  PENDING: Clock,\n  CONFIRMED: CheckCircle,\n  SHIPPED: Truck,\n  DELIVERED: CheckCircle,\n  CANCELLED: XCircle\n}\n\nconst transportModeLabels = {\n  ROAD: 'Routier (5-7j)',\n  AIR: 'Aérien (24-48h)'\n}\n\nconst transportModeColors = {\n  ROAD: 'bg-blue-50 text-blue-700',\n  AIR: 'bg-purple-50 text-purple-700'\n}\n\nfunction getDeliveryEstimate(createdAt: Date, transportMode: string) {\n  const created = new Date(createdAt)\n  const days = transportMode === 'AIR' ? 2 : 6\n  const estimate = new Date(created.getTime() + days * 24 * 60 * 60 * 1000)\n  return estimate\n}\n\nexport async function OrdersList() {\n  const orders = await getOrders()\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n      <div className=\"overflow-x-auto\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Commande\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Client\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Fournisseur\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Transport\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Montant\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Statut\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Livraison\n              </th>\n              <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Actions\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {orders.map((order) => {\n              const StatusIcon = statusIcons[order.status]\n              const deliveryEstimate = getDeliveryEstimate(order.createdAt, order.transportMode)\n              const isOverdue = order.status === 'SHIPPED' && new Date() > deliveryEstimate\n\n              return (\n                <tr key={order.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0 h-10 w-10\">\n                        <div className=\"h-10 w-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center\">\n                          <Package className=\"h-5 w-5 text-white\" />\n                        </div>\n                      </div>\n                      <div className=\"ml-4\">\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {order.orderNumber}\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          {order.orderItems.length} article{order.orderItems.length > 1 ? 's' : ''}\n                        </div>\n                        <div className=\"text-xs text-gray-400\">\n                          {formatDateTime(order.createdAt)}\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      {order.customer.name}\n                    </div>\n                    <div className=\"text-sm text-gray-500\">\n                      {order.customer.phone}\n                    </div>\n                    <div className=\"flex items-center text-xs text-gray-400 mt-1\">\n                      <MapPin className=\"h-3 w-3 mr-1\" />\n                      Dakar, Sénégal\n                    </div>\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      {order.supplier.name}\n                    </div>\n                    <div className=\"flex items-center text-xs text-gray-500 mt-1\">\n                      <MapPin className=\"h-3 w-3 mr-1\" />\n                      {order.supplier.city}, Nigeria\n                    </div>\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <Badge className={transportModeColors[order.transportMode]}>\n                      {transportModeLabels[order.transportMode]}\n                    </Badge>\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      {formatCurrency(order.totalAmount)}\n                    </div>\n                    <div className=\"text-sm text-green-600\">\n                      +{formatCurrency(order.totalProfit)} bénéfice\n                    </div>\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <StatusIcon className={`h-4 w-4 mr-2 ${\n                        order.status === 'DELIVERED' ? 'text-green-500' :\n                        order.status === 'CANCELLED' ? 'text-red-500' :\n                        order.status === 'SHIPPED' ? 'text-purple-500' :\n                        order.status === 'CONFIRMED' ? 'text-blue-500' :\n                        'text-yellow-500'\n                      }`} />\n                      <Badge className={statusColors[order.status]}>\n                        {statusLabels[order.status]}\n                      </Badge>\n                    </div>\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    {order.status === 'DELIVERED' ? (\n                      <div className=\"text-sm text-green-600 font-medium\">\n                        Livrée\n                      </div>\n                    ) : order.status === 'CANCELLED' ? (\n                      <div className=\"text-sm text-red-600\">\n                        Annulée\n                      </div>\n                    ) : (\n                      <div>\n                        <div className={`text-sm ${isOverdue ? 'text-red-600 font-medium' : 'text-gray-900'}`}>\n                          {formatDateTime(deliveryEstimate).split(' ')[0]}\n                        </div>\n                        <div className=\"flex items-center text-xs text-gray-500 mt-1\">\n                          <Calendar className=\"h-3 w-3 mr-1\" />\n                          {isOverdue ? 'En retard' : 'Estimée'}\n                        </div>\n                      </div>\n                    )}\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                    <div className=\"flex items-center justify-end space-x-2\">\n                      <button className=\"text-blue-600 hover:text-blue-900\">\n                        <Eye className=\"h-4 w-4\" />\n                      </button>\n                      <button className=\"text-gray-600 hover:text-gray-900\">\n                        <Edit className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              )\n            })}\n          </tbody>\n        </table>\n      </div>\n      \n      {orders.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Package className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n          <p className=\"text-gray-500 text-lg\">Aucune commande trouvée</p>\n          <p className=\"text-gray-400 text-sm mt-1\">\n            Les nouvelles commandes apparaîtront ici\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAYA,eAAe;IACb,OAAO,MAAM,oHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;QACjC,SAAS;YACP,UAAU;YACV,UAAU;YACV,YAAY;gBACV,SAAS;oBACP,SAAS;gBACX;YACF;QACF;QACA,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAEA,MAAM,eAAe;IACnB,SAAS;IACT,WAAW;IACX,SAAS;IACT,WAAW;IACX,WAAW;AACb;AAEA,MAAM,eAAe;IACnB,SAAS;IACT,WAAW;IACX,SAAS;IACT,WAAW;IACX,WAAW;AACb;AAEA,MAAM,cAAc;IAClB,SAAS,oMAAA,CAAA,QAAK;IACd,WAAW,2NAAA,CAAA,cAAW;IACtB,SAAS,oMAAA,CAAA,QAAK;IACd,WAAW,2NAAA,CAAA,cAAW;IACtB,WAAW,4MAAA,CAAA,UAAO;AACpB;AAEA,MAAM,sBAAsB;IAC1B,MAAM;IACN,KAAK;AACP;AAEA,MAAM,sBAAsB;IAC1B,MAAM;IACN,KAAK;AACP;AAEA,SAAS,oBAAoB,SAAe,EAAE,aAAqB;IACjE,MAAM,UAAU,IAAI,KAAK;IACzB,MAAM,OAAO,kBAAkB,QAAQ,IAAI;IAC3C,MAAM,WAAW,IAAI,KAAK,QAAQ,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK;IACpE,OAAO;AACT;AAEO,eAAe;IACpB,MAAM,SAAS,MAAM;IAErB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,WAAU;sCACf,cAAA,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAkF;;;;;;;;;;;;;;;;;sCAKpG,8OAAC;4BAAM,WAAU;sCACd,OAAO,GAAG,CAAC,CAAC;gCACX,MAAM,aAAa,WAAW,CAAC,MAAM,MAAM,CAAC;gCAC5C,MAAM,mBAAmB,oBAAoB,MAAM,SAAS,EAAE,MAAM,aAAa;gCACjF,MAAM,YAAY,MAAM,MAAM,KAAK,aAAa,IAAI,SAAS;gCAE7D,qBACE,8OAAC;oCAAkB,WAAU;;sDAC3B,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAGvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,MAAM,WAAW;;;;;;0EAEpB,8OAAC;gEAAI,WAAU;;oEACZ,MAAM,UAAU,CAAC,MAAM;oEAAC;oEAAS,MAAM,UAAU,CAAC,MAAM,GAAG,IAAI,MAAM;;;;;;;0EAExE,8OAAC;gEAAI,WAAU;0EACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,SAAS;;;;;;;;;;;;;;;;;;;;;;;sDAMvC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;8DACZ,MAAM,QAAQ,CAAC,IAAI;;;;;;8DAEtB,8OAAC;oDAAI,WAAU;8DACZ,MAAM,QAAQ,CAAC,KAAK;;;;;;8DAEvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAKvC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;8DACZ,MAAM,QAAQ,CAAC,IAAI;;;;;;8DAEtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDACjB,MAAM,QAAQ,CAAC,IAAI;wDAAC;;;;;;;;;;;;;sDAIzB,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAW,mBAAmB,CAAC,MAAM,aAAa,CAAC;0DACvD,mBAAmB,CAAC,MAAM,aAAa,CAAC;;;;;;;;;;;sDAI7C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,WAAW;;;;;;8DAEnC,8OAAC;oDAAI,WAAU;;wDAAyB;wDACpC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,WAAW;wDAAE;;;;;;;;;;;;;sDAIxC,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAW,WAAW,CAAC,aAAa,EACnC,MAAM,MAAM,KAAK,cAAc,mBAC/B,MAAM,MAAM,KAAK,cAAc,iBAC/B,MAAM,MAAM,KAAK,YAAY,oBAC7B,MAAM,MAAM,KAAK,cAAc,kBAC/B,mBACA;;;;;;kEACF,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAW,YAAY,CAAC,MAAM,MAAM,CAAC;kEACzC,YAAY,CAAC,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;;;sDAKjC,8OAAC;4CAAG,WAAU;sDACX,MAAM,MAAM,KAAK,4BAChB,8OAAC;gDAAI,WAAU;0DAAqC;;;;;uDAGlD,MAAM,MAAM,KAAK,4BACnB,8OAAC;gDAAI,WAAU;0DAAuB;;;;;qEAItC,8OAAC;;kEACC,8OAAC;wDAAI,WAAW,CAAC,QAAQ,EAAE,YAAY,6BAA6B,iBAAiB;kEAClF,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,kBAAkB,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;kEAEjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,YAAY,cAAc;;;;;;;;;;;;;;;;;;sDAMnC,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAO,WAAU;kEAChB,cAAA,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;kEAEjB,8OAAC;wDAAO,WAAU;kEAChB,cAAA,8OAAC,2MAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mCAvGf,MAAM,EAAE;;;;;4BA6GrB;;;;;;;;;;;;;;;;;YAKL,OAAO,MAAM,KAAK,mBACjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wMAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/orders/OrderFilters.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const OrderFilters = registerClientReference(\n    function() { throw new Error(\"Attempted to call OrderFilters() from the server but OrderFilters is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/orders/OrderFilters.tsx <module evaluation>\",\n    \"OrderFilters\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,wEACA", "debugId": null}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/orders/OrderFilters.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const OrderFilters = registerClientReference(\n    function() { throw new Error(\"Attempted to call OrderFilters() from the server but OrderFilters is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/orders/OrderFilters.tsx\",\n    \"OrderFilters\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,oDACA", "debugId": null}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 711, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/orders/CreateOrderButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CreateOrderButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call CreateOrderButton() from the server but CreateOrderButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/orders/CreateOrderButton.tsx <module evaluation>\",\n    \"CreateOrderButton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,6EACA", "debugId": null}}, {"offset": {"line": 725, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/orders/CreateOrderButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CreateOrderButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call CreateOrderButton() from the server but CreateOrderButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/orders/CreateOrderButton.tsx\",\n    \"CreateOrderButton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,yDACA", "debugId": null}}, {"offset": {"line": 739, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 749, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/orders/OrderStats.tsx"], "sourcesContent": ["import { prisma } from '@/lib/prisma'\nimport { formatCurrency } from '@/lib/utils'\nimport { \n  ShoppingCart, \n  Clock, \n  Truck, \n  CheckCircle,\n  TrendingUp,\n  DollarSign\n} from 'lucide-react'\n\nasync function getOrderStats() {\n  const [\n    totalOrders,\n    pendingOrders,\n    shippedOrders,\n    deliveredOrders,\n    orders\n  ] = await Promise.all([\n    prisma.order.count(),\n    prisma.order.count({ where: { status: 'PENDING' } }),\n    prisma.order.count({ where: { status: 'SHIPPED' } }),\n    prisma.order.count({ where: { status: 'DELIVERED' } }),\n    prisma.order.findMany({\n      select: {\n        totalAmount: true,\n        totalProfit: true,\n        transportMode: true,\n        status: true\n      }\n    })\n  ])\n\n  // Calculs des statistiques financières\n  const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0)\n  const totalProfit = orders.reduce((sum, order) => sum + order.totalProfit, 0)\n  \n  // Statistiques par mode de transport\n  const roadOrders = orders.filter(order => order.transportMode === 'ROAD').length\n  const airOrders = orders.filter(order => order.transportMode === 'AIR').length\n\n  return {\n    totalOrders,\n    pendingOrders,\n    shippedOrders,\n    deliveredOrders,\n    totalRevenue,\n    totalProfit,\n    roadOrders,\n    airOrders\n  }\n}\n\nexport async function OrderStats() {\n  const stats = await getOrderStats()\n\n  const statCards = [\n    {\n      title: 'Total Commandes',\n      value: stats.totalOrders.toString(),\n      icon: ShoppingCart,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-50',\n      description: 'Toutes commandes'\n    },\n    {\n      title: 'En attente',\n      value: stats.pendingOrders.toString(),\n      icon: Clock,\n      color: 'text-yellow-600',\n      bgColor: 'bg-yellow-50',\n      description: 'À traiter'\n    },\n    {\n      title: 'En transit',\n      value: stats.shippedOrders.toString(),\n      icon: Truck,\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-50',\n      description: 'En livraison'\n    },\n    {\n      title: 'Livrées',\n      value: stats.deliveredOrders.toString(),\n      icon: CheckCircle,\n      color: 'text-green-600',\n      bgColor: 'bg-green-50',\n      description: 'Terminées'\n    },\n    {\n      title: 'Chiffre d\\'affaires',\n      value: formatCurrency(stats.totalRevenue),\n      icon: DollarSign,\n      color: 'text-emerald-600',\n      bgColor: 'bg-emerald-50',\n      description: 'Total des ventes'\n    },\n    {\n      title: 'Bénéfices',\n      value: formatCurrency(stats.totalProfit),\n      icon: TrendingUp,\n      color: 'text-indigo-600',\n      bgColor: 'bg-indigo-50',\n      description: 'Profit total'\n    }\n  ]\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Main Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4\">\n        {statCards.map((stat, index) => (\n          <div key={index} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-xs font-medium text-gray-600 uppercase tracking-wide\">\n                  {stat.title}\n                </p>\n                <p className=\"text-lg font-bold text-gray-900 mt-1\">{stat.value}</p>\n                <p className=\"text-xs text-gray-500 mt-1\">{stat.description}</p>\n              </div>\n              <div className={`p-2 rounded-lg ${stat.bgColor}`}>\n                <stat.icon className={`h-4 w-4 ${stat.color}`} />\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Transport Mode Stats */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n          Répartition par mode de transport\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div className=\"flex items-center justify-between p-4 bg-blue-50 rounded-lg\">\n            <div className=\"flex items-center\">\n              <Truck className=\"h-8 w-8 text-blue-600 mr-3\" />\n              <div>\n                <p className=\"font-medium text-gray-900\">Transport routier</p>\n                <p className=\"text-sm text-gray-600\">5-7 jours • Économique</p>\n              </div>\n            </div>\n            <div className=\"text-right\">\n              <p className=\"text-2xl font-bold text-blue-600\">{stats.roadOrders}</p>\n              <p className=\"text-sm text-gray-600\">commandes</p>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center justify-between p-4 bg-purple-50 rounded-lg\">\n            <div className=\"flex items-center\">\n              <div className=\"h-8 w-8 bg-purple-600 rounded-lg flex items-center justify-center mr-3\">\n                <span className=\"text-white font-bold text-sm\">✈</span>\n              </div>\n              <div>\n                <p className=\"font-medium text-gray-900\">Transport aérien</p>\n                <p className=\"text-sm text-gray-600\">24-48h • Express</p>\n              </div>\n            </div>\n            <div className=\"text-right\">\n              <p className=\"text-2xl font-bold text-purple-600\">{stats.airOrders}</p>\n              <p className=\"text-sm text-gray-600\">commandes</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AASA,eAAe;IACb,MAAM,CACJ,aACA,eACA,eACA,iBACA,OACD,GAAG,MAAM,QAAQ,GAAG,CAAC;QACpB,oHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK;QAClB,oHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YAAE,OAAO;gBAAE,QAAQ;YAAU;QAAE;QAClD,oHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YAAE,OAAO;gBAAE,QAAQ;YAAU;QAAE;QAClD,oHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YAAE,OAAO;gBAAE,QAAQ;YAAY;QAAE;QACpD,oHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YACpB,QAAQ;gBACN,aAAa;gBACb,aAAa;gBACb,eAAe;gBACf,QAAQ;YACV;QACF;KACD;IAED,uCAAuC;IACvC,MAAM,eAAe,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,WAAW,EAAE;IAC5E,MAAM,cAAc,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,WAAW,EAAE;IAE3E,qCAAqC;IACrC,MAAM,aAAa,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,aAAa,KAAK,QAAQ,MAAM;IAChF,MAAM,YAAY,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,aAAa,KAAK,OAAO,MAAM;IAE9E,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAEO,eAAe;IACpB,MAAM,QAAQ,MAAM;IAEpB,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,MAAM,WAAW,CAAC,QAAQ;YACjC,MAAM,sNAAA,CAAA,eAAY;YAClB,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,MAAM,aAAa,CAAC,QAAQ;YACnC,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,MAAM,aAAa,CAAC,QAAQ;YACnC,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,MAAM,eAAe,CAAC,QAAQ;YACrC,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY;YACxC,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,WAAW;YACvC,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;YACT,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;wBAAgB,WAAU;kCACzB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDACV,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CAAE,WAAU;sDAAwC,KAAK,KAAK;;;;;;sDAC/D,8OAAC;4CAAE,WAAU;sDAA8B,KAAK,WAAW;;;;;;;;;;;;8CAE7D,8OAAC;oCAAI,WAAW,CAAC,eAAe,EAAE,KAAK,OAAO,EAAE;8CAC9C,cAAA,8OAAC,KAAK,IAAI;wCAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;;;;;;;uBAVzC;;;;;;;;;;0BAkBd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAA4B;;;;;;kEACzC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAGzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC,MAAM,UAAU;;;;;;0DACjE,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAIzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAA4B;;;;;;kEACzC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAGzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAsC,MAAM,SAAS;;;;;;0DAClE,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}, {"offset": {"line": 1128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/orders/page.tsx"], "sourcesContent": ["import { Suspense } from 'react'\nimport { OrdersList } from '@/components/orders/OrdersList'\nimport { OrderFilters } from '@/components/orders/OrderFilters'\nimport { CreateOrderButton } from '@/components/orders/CreateOrderButton'\nimport { OrderStats } from '@/components/orders/OrderStats'\n\nexport default function OrdersPage() {\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between border-b border-gray-200 pb-4\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Commandes</h1>\n          <p className=\"text-gray-600 mt-1\">\n            G<PERSON><PERSON> vos commandes avec suivi logistique Nigeria-Dakar\n          </p>\n        </div>\n        <CreateOrderButton />\n      </div>\n\n      {/* Stats */}\n      <Suspense fallback={<div className=\"animate-pulse h-24 bg-gray-200 rounded-lg\" />}>\n        <OrderStats />\n      </Suspense>\n\n      {/* Filters */}\n      <Suspense fallback={<div className=\"animate-pulse h-16 bg-gray-200 rounded-lg\" />}>\n        <OrderFilters />\n      </Suspense>\n\n      {/* Orders List */}\n      <Suspense fallback={<div className=\"animate-pulse h-96 bg-gray-200 rounded-lg\" />}>\n        <OrdersList />\n      </Suspense>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,8OAAC,iJAAA,CAAA,oBAAiB;;;;;;;;;;;0BAIpB,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;oBAAI,WAAU;;;;;;0BACjC,cAAA,8OAAC,0IAAA,CAAA,aAAU;;;;;;;;;;0BAIb,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;oBAAI,WAAU;;;;;;0BACjC,cAAA,8OAAC,4IAAA,CAAA,eAAY;;;;;;;;;;0BAIf,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;oBAAI,WAAU;;;;;;0BACjC,cAAA,8OAAC,0IAAA,CAAA,aAAU;;;;;;;;;;;;;;;;AAInB", "debugId": null}}]}