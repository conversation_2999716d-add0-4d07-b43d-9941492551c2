{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/logistics.ts"], "sourcesContent": ["import { TransportMode, SupplierCity, ShipmentStatus } from '@/types'\n\n/**\n * Calcule les coûts de transport selon le mode et le poids\n */\nexport function calculateTransportCosts(\n  weight: number,\n  mode: TransportMode,\n  originCity: SupplierCity,\n  fuelSurcharge: number = 0\n): number {\n  // Tarifs de base par kg selon le mode de transport\n  const baseRates = {\n    ROAD: {\n      LAGOS: 500,   // 500 XOF par kg depuis Lagos\n      ABUJA: 600,   // 600 XOF par kg depuis Abuja\n      KANO: 700     // 700 XOF par kg depuis Kano\n    },\n    AIR_EXPRESS: {\n      LAGOS: 2000,  // 2000 XOF par kg depuis Lagos\n      ABUJA: 2200,  // 2200 XOF par kg depuis Abuja\n      KANO: 2500    // 2500 XOF par kg depuis Kano\n    }\n  }\n\n  const baseRate = baseRates[mode][originCity]\n  const baseCost = weight * baseRate\n  const fuelCost = baseCost * (fuelSurcharge / 100)\n  \n  return baseCost + fuelCost\n}\n\n/**\n * Calcule la durée estimée de livraison\n */\nexport function calculateDeliveryTime(\n  mode: TransportMode,\n  originCity: SupplierCity,\n  orderDate: Date = new Date()\n): Date {\n  // Durées en heures selon le mode et la ville d'origine\n  const deliveryTimes = {\n    ROAD: {\n      LAGOS: 120,   // 5 jours\n      ABUJA: 144,   // 6 jours\n      KANO: 168     // 7 jours\n    },\n    AIR_EXPRESS: {\n      LAGOS: 24,    // 1 jour\n      ABUJA: 36,    // 1.5 jours\n      KANO: 48      // 2 jours\n    }\n  }\n\n  const hoursToAdd = deliveryTimes[mode][originCity]\n  const estimatedDelivery = new Date(orderDate)\n  estimatedDelivery.setHours(estimatedDelivery.getHours() + hoursToAdd)\n  \n  return estimatedDelivery\n}\n\n/**\n * Génère un numéro de suivi unique\n */\nexport function generateTrackingNumber(\n  mode: TransportMode,\n  originCity: SupplierCity\n): string {\n  const prefix = mode === 'ROAD' ? 'RD' : 'AE'\n  const cityCode = originCity.substring(0, 2)\n  const timestamp = Date.now().toString().slice(-6)\n  const random = Math.random().toString(36).substring(2, 5).toUpperCase()\n  \n  return `${prefix}${cityCode}${timestamp}${random}`\n}\n\n/**\n * Détermine le statut de livraison basé sur les dates\n */\nexport function getDeliveryStatus(\n  estimatedDelivery: Date,\n  actualDelivery?: Date\n): 'ON_TIME' | 'DELAYED' | 'EARLY' | 'PENDING' {\n  if (!actualDelivery) return 'PENDING'\n  \n  const timeDiff = actualDelivery.getTime() - estimatedDelivery.getTime()\n  const hoursDiff = timeDiff / (1000 * 60 * 60)\n  \n  if (hoursDiff <= 0) return 'EARLY'\n  if (hoursDiff <= 24) return 'ON_TIME' // Tolérance de 24h\n  return 'DELAYED'\n}\n\n/**\n * Calcule le taux de ponctualité d'un transporteur\n */\nexport function calculateOnTimeRate(\n  totalDeliveries: number,\n  onTimeDeliveries: number\n): number {\n  if (totalDeliveries === 0) return 0\n  return Math.round((onTimeDeliveries / totalDeliveries) * 100)\n}\n\n/**\n * Obtient la couleur du statut d'expédition\n */\nexport function getShipmentStatusColor(status: ShipmentStatus): string {\n  const colors = {\n    PENDING: 'bg-gray-100 text-gray-800',\n    PICKED_UP: 'bg-blue-100 text-blue-800',\n    IN_TRANSIT: 'bg-yellow-100 text-yellow-800',\n    CUSTOMS: 'bg-orange-100 text-orange-800',\n    OUT_FOR_DELIVERY: 'bg-purple-100 text-purple-800',\n    DELIVERED: 'bg-green-100 text-green-800',\n    DELAYED: 'bg-red-100 text-red-800',\n    CANCELLED: 'bg-gray-100 text-gray-800'\n  }\n  \n  return colors[status] || 'bg-gray-100 text-gray-800'\n}\n\n/**\n * Obtient le libellé français du statut d'expédition\n */\nexport function getShipmentStatusLabel(status: ShipmentStatus): string {\n  const labels = {\n    PENDING: 'En attente',\n    PICKED_UP: 'Enlevée',\n    IN_TRANSIT: 'En transit',\n    CUSTOMS: 'En douane',\n    OUT_FOR_DELIVERY: 'En livraison',\n    DELIVERED: 'Livrée',\n    DELAYED: 'Retardée',\n    CANCELLED: 'Annulée'\n  }\n  \n  return labels[status] || status\n}\n\n/**\n * Calcule les statistiques logistiques\n */\nexport function calculateLogisticsKPIs(shipments: any[]) {\n  const total = shipments.length\n  const delivered = shipments.filter(s => s.status === 'DELIVERED').length\n  const inTransit = shipments.filter(s => \n    ['PICKED_UP', 'IN_TRANSIT', 'CUSTOMS', 'OUT_FOR_DELIVERY'].includes(s.status)\n  ).length\n  \n  // Calcul du temps moyen de livraison (en heures)\n  const deliveredShipments = shipments.filter(s => s.deliveryDate && s.pickupDate)\n  const averageDeliveryTime = deliveredShipments.length > 0\n    ? deliveredShipments.reduce((sum, s) => {\n        const diff = new Date(s.deliveryDate).getTime() - new Date(s.pickupDate).getTime()\n        return sum + (diff / (1000 * 60 * 60))\n      }, 0) / deliveredShipments.length\n    : 0\n  \n  // Calcul du taux de ponctualité\n  const onTimeDeliveries = deliveredShipments.filter(s => {\n    const status = getDeliveryStatus(new Date(s.estimatedDelivery), new Date(s.deliveryDate))\n    return status === 'ON_TIME' || status === 'EARLY'\n  }).length\n  \n  const onTimeRate = deliveredShipments.length > 0\n    ? (onTimeDeliveries / deliveredShipments.length) * 100\n    : 0\n  \n  // Coût total de transport\n  const totalTransportCosts = shipments.reduce((sum, s) => sum + (s.transportCost || 0), 0)\n  \n  return {\n    totalShipments: total,\n    inTransitShipments: inTransit,\n    deliveredShipments: delivered,\n    averageDeliveryTime: Math.round(averageDeliveryTime),\n    onTimeDeliveryRate: Math.round(onTimeRate),\n    totalTransportCosts\n  }\n}\n\n/**\n * Valide les données d'une expédition\n */\nexport function validateShipmentData(data: any): string[] {\n  const errors: string[] = []\n  \n  if (!data.orderId) errors.push('ID de commande requis')\n  if (!data.carrierId) errors.push('Transporteur requis')\n  if (!data.weight || data.weight <= 0) errors.push('Poids valide requis')\n  if (!data.transportMode) errors.push('Mode de transport requis')\n  if (!data.originCity) errors.push('Ville d\\'origine requise')\n  \n  return errors\n}\n\n/**\n * Formate une durée en heures vers un texte lisible\n */\nexport function formatDuration(hours: number): string {\n  if (hours < 24) {\n    return `${Math.round(hours)}h`\n  } else {\n    const days = Math.floor(hours / 24)\n    const remainingHours = Math.round(hours % 24)\n    return remainingHours > 0 ? `${days}j ${remainingHours}h` : `${days}j`\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAKO,SAAS,wBACd,MAAc,EACd,IAAmB,EACnB,UAAwB,EACxB,gBAAwB,CAAC;IAEzB,mDAAmD;IACnD,MAAM,YAAY;QAChB,MAAM;YACJ,OAAO;YACP,OAAO;YACP,MAAM,IAAQ,6BAA6B;QAC7C;QACA,aAAa;YACX,OAAO;YACP,OAAO;YACP,MAAM,KAAQ,8BAA8B;QAC9C;IACF;IAEA,MAAM,WAAW,SAAS,CAAC,KAAK,CAAC,WAAW;IAC5C,MAAM,WAAW,SAAS;IAC1B,MAAM,WAAW,WAAW,CAAC,gBAAgB,GAAG;IAEhD,OAAO,WAAW;AACpB;AAKO,SAAS,sBACd,IAAmB,EACnB,UAAwB,EACxB,YAAkB,IAAI,MAAM;IAE5B,uDAAuD;IACvD,MAAM,gBAAgB;QACpB,MAAM;YACJ,OAAO;YACP,OAAO;YACP,MAAM,IAAQ,UAAU;QAC1B;QACA,aAAa;YACX,OAAO;YACP,OAAO;YACP,MAAM,GAAQ,UAAU;QAC1B;IACF;IAEA,MAAM,aAAa,aAAa,CAAC,KAAK,CAAC,WAAW;IAClD,MAAM,oBAAoB,IAAI,KAAK;IACnC,kBAAkB,QAAQ,CAAC,kBAAkB,QAAQ,KAAK;IAE1D,OAAO;AACT;AAKO,SAAS,uBACd,IAAmB,EACnB,UAAwB;IAExB,MAAM,SAAS,SAAS,SAAS,OAAO;IACxC,MAAM,WAAW,WAAW,SAAS,CAAC,GAAG;IACzC,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW;IAErE,OAAO,GAAG,SAAS,WAAW,YAAY,QAAQ;AACpD;AAKO,SAAS,kBACd,iBAAuB,EACvB,cAAqB;IAErB,IAAI,CAAC,gBAAgB,OAAO;IAE5B,MAAM,WAAW,eAAe,OAAO,KAAK,kBAAkB,OAAO;IACrE,MAAM,YAAY,WAAW,CAAC,OAAO,KAAK,EAAE;IAE5C,IAAI,aAAa,GAAG,OAAO;IAC3B,IAAI,aAAa,IAAI,OAAO,UAAU,mBAAmB;;IACzD,OAAO;AACT;AAKO,SAAS,oBACd,eAAuB,EACvB,gBAAwB;IAExB,IAAI,oBAAoB,GAAG,OAAO;IAClC,OAAO,KAAK,KAAK,CAAC,AAAC,mBAAmB,kBAAmB;AAC3D;AAKO,SAAS,uBAAuB,MAAsB;IAC3D,MAAM,SAAS;QACb,SAAS;QACT,WAAW;QACX,YAAY;QACZ,SAAS;QACT,kBAAkB;QAClB,WAAW;QACX,SAAS;QACT,WAAW;IACb;IAEA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAKO,SAAS,uBAAuB,MAAsB;IAC3D,MAAM,SAAS;QACb,SAAS;QACT,WAAW;QACX,YAAY;QACZ,SAAS;QACT,kBAAkB;QAClB,WAAW;QACX,SAAS;QACT,WAAW;IACb;IAEA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAKO,SAAS,uBAAuB,SAAgB;IACrD,MAAM,QAAQ,UAAU,MAAM;IAC9B,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;IACxE,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,IACjC;YAAC;YAAa;YAAc;YAAW;SAAmB,CAAC,QAAQ,CAAC,EAAE,MAAM,GAC5E,MAAM;IAER,iDAAiD;IACjD,MAAM,qBAAqB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,IAAI,EAAE,UAAU;IAC/E,MAAM,sBAAsB,mBAAmB,MAAM,GAAG,IACpD,mBAAmB,MAAM,CAAC,CAAC,KAAK;QAC9B,MAAM,OAAO,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;QAChF,OAAO,MAAO,OAAO,CAAC,OAAO,KAAK,EAAE;IACtC,GAAG,KAAK,mBAAmB,MAAM,GACjC;IAEJ,gCAAgC;IAChC,MAAM,mBAAmB,mBAAmB,MAAM,CAAC,CAAA;QACjD,MAAM,SAAS,kBAAkB,IAAI,KAAK,EAAE,iBAAiB,GAAG,IAAI,KAAK,EAAE,YAAY;QACvF,OAAO,WAAW,aAAa,WAAW;IAC5C,GAAG,MAAM;IAET,MAAM,aAAa,mBAAmB,MAAM,GAAG,IAC3C,AAAC,mBAAmB,mBAAmB,MAAM,GAAI,MACjD;IAEJ,0BAA0B;IAC1B,MAAM,sBAAsB,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,aAAa,IAAI,CAAC,GAAG;IAEvF,OAAO;QACL,gBAAgB;QAChB,oBAAoB;QACpB,oBAAoB;QACpB,qBAAqB,KAAK,KAAK,CAAC;QAChC,oBAAoB,KAAK,KAAK,CAAC;QAC/B;IACF;AACF;AAKO,SAAS,qBAAqB,IAAS;IAC5C,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,KAAK,OAAO,EAAE,OAAO,IAAI,CAAC;IAC/B,IAAI,CAAC,KAAK,SAAS,EAAE,OAAO,IAAI,CAAC;IACjC,IAAI,CAAC,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO,IAAI,CAAC;IAClD,IAAI,CAAC,KAAK,aAAa,EAAE,OAAO,IAAI,CAAC;IACrC,IAAI,CAAC,KAAK,UAAU,EAAE,OAAO,IAAI,CAAC;IAElC,OAAO;AACT;AAKO,SAAS,eAAe,KAAa;IAC1C,IAAI,QAAQ,IAAI;QACd,OAAO,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC;IAChC,OAAO;QACL,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ;QAChC,MAAM,iBAAiB,KAAK,KAAK,CAAC,QAAQ;QAC1C,OAAO,iBAAiB,IAAI,GAAG,KAAK,EAAE,EAAE,eAAe,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;IACxE;AACF", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/actions/shipments.ts"], "sourcesContent": ["'use server'\n\nimport { prisma } from '@/lib/prisma'\nimport { revalidatePath } from 'next/cache'\nimport { \n  generateTrackingNumber, \n  calculateTransportCosts, \n  calculateDeliveryTime,\n  validateShipmentData \n} from '@/lib/logistics'\nimport { TransportMode, SupplierCity, ShipmentStatus, TrackingEventType } from '@/types'\n\n/**\n * Crée une nouvelle expédition pour une commande\n */\nexport async function createShipment(data: {\n  orderId: string\n  carrierId: string\n  weight: number\n  transportMode: TransportMode\n  originCity: SupplierCity\n  fuelSurcharge?: number\n  notes?: string\n}) {\n  try {\n    // Validation des données\n    const errors = validateShipmentData(data)\n    if (errors.length > 0) {\n      return { success: false, error: errors.join(', ') }\n    }\n\n    // Vérifier que la commande existe et n'a pas déjà d'expédition\n    const existingOrder = await prisma.order.findUnique({\n      where: { id: data.orderId },\n      include: { shipment: true }\n    })\n\n    if (!existingOrder) {\n      return { success: false, error: 'Commande introuvable' }\n    }\n\n    if (existingOrder.shipment) {\n      return { success: false, error: 'Cette commande a déjà une expédition' }\n    }\n\n    // Générer le numéro de suivi\n    const trackingNumber = generateTrackingNumber(data.transportMode, data.originCity)\n\n    // Calculer les coûts et délais\n    const transportCost = calculateTransportCosts(\n      data.weight,\n      data.transportMode,\n      data.originCity,\n      data.fuelSurcharge || 0\n    )\n\n    const estimatedDelivery = calculateDeliveryTime(\n      data.transportMode,\n      data.originCity,\n      existingOrder.orderDate\n    )\n\n    // Créer l'expédition\n    const shipment = await prisma.shipment.create({\n      data: {\n        trackingNumber,\n        orderId: data.orderId,\n        carrierId: data.carrierId,\n        status: 'PENDING',\n        transportMode: data.transportMode,\n        originCity: data.originCity,\n        destinationCity: 'DAKAR',\n        weight: data.weight,\n        transportCost,\n        fuelSurcharge: data.fuelSurcharge || 0,\n        estimatedDelivery,\n        notes: data.notes\n      },\n      include: {\n        carrier: true,\n        order: true\n      }\n    })\n\n    // Créer l'événement de suivi initial\n    await prisma.trackingEvent.create({\n      data: {\n        shipmentId: shipment.id,\n        eventType: 'PICKUP_SCHEDULED',\n        location: data.originCity,\n        description: `Enlèvement programmé chez ${shipment.carrier.name}`,\n        timestamp: new Date()\n      }\n    })\n\n    // Mettre à jour le statut de la commande\n    await prisma.order.update({\n      where: { id: data.orderId },\n      data: { status: 'SHIPPED' }\n    })\n\n    revalidatePath('/logistics')\n    revalidatePath('/orders')\n    \n    return { success: true, shipment }\n  } catch (error) {\n    console.error('Erreur lors de la création de l\\'expédition:', error)\n    return { success: false, error: 'Erreur lors de la création de l\\'expédition' }\n  }\n}\n\n/**\n * Met à jour le statut d'une expédition\n */\nexport async function updateShipmentStatus(\n  shipmentId: string,\n  status: ShipmentStatus,\n  location?: string,\n  notes?: string\n) {\n  try {\n    const shipment = await prisma.shipment.findUnique({\n      where: { id: shipmentId },\n      include: {\n        orders: true,\n        shipmentRequests: true\n      }\n    })\n\n    if (!shipment) {\n      return { success: false, error: 'Expédition introuvable' }\n    }\n\n    // Mettre à jour l'expédition\n    const updatedShipment = await prisma.shipment.update({\n      where: { id: shipmentId },\n      data: {\n        status,\n        currentLocation: location,\n        ...(status === 'PICKED_UP' && { pickupDate: new Date() }),\n        ...(status === 'DELIVERED' && { deliveryDate: new Date() }),\n        ...(notes && { notes })\n      }\n    })\n\n    // Créer un événement de suivi\n    const eventTypeMap: Record<ShipmentStatus, TrackingEventType> = {\n      PENDING: 'PICKUP_SCHEDULED',\n      PICKED_UP: 'PICKED_UP',\n      IN_TRANSIT: 'IN_TRANSIT',\n      CUSTOMS: 'CUSTOMS_CLEARANCE',\n      OUT_FOR_DELIVERY: 'OUT_FOR_DELIVERY',\n      DELIVERED: 'DELIVERED',\n      DELAYED: 'DELAYED',\n      CANCELLED: 'DELIVERY_FAILED'\n    }\n\n    await prisma.trackingEvent.create({\n      data: {\n        shipmentId,\n        eventType: eventTypeMap[status],\n        location: location || shipment.currentLocation || shipment.originCity,\n        description: notes || `Statut mis à jour: ${status}`,\n        timestamp: new Date()\n      }\n    })\n\n    // Mettre à jour le statut de la commande si nécessaire\n    if (status === 'DELIVERED') {\n      await prisma.order.update({\n        where: { id: shipment.orderId },\n        data: { \n          status: 'DELIVERED',\n          deliveredAt: new Date()\n        }\n      })\n    }\n\n    revalidatePath('/logistics')\n    revalidatePath('/orders')\n    \n    return { success: true, shipment: updatedShipment }\n  } catch (error) {\n    console.error('Erreur lors de la mise à jour du statut:', error)\n    return { success: false, error: 'Erreur lors de la mise à jour du statut' }\n  }\n}\n\n/**\n * Récupère toutes les expéditions avec filtres\n */\nexport async function getShipments(filters?: {\n  status?: ShipmentStatus\n  carrierId?: string\n  transportMode?: TransportMode\n  dateFrom?: Date\n  dateTo?: Date\n}) {\n  try {\n    const where: any = {}\n\n    if (filters?.status) where.status = filters.status\n    if (filters?.carrierId) where.carrierId = filters.carrierId\n    if (filters?.transportMode) where.transportMode = filters.transportMode\n    if (filters?.dateFrom || filters?.dateTo) {\n      where.createdAt = {}\n      if (filters.dateFrom) where.createdAt.gte = filters.dateFrom\n      if (filters.dateTo) where.createdAt.lte = filters.dateTo\n    }\n\n    const shipments = await prisma.shipment.findMany({\n      where,\n      include: {\n        carrier: true,\n        orders: {\n          include: {\n            customer: true,\n            supplier: true\n          }\n        },\n        shipmentRequests: {\n          include: {\n            customer: true\n          }\n        },\n        trackingEvents: {\n          orderBy: { timestamp: 'desc' }\n        },\n        shipmentTracking: {\n          orderBy: { timestamp: 'desc' }\n        }\n      },\n      orderBy: { createdAt: 'desc' }\n    })\n\n    return shipments\n  } catch (error) {\n    console.error('Erreur lors de la récupération des expéditions:', error)\n    return []\n  }\n}\n\n/**\n * Récupère une expédition par son numéro de suivi\n */\nexport async function getShipmentByTracking(trackingNumber: string) {\n  try {\n    const shipment = await prisma.shipment.findUnique({\n      where: { trackingNumber },\n      include: {\n        carrier: true,\n        order: {\n          include: {\n            customer: true,\n            supplier: true,\n            orderItems: {\n              include: {\n                product: true\n              }\n            }\n          }\n        },\n        trackingEvents: {\n          orderBy: { timestamp: 'asc' }\n        }\n      }\n    })\n\n    return shipment\n  } catch (error) {\n    console.error('Erreur lors de la récupération de l\\'expédition:', error)\n    return null\n  }\n}\n\n/**\n * Ajoute un événement de suivi manuel\n */\nexport async function addTrackingEvent(\n  shipmentId: string,\n  eventType: TrackingEventType,\n  location: string,\n  description: string\n) {\n  try {\n    const event = await prisma.trackingEvent.create({\n      data: {\n        shipmentId,\n        eventType,\n        location,\n        description,\n        timestamp: new Date()\n      }\n    })\n\n    revalidatePath('/logistics')\n    \n    return { success: true, event }\n  } catch (error) {\n    console.error('Erreur lors de l\\'ajout de l\\'événement:', error)\n    return { success: false, error: 'Erreur lors de l\\'ajout de l\\'événement' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;;;;;;;AAWO,eAAe,eAAe,IAQpC;IACC,IAAI;QACF,yBAAyB;QACzB,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,uBAAoB,AAAD,EAAE;QACpC,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,OAAO;gBAAE,SAAS;gBAAO,OAAO,OAAO,IAAI,CAAC;YAAM;QACpD;QAEA,+DAA+D;QAC/D,MAAM,gBAAgB,MAAM,oHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,UAAU,CAAC;YAClD,OAAO;gBAAE,IAAI,KAAK,OAAO;YAAC;YAC1B,SAAS;gBAAE,UAAU;YAAK;QAC5B;QAEA,IAAI,CAAC,eAAe;YAClB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAuB;QACzD;QAEA,IAAI,cAAc,QAAQ,EAAE;YAC1B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAuC;QACzE;QAEA,6BAA6B;QAC7B,MAAM,iBAAiB,CAAA,GAAA,uHAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,aAAa,EAAE,KAAK,UAAU;QAEjF,+BAA+B;QAC/B,MAAM,gBAAgB,CAAA,GAAA,uHAAA,CAAA,0BAAuB,AAAD,EAC1C,KAAK,MAAM,EACX,KAAK,aAAa,EAClB,KAAK,UAAU,EACf,KAAK,aAAa,IAAI;QAGxB,MAAM,oBAAoB,CAAA,GAAA,uHAAA,CAAA,wBAAqB,AAAD,EAC5C,KAAK,aAAa,EAClB,KAAK,UAAU,EACf,cAAc,SAAS;QAGzB,qBAAqB;QACrB,MAAM,WAAW,MAAM,oHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ;gBACA,SAAS,KAAK,OAAO;gBACrB,WAAW,KAAK,SAAS;gBACzB,QAAQ;gBACR,eAAe,KAAK,aAAa;gBACjC,YAAY,KAAK,UAAU;gBAC3B,iBAAiB;gBACjB,QAAQ,KAAK,MAAM;gBACnB;gBACA,eAAe,KAAK,aAAa,IAAI;gBACrC;gBACA,OAAO,KAAK,KAAK;YACnB;YACA,SAAS;gBACP,SAAS;gBACT,OAAO;YACT;QACF;QAEA,qCAAqC;QACrC,MAAM,oHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ,YAAY,SAAS,EAAE;gBACvB,WAAW;gBACX,UAAU,KAAK,UAAU;gBACzB,aAAa,CAAC,0BAA0B,EAAE,SAAS,OAAO,CAAC,IAAI,EAAE;gBACjE,WAAW,IAAI;YACjB;QACF;QAEA,yCAAyC;QACzC,MAAM,oHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YACxB,OAAO;gBAAE,IAAI,KAAK,OAAO;YAAC;YAC1B,MAAM;gBAAE,QAAQ;YAAU;QAC5B;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAM;QAAS;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO;YAAE,SAAS;YAAO,OAAO;QAA8C;IAChF;AACF;AAKO,eAAe,qBACpB,UAAkB,EAClB,MAAsB,EACtB,QAAiB,EACjB,KAAc;IAEd,IAAI;QACF,MAAM,WAAW,MAAM,oHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE,IAAI;YAAW;YACxB,SAAS;gBACP,QAAQ;gBACR,kBAAkB;YACpB;QACF;QAEA,IAAI,CAAC,UAAU;YACb,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;QAEA,6BAA6B;QAC7B,MAAM,kBAAkB,MAAM,oHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YACnD,OAAO;gBAAE,IAAI;YAAW;YACxB,MAAM;gBACJ;gBACA,iBAAiB;gBACjB,GAAI,WAAW,eAAe;oBAAE,YAAY,IAAI;gBAAO,CAAC;gBACxD,GAAI,WAAW,eAAe;oBAAE,cAAc,IAAI;gBAAO,CAAC;gBAC1D,GAAI,SAAS;oBAAE;gBAAM,CAAC;YACxB;QACF;QAEA,8BAA8B;QAC9B,MAAM,eAA0D;YAC9D,SAAS;YACT,WAAW;YACX,YAAY;YACZ,SAAS;YACT,kBAAkB;YAClB,WAAW;YACX,SAAS;YACT,WAAW;QACb;QAEA,MAAM,oHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ;gBACA,WAAW,YAAY,CAAC,OAAO;gBAC/B,UAAU,YAAY,SAAS,eAAe,IAAI,SAAS,UAAU;gBACrE,aAAa,SAAS,CAAC,mBAAmB,EAAE,QAAQ;gBACpD,WAAW,IAAI;YACjB;QACF;QAEA,uDAAuD;QACvD,IAAI,WAAW,aAAa;YAC1B,MAAM,oHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;gBACxB,OAAO;oBAAE,IAAI,SAAS,OAAO;gBAAC;gBAC9B,MAAM;oBACJ,QAAQ;oBACR,aAAa,IAAI;gBACnB;YACF;QACF;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAM,UAAU;QAAgB;IACpD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0C;IAC5E;AACF;AAKO,eAAe,aAAa,OAMlC;IACC,IAAI;QACF,MAAM,QAAa,CAAC;QAEpB,IAAI,SAAS,QAAQ,MAAM,MAAM,GAAG,QAAQ,MAAM;QAClD,IAAI,SAAS,WAAW,MAAM,SAAS,GAAG,QAAQ,SAAS;QAC3D,IAAI,SAAS,eAAe,MAAM,aAAa,GAAG,QAAQ,aAAa;QACvE,IAAI,SAAS,YAAY,SAAS,QAAQ;YACxC,MAAM,SAAS,GAAG,CAAC;YACnB,IAAI,QAAQ,QAAQ,EAAE,MAAM,SAAS,CAAC,GAAG,GAAG,QAAQ,QAAQ;YAC5D,IAAI,QAAQ,MAAM,EAAE,MAAM,SAAS,CAAC,GAAG,GAAG,QAAQ,MAAM;QAC1D;QAEA,MAAM,YAAY,MAAM,oHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC/C;YACA,SAAS;gBACP,SAAS;gBACT,QAAQ;oBACN,SAAS;wBACP,UAAU;wBACV,UAAU;oBACZ;gBACF;gBACA,kBAAkB;oBAChB,SAAS;wBACP,UAAU;oBACZ;gBACF;gBACA,gBAAgB;oBACd,SAAS;wBAAE,WAAW;oBAAO;gBAC/B;gBACA,kBAAkB;oBAChB,SAAS;wBAAE,WAAW;oBAAO;gBAC/B;YACF;YACA,SAAS;gBAAE,WAAW;YAAO;QAC/B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mDAAmD;QACjE,OAAO,EAAE;IACX;AACF;AAKO,eAAe,sBAAsB,cAAsB;IAChE,IAAI;QACF,MAAM,WAAW,MAAM,oHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE;YAAe;YACxB,SAAS;gBACP,SAAS;gBACT,OAAO;oBACL,SAAS;wBACP,UAAU;wBACV,UAAU;wBACV,YAAY;4BACV,SAAS;gCACP,SAAS;4BACX;wBACF;oBACF;gBACF;gBACA,gBAAgB;oBACd,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;YACF;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oDAAoD;QAClE,OAAO;IACT;AACF;AAKO,eAAe,iBACpB,UAAkB,EAClB,SAA4B,EAC5B,QAAgB,EAChB,WAAmB;IAEnB,IAAI;QACF,MAAM,QAAQ,MAAM,oHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YAC9C,MAAM;gBACJ;gBACA;gBACA;gBACA;gBACA,WAAW,IAAI;YACjB;QACF;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAM;QAAM;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0C;IAC5E;AACF;;;IA/RsB;IAmGA;IA6EA;IAsDA;IAiCA;;AAvQA,+OAAA;AAmGA,+OAAA;AA6EA,+OAAA;AAsDA,+OAAA;AAiCA,+OAAA", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/actions/carriers.ts"], "sourcesContent": ["'use server'\n\nimport { prisma } from '@/lib/prisma'\nimport { revalidatePath } from 'next/cache'\nimport { SupplierCity } from '@/types'\n\n/**\n * Récupère tous les transporteurs\n */\nexport async function getCarriers() {\n  try {\n    const carriers = await prisma.carrier.findMany({\n      where: { isActive: true },\n      include: {\n        shipments: {\n          select: {\n            id: true,\n            status: true,\n            deliveryDate: true,\n            estimatedDelivery: true,\n            totalTransportCost: true,\n            pickupDate: true,\n            arrivalDate: true\n          }\n        },\n        evaluations: {\n          select: {\n            overall: true,\n            punctuality: true,\n            condition: true,\n            communication: true\n          }\n        }\n      },\n      orderBy: { name: 'asc' }\n    })\n\n    // Calculer les statistiques pour chaque transporteur\n    const carriersWithStats = carriers.map(carrier => {\n      const totalShipments = carrier.shipments.length\n      const deliveredShipments = carrier.shipments.filter(s => s.status === 'DELIVERED')\n      \n      // Calcul du taux de ponctualité\n      const onTimeDeliveries = deliveredShipments.filter(s => {\n        if (!s.deliveryDate || !s.estimatedDelivery) return false\n        return new Date(s.deliveryDate) <= new Date(s.estimatedDelivery)\n      }).length\n      \n      const onTimeRate = deliveredShipments.length > 0 \n        ? (onTimeDeliveries / deliveredShipments.length) * 100 \n        : 0\n\n      // Calcul de la note moyenne\n      const averageRating = carrier.evaluations.length > 0\n        ? carrier.evaluations.reduce((sum, evaluation) => sum + evaluation.overall, 0) / carrier.evaluations.length\n        : 0\n\n      // Coût total\n      const totalCost = carrier.shipments.reduce((sum, s) => sum + (s.totalTransportCost || 0), 0)\n\n      return {\n        ...carrier,\n        stats: {\n          totalShipments,\n          deliveredShipments: deliveredShipments.length,\n          onTimeRate: Math.round(onTimeRate),\n          averageRating: Math.round(averageRating * 10) / 10,\n          totalCost\n        }\n      }\n    })\n\n    return carriersWithStats\n  } catch (error) {\n    console.error('Erreur lors de la récupération des transporteurs:', error)\n    return []\n  }\n}\n\n/**\n * Crée un nouveau transporteur\n */\nexport async function createCarrier(data: {\n  name: string\n  phone: string\n  email?: string\n  address: string\n  city: SupplierCity\n  transportModes: string[]\n  capacity?: number\n}) {\n  try {\n    const carrier = await prisma.carrier.create({\n      data: {\n        name: data.name,\n        phone: data.phone,\n        email: data.email,\n        address: data.address,\n        city: data.city,\n        transportModes: data.transportModes.join(','),\n        capacity: data.capacity,\n        rating: 0,\n        onTimeRate: 0,\n        isActive: true\n      }\n    })\n\n    revalidatePath('/logistics')\n    \n    return { success: true, carrier }\n  } catch (error) {\n    console.error('Erreur lors de la création du transporteur:', error)\n    return { success: false, error: 'Erreur lors de la création du transporteur' }\n  }\n}\n\n/**\n * Met à jour un transporteur\n */\nexport async function updateCarrier(\n  carrierId: string,\n  data: {\n    name?: string\n    phone?: string\n    email?: string\n    address?: string\n    city?: SupplierCity\n    transportModes?: string[]\n    capacity?: number\n    isActive?: boolean\n  }\n) {\n  try {\n    const updateData: any = { ...data }\n    \n    if (data.transportModes) {\n      updateData.transportModes = data.transportModes.join(',')\n    }\n\n    const carrier = await prisma.carrier.update({\n      where: { id: carrierId },\n      data: updateData\n    })\n\n    revalidatePath('/logistics')\n    \n    return { success: true, carrier }\n  } catch (error) {\n    console.error('Erreur lors de la mise à jour du transporteur:', error)\n    return { success: false, error: 'Erreur lors de la mise à jour du transporteur' }\n  }\n}\n\n/**\n * Évalue un transporteur\n */\nexport async function evaluateCarrier(data: {\n  carrierId: string\n  shipmentId?: string\n  punctuality: number\n  condition: number\n  communication: number\n  overall: number\n  comment?: string\n}) {\n  try {\n    // Créer l'évaluation\n    const evaluation = await prisma.carrierEvaluation.create({\n      data: {\n        carrierId: data.carrierId,\n        shipmentId: data.shipmentId,\n        punctuality: data.punctuality,\n        condition: data.condition,\n        communication: data.communication,\n        overall: data.overall,\n        comment: data.comment\n      }\n    })\n\n    // Recalculer la note moyenne du transporteur\n    const allEvaluations = await prisma.carrierEvaluation.findMany({\n      where: { carrierId: data.carrierId },\n      select: { overall: true }\n    })\n\n    const averageRating = allEvaluations.reduce((sum, evaluation) => sum + evaluation.overall, 0) / allEvaluations.length\n\n    // Mettre à jour la note du transporteur\n    await prisma.carrier.update({\n      where: { id: data.carrierId },\n      data: { rating: averageRating }\n    })\n\n    revalidatePath('/logistics')\n    \n    return { success: true, evaluation }\n  } catch (error) {\n    console.error('Erreur lors de l\\'évaluation du transporteur:', error)\n    return { success: false, error: 'Erreur lors de l\\'évaluation du transporteur' }\n  }\n}\n\n/**\n * Récupère les transporteurs disponibles pour un mode de transport\n */\nexport async function getAvailableCarriers(transportMode: string, city?: SupplierCity) {\n  try {\n    const where: any = {\n      isActive: true,\n      transportModes: {\n        contains: transportMode\n      }\n    }\n\n    if (city) {\n      where.city = city\n    }\n\n    const carriers = await prisma.carrier.findMany({\n      where,\n      select: {\n        id: true,\n        name: true,\n        city: true,\n        rating: true,\n        onTimeRate: true,\n        capacity: true\n      },\n      orderBy: [\n        { rating: 'desc' },\n        { onTimeRate: 'desc' }\n      ]\n    })\n\n    return carriers\n  } catch (error) {\n    console.error('Erreur lors de la récupération des transporteurs disponibles:', error)\n    return []\n  }\n}\n\n/**\n * Récupère les statistiques d'un transporteur\n */\nexport async function getCarrierStats(carrierId: string) {\n  try {\n    const carrier = await prisma.carrier.findUnique({\n      where: { id: carrierId },\n      include: {\n        shipments: {\n          include: {\n            order: true\n          }\n        },\n        evaluations: true\n      }\n    })\n\n    if (!carrier) {\n      return null\n    }\n\n    const totalShipments = carrier.shipments.length\n    const deliveredShipments = carrier.shipments.filter(s => s.status === 'DELIVERED')\n    const inTransitShipments = carrier.shipments.filter(s => \n      ['PICKED_UP', 'IN_TRANSIT', 'CUSTOMS', 'OUT_FOR_DELIVERY'].includes(s.status)\n    )\n\n    // Calcul du taux de ponctualité\n    const onTimeDeliveries = deliveredShipments.filter(s => {\n      if (!s.deliveryDate || !s.estimatedDelivery) return false\n      return new Date(s.deliveryDate) <= new Date(s.estimatedDelivery)\n    }).length\n\n    const onTimeRate = deliveredShipments.length > 0 \n      ? (onTimeDeliveries / deliveredShipments.length) * 100 \n      : 0\n\n    // Revenus générés\n    const totalRevenue = carrier.shipments.reduce((sum, s) => sum + (s.totalTransportCost || 0), 0)\n\n    // Temps moyen de livraison\n    const deliveryTimes = deliveredShipments\n      .filter(s => s.pickupDate && s.deliveryDate)\n      .map(s => {\n        const pickup = new Date(s.pickupDate!).getTime()\n        const delivery = new Date(s.deliveryDate!).getTime()\n        return (delivery - pickup) / (1000 * 60 * 60) // en heures\n      })\n\n    const averageDeliveryTime = deliveryTimes.length > 0\n      ? deliveryTimes.reduce((sum, time) => sum + time, 0) / deliveryTimes.length\n      : 0\n\n    // Évaluations moyennes\n    const evaluationStats = carrier.evaluations.length > 0 ? {\n      punctuality: carrier.evaluations.reduce((sum, e) => sum + e.punctuality, 0) / carrier.evaluations.length,\n      condition: carrier.evaluations.reduce((sum, e) => sum + e.condition, 0) / carrier.evaluations.length,\n      communication: carrier.evaluations.reduce((sum, e) => sum + e.communication, 0) / carrier.evaluations.length,\n      overall: carrier.evaluations.reduce((sum, e) => sum + e.overall, 0) / carrier.evaluations.length\n    } : {\n      punctuality: 0,\n      condition: 0,\n      communication: 0,\n      overall: 0\n    }\n\n    return {\n      carrier,\n      stats: {\n        totalShipments,\n        deliveredShipments: deliveredShipments.length,\n        inTransitShipments: inTransitShipments.length,\n        onTimeRate: Math.round(onTimeRate),\n        totalRevenue,\n        averageDeliveryTime: Math.round(averageDeliveryTime),\n        evaluations: evaluationStats\n      }\n    }\n  } catch (error) {\n    console.error('Erreur lors de la récupération des statistiques du transporteur:', error)\n    return null\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;;;;;;AAMO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7C,OAAO;gBAAE,UAAU;YAAK;YACxB,SAAS;gBACP,WAAW;oBACT,QAAQ;wBACN,IAAI;wBACJ,QAAQ;wBACR,cAAc;wBACd,mBAAmB;wBACnB,oBAAoB;wBACpB,YAAY;wBACZ,aAAa;oBACf;gBACF;gBACA,aAAa;oBACX,QAAQ;wBACN,SAAS;wBACT,aAAa;wBACb,WAAW;wBACX,eAAe;oBACjB;gBACF;YACF;YACA,SAAS;gBAAE,MAAM;YAAM;QACzB;QAEA,qDAAqD;QACrD,MAAM,oBAAoB,SAAS,GAAG,CAAC,CAAA;YACrC,MAAM,iBAAiB,QAAQ,SAAS,CAAC,MAAM;YAC/C,MAAM,qBAAqB,QAAQ,SAAS,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;YAEtE,gCAAgC;YAChC,MAAM,mBAAmB,mBAAmB,MAAM,CAAC,CAAA;gBACjD,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC,EAAE,iBAAiB,EAAE,OAAO;gBACpD,OAAO,IAAI,KAAK,EAAE,YAAY,KAAK,IAAI,KAAK,EAAE,iBAAiB;YACjE,GAAG,MAAM;YAET,MAAM,aAAa,mBAAmB,MAAM,GAAG,IAC3C,AAAC,mBAAmB,mBAAmB,MAAM,GAAI,MACjD;YAEJ,4BAA4B;YAC5B,MAAM,gBAAgB,QAAQ,WAAW,CAAC,MAAM,GAAG,IAC/C,QAAQ,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,aAAe,MAAM,WAAW,OAAO,EAAE,KAAK,QAAQ,WAAW,CAAC,MAAM,GACzG;YAEJ,aAAa;YACb,MAAM,YAAY,QAAQ,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,kBAAkB,IAAI,CAAC,GAAG;YAE1F,OAAO;gBACL,GAAG,OAAO;gBACV,OAAO;oBACL;oBACA,oBAAoB,mBAAmB,MAAM;oBAC7C,YAAY,KAAK,KAAK,CAAC;oBACvB,eAAe,KAAK,KAAK,CAAC,gBAAgB,MAAM;oBAChD;gBACF;YACF;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QACnE,OAAO,EAAE;IACX;AACF;AAKO,eAAe,cAAc,IAQnC;IACC,IAAI;QACF,MAAM,UAAU,MAAM,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,MAAM;gBACJ,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,OAAO,KAAK,KAAK;gBACjB,SAAS,KAAK,OAAO;gBACrB,MAAM,KAAK,IAAI;gBACf,gBAAgB,KAAK,cAAc,CAAC,IAAI,CAAC;gBACzC,UAAU,KAAK,QAAQ;gBACvB,QAAQ;gBACR,YAAY;gBACZ,UAAU;YACZ;QACF;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAM;QAAQ;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,OAAO;YAAE,SAAS;YAAO,OAAO;QAA6C;IAC/E;AACF;AAKO,eAAe,cACpB,SAAiB,EACjB,IASC;IAED,IAAI;QACF,MAAM,aAAkB;YAAE,GAAG,IAAI;QAAC;QAElC,IAAI,KAAK,cAAc,EAAE;YACvB,WAAW,cAAc,GAAG,KAAK,cAAc,CAAC,IAAI,CAAC;QACvD;QAEA,MAAM,UAAU,MAAM,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,OAAO;gBAAE,IAAI;YAAU;YACvB,MAAM;QACR;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAM;QAAQ;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,OAAO;YAAE,SAAS;YAAO,OAAO;QAAgD;IAClF;AACF;AAKO,eAAe,gBAAgB,IAQrC;IACC,IAAI;QACF,qBAAqB;QACrB,MAAM,aAAa,MAAM,oHAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;YACvD,MAAM;gBACJ,WAAW,KAAK,SAAS;gBACzB,YAAY,KAAK,UAAU;gBAC3B,aAAa,KAAK,WAAW;gBAC7B,WAAW,KAAK,SAAS;gBACzB,eAAe,KAAK,aAAa;gBACjC,SAAS,KAAK,OAAO;gBACrB,SAAS,KAAK,OAAO;YACvB;QACF;QAEA,6CAA6C;QAC7C,MAAM,iBAAiB,MAAM,oHAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YAC7D,OAAO;gBAAE,WAAW,KAAK,SAAS;YAAC;YACnC,QAAQ;gBAAE,SAAS;YAAK;QAC1B;QAEA,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAC,KAAK,aAAe,MAAM,WAAW,OAAO,EAAE,KAAK,eAAe,MAAM;QAErH,wCAAwC;QACxC,MAAM,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1B,OAAO;gBAAE,IAAI,KAAK,SAAS;YAAC;YAC5B,MAAM;gBAAE,QAAQ;YAAc;QAChC;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAM;QAAW;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+C;IACjF;AACF;AAKO,eAAe,qBAAqB,aAAqB,EAAE,IAAmB;IACnF,IAAI;QACF,MAAM,QAAa;YACjB,UAAU;YACV,gBAAgB;gBACd,UAAU;YACZ;QACF;QAEA,IAAI,MAAM;YACR,MAAM,IAAI,GAAG;QACf;QAEA,MAAM,WAAW,MAAM,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7C;YACA,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,YAAY;gBACZ,UAAU;YACZ;YACA,SAAS;gBACP;oBAAE,QAAQ;gBAAO;gBACjB;oBAAE,YAAY;gBAAO;aACtB;QACH;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iEAAiE;QAC/E,OAAO,EAAE;IACX;AACF;AAKO,eAAe,gBAAgB,SAAiB;IACrD,IAAI;QACF,MAAM,UAAU,MAAM,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBAAE,IAAI;YAAU;YACvB,SAAS;gBACP,WAAW;oBACT,SAAS;wBACP,OAAO;oBACT;gBACF;gBACA,aAAa;YACf;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,MAAM,iBAAiB,QAAQ,SAAS,CAAC,MAAM;QAC/C,MAAM,qBAAqB,QAAQ,SAAS,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QACtE,MAAM,qBAAqB,QAAQ,SAAS,CAAC,MAAM,CAAC,CAAA,IAClD;gBAAC;gBAAa;gBAAc;gBAAW;aAAmB,CAAC,QAAQ,CAAC,EAAE,MAAM;QAG9E,gCAAgC;QAChC,MAAM,mBAAmB,mBAAmB,MAAM,CAAC,CAAA;YACjD,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC,EAAE,iBAAiB,EAAE,OAAO;YACpD,OAAO,IAAI,KAAK,EAAE,YAAY,KAAK,IAAI,KAAK,EAAE,iBAAiB;QACjE,GAAG,MAAM;QAET,MAAM,aAAa,mBAAmB,MAAM,GAAG,IAC3C,AAAC,mBAAmB,mBAAmB,MAAM,GAAI,MACjD;QAEJ,kBAAkB;QAClB,MAAM,eAAe,QAAQ,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,kBAAkB,IAAI,CAAC,GAAG;QAE7F,2BAA2B;QAC3B,MAAM,gBAAgB,mBACnB,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,IAAI,EAAE,YAAY,EAC1C,GAAG,CAAC,CAAA;YACH,MAAM,SAAS,IAAI,KAAK,EAAE,UAAU,EAAG,OAAO;YAC9C,MAAM,WAAW,IAAI,KAAK,EAAE,YAAY,EAAG,OAAO;YAClD,OAAO,CAAC,WAAW,MAAM,IAAI,CAAC,OAAO,KAAK,EAAE,EAAE,YAAY;;QAC5D;QAEF,MAAM,sBAAsB,cAAc,MAAM,GAAG,IAC/C,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,MAAM,KAAK,cAAc,MAAM,GACzE;QAEJ,uBAAuB;QACvB,MAAM,kBAAkB,QAAQ,WAAW,CAAC,MAAM,GAAG,IAAI;YACvD,aAAa,QAAQ,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,WAAW,EAAE,KAAK,QAAQ,WAAW,CAAC,MAAM;YACxG,WAAW,QAAQ,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,SAAS,EAAE,KAAK,QAAQ,WAAW,CAAC,MAAM;YACpG,eAAe,QAAQ,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,aAAa,EAAE,KAAK,QAAQ,WAAW,CAAC,MAAM;YAC5G,SAAS,QAAQ,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,OAAO,EAAE,KAAK,QAAQ,WAAW,CAAC,MAAM;QAClG,IAAI;YACF,aAAa;YACb,WAAW;YACX,eAAe;YACf,SAAS;QACX;QAEA,OAAO;YACL;YACA,OAAO;gBACL;gBACA,oBAAoB,mBAAmB,MAAM;gBAC7C,oBAAoB,mBAAmB,MAAM;gBAC7C,YAAY,KAAK,KAAK,CAAC;gBACvB;gBACA,qBAAqB,KAAK,KAAK,CAAC;gBAChC,aAAa;YACf;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oEAAoE;QAClF,OAAO;IACT;AACF;;;IA1TsB;IAyEA;IAqCA;IAqCA;IAiDA;IAuCA;;AA3OA,+OAAA;AAyEA,+OAAA;AAqCA,+OAAA;AAqCA,+OAAA;AAiDA,+OAAA;AAuCA,+OAAA", "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/.next-internal/server/app/logistics/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getShipments as '400e43fc1f2aaf870f07bbb36850606ee4c235bdce'} from 'ACTIONS_MODULE0'\nexport {getShipmentByTracking as '406b8fdba6d30ab4721ed2f1c8df6beb052ce0e974'} from 'ACTIONS_MODULE0'\nexport {createShipment as '40bcac4285126bc163b2633c886c4d635a017aeb65'} from 'ACTIONS_MODULE0'\nexport {updateShipmentStatus as '788d9127ee73cd3c652d41a8288476f42b5bcd33bd'} from 'ACTIONS_MODULE0'\nexport {addTrackingEvent as '7891806b98b3184a75429095de2d10853776b202c0'} from 'ACTIONS_MODULE0'\nexport {getCarriers as '009fa320e69f1c62b3b79b32bd03df3de5c927a3fb'} from 'ACTIONS_MODULE1'\nexport {createCarrier as '4019a5d2f47e9c6c999cd87d906c352eec5a9fe4df'} from 'ACTIONS_MODULE1'\nexport {getCarrierStats as '405d57f67df39407509080f7e0d1b523bfd94405e9'} from 'ACTIONS_MODULE1'\nexport {evaluateCarrier as '40df4598c1bfa1dfd9332decd8fd2f6f039003ab03'} from 'ACTIONS_MODULE1'\nexport {updateCarrier as '602f31cff11360be9a26f5c8d1fd218ced8b2bea52'} from 'ACTIONS_MODULE1'\nexport {getAvailableCarriers as '60381a7196bf6b52946d1c5595accfb95e8c5a1b3b'} from 'ACTIONS_MODULE1'\n"], "names": [], "mappings": ";AAAA;AAKA", "debugId": null}}, {"offset": {"line": 887, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/LogisticsStats.tsx"], "sourcesContent": ["import { getShipments } from '@/lib/actions/shipments'\nimport { calculateLogisticsKPIs, formatDuration } from '@/lib/logistics'\nimport { Package, Truck, Clock, TrendingUp, DollarSign, CheckCircle } from 'lucide-react'\n\nasync function getLogisticsStats() {\n  const shipments = await getShipments()\n  const kpis = calculateLogisticsKPIs(shipments)\n  \n  return {\n    ...kpis,\n    shipments\n  }\n}\n\nexport async function LogisticsStats() {\n  const stats = await getLogisticsStats()\n\n  const statCards = [\n    {\n      title: 'Total Expéditions',\n      value: stats.totalShipments,\n      icon: Package,\n      color: 'bg-blue-50 text-blue-600',\n      bgColor: 'bg-blue-500'\n    },\n    {\n      title: 'En Transit',\n      value: stats.inTransitShipments,\n      icon: Truck,\n      color: 'bg-yellow-50 text-yellow-600',\n      bgColor: 'bg-yellow-500'\n    },\n    {\n      title: 'Livrées',\n      value: stats.deliveredShipments,\n      icon: CheckCircle,\n      color: 'bg-green-50 text-green-600',\n      bgColor: 'bg-green-500'\n    },\n    {\n      title: 'Temps Moyen',\n      value: formatDuration(stats.averageDeliveryTime),\n      icon: Clock,\n      color: 'bg-purple-50 text-purple-600',\n      bgColor: 'bg-purple-500'\n    },\n    {\n      title: 'Taux Ponctualité',\n      value: `${stats.onTimeDeliveryRate}%`,\n      icon: TrendingUp,\n      color: 'bg-indigo-50 text-indigo-600',\n      bgColor: 'bg-indigo-500'\n    },\n    {\n      title: 'Coûts Transport',\n      value: `${stats.totalTransportCosts.toLocaleString()} XOF`,\n      icon: DollarSign,\n      color: 'bg-orange-50 text-orange-600',\n      bgColor: 'bg-orange-500'\n    }\n  ]\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4\">\n      {statCards.map((stat, index) => {\n        const Icon = stat.icon\n        return (\n          <div key={index} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4\">\n            <div className=\"flex items-center\">\n              <div className={`h-10 w-10 rounded-lg flex items-center justify-center ${stat.color}`}>\n                <Icon className=\"h-5 w-5\" />\n              </div>\n              <div className=\"ml-3 flex-1\">\n                <p className=\"text-sm font-medium text-gray-600\">{stat.title}</p>\n                <p className=\"text-lg font-semibold text-gray-900\">{stat.value}</p>\n              </div>\n            </div>\n          </div>\n        )\n      })}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAEA,eAAe;IACb,MAAM,YAAY,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;IACnC,MAAM,OAAO,CAAA,GAAA,uHAAA,CAAA,yBAAsB,AAAD,EAAE;IAEpC,OAAO;QACL,GAAG,IAAI;QACP;IACF;AACF;AAEO,eAAe;IACpB,MAAM,QAAQ,MAAM;IAEpB,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,MAAM,cAAc;YAC3B,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,MAAM,kBAAkB;YAC/B,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,MAAM,kBAAkB;YAC/B,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,mBAAmB;YAC/C,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,GAAG,MAAM,kBAAkB,CAAC,CAAC,CAAC;YACrC,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,GAAG,MAAM,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC;YAC1D,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;QACX;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACZ,UAAU,GAAG,CAAC,CAAC,MAAM;YACpB,MAAM,OAAO,KAAK,IAAI;YACtB,qBACE,8OAAC;gBAAgB,WAAU;0BACzB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAW,CAAC,sDAAsD,EAAE,KAAK,KAAK,EAAE;sCACnF,cAAA,8OAAC;gCAAK,WAAU;;;;;;;;;;;sCAElB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAqC,KAAK,KAAK;;;;;;8CAC5D,8OAAC;oCAAE,WAAU;8CAAuC,KAAK,KAAK;;;;;;;;;;;;;;;;;;eAP1D;;;;;QAYd;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 1029, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/ShipmentStatusBadge.tsx"], "sourcesContent": ["import { getShipmentStatusColor, getShipmentStatusLabel } from '@/lib/logistics'\nimport { ShipmentStatus } from '@/types'\n\ninterface ShipmentStatusBadgeProps {\n  status: ShipmentStatus\n}\n\nexport function ShipmentStatusBadge({ status }: ShipmentStatusBadgeProps) {\n  const colorClass = getShipmentStatusColor(status)\n  const label = getShipmentStatusLabel(status)\n\n  return (\n    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>\n      {label}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOO,SAAS,oBAAoB,EAAE,MAAM,EAA4B;IACtE,MAAM,aAAa,CAAA,GAAA,uHAAA,CAAA,yBAAsB,AAAD,EAAE;IAC1C,MAAM,QAAQ,CAAA,GAAA,uHAAA,CAAA,yBAAsB,AAAD,EAAE;IAErC,qBACE,8OAAC;QAAK,WAAW,CAAC,wEAAwE,EAAE,YAAY;kBACrG;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1054, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/TrackingButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TrackingButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call TrackingButton() from the server but TrackingButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/logistics/TrackingButton.tsx <module evaluation>\",\n    \"TrackingButton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,6EACA", "debugId": null}}, {"offset": {"line": 1068, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/TrackingButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TrackingButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call TrackingButton() from the server but TrackingButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/logistics/TrackingButton.tsx\",\n    \"TrackingButton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,yDACA", "debugId": null}}, {"offset": {"line": 1082, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/ShipmentsList.tsx"], "sourcesContent": ["import { getShipments } from '@/lib/actions/shipments'\nimport { getShipmentStatusColor, getShipmentStatusLabel, formatDuration } from '@/lib/logistics'\nimport { Package, MapPin, Calendar, Truck, User, Building } from 'lucide-react'\nimport { ShipmentStatusBadge } from './ShipmentStatusBadge'\nimport { TrackingButton } from './TrackingButton'\n\nconst transportModeLabels = {\n  ROAD: 'Routier',\n  AIR_EXPRESS: 'Aérien Express'\n}\n\nconst transportModeIcons = {\n  ROAD: '🚛',\n  AIR_EXPRESS: '✈️'\n}\n\nexport async function ShipmentsList() {\n  const shipments = await getShipments()\n\n  if (shipments.length === 0) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center\">\n        <Package className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Aucune expédition</h3>\n        <p className=\"text-gray-600\">\n          Les expéditions créées apparaîtront ici.\n        </p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n      <div className=\"px-6 py-4 border-b border-gray-200\">\n        <h3 className=\"text-lg font-medium text-gray-900\">\n          Expéditions ({shipments.length})\n        </h3>\n      </div>\n      \n      <div className=\"divide-y divide-gray-200\">\n        {shipments.map((shipment) => (\n          <div key={shipment.id} className=\"p-6 hover:bg-gray-50\">\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex-1\">\n                {/* En-tête avec numéro de suivi et statut */}\n                <div className=\"flex items-center justify-between mb-3\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-lg\">\n                        {transportModeIcons[shipment.transportMode]}\n                      </span>\n                      <div>\n                        <p className=\"text-sm font-medium text-gray-900\">\n                          {shipment.trackingNumber}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">\n                          {transportModeLabels[shipment.transportMode]}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                  <ShipmentStatusBadge status={shipment.status} />\n                </div>\n\n                {/* Informations de la commande */}\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    <User className=\"h-4 w-4 text-gray-400\" />\n                    <div>\n                      <p className=\"text-xs text-gray-500\">Client</p>\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {shipment.order.customer.name}\n                      </p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center space-x-2\">\n                    <Building className=\"h-4 w-4 text-gray-400\" />\n                    <div>\n                      <p className=\"text-xs text-gray-500\">Transporteur</p>\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {shipment.carrier.name}\n                      </p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center space-x-2\">\n                    <MapPin className=\"h-4 w-4 text-gray-400\" />\n                    <div>\n                      <p className=\"text-xs text-gray-500\">Trajet</p>\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {shipment.originCity} → {shipment.destinationCity}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Informations de livraison */}\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Calendar className=\"h-4 w-4 text-gray-400\" />\n                    <div>\n                      <p className=\"text-xs text-gray-500\">Livraison estimée</p>\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {new Date(shipment.estimatedDelivery).toLocaleDateString('fr-FR')}\n                      </p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center space-x-2\">\n                    <Package className=\"h-4 w-4 text-gray-400\" />\n                    <div>\n                      <p className=\"text-xs text-gray-500\">Poids</p>\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {shipment.weight} kg\n                      </p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center space-x-2\">\n                    <Truck className=\"h-4 w-4 text-gray-400\" />\n                    <div>\n                      <p className=\"text-xs text-gray-500\">Coût transport</p>\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {shipment.transportCost.toLocaleString()} XOF\n                      </p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Localisation actuelle */}\n                {shipment.currentLocation && (\n                  <div className=\"mb-4\">\n                    <div className=\"flex items-center space-x-2\">\n                      <MapPin className=\"h-4 w-4 text-blue-500\" />\n                      <div>\n                        <p className=\"text-xs text-gray-500\">Localisation actuelle</p>\n                        <p className=\"text-sm font-medium text-blue-600\">\n                          {shipment.currentLocation}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {/* Dernier événement de suivi */}\n                {shipment.trackingEvents.length > 0 && (\n                  <div className=\"mb-4\">\n                    <div className=\"bg-gray-50 rounded-lg p-3\">\n                      <p className=\"text-xs text-gray-500 mb-1\">Dernier événement</p>\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {shipment.trackingEvents[0].description}\n                      </p>\n                      <p className=\"text-xs text-gray-500 mt-1\">\n                        {new Date(shipment.trackingEvents[0].timestamp).toLocaleString('fr-FR')} \n                        • {shipment.trackingEvents[0].location}\n                      </p>\n                    </div>\n                  </div>\n                )}\n\n                {/* Notes */}\n                {shipment.notes && (\n                  <div className=\"mb-4\">\n                    <p className=\"text-xs text-gray-500 mb-1\">Notes</p>\n                    <p className=\"text-sm text-gray-700\">{shipment.notes}</p>\n                  </div>\n                )}\n              </div>\n\n              {/* Actions */}\n              <div className=\"ml-6 flex flex-col space-y-2\">\n                <TrackingButton trackingNumber={shipment.trackingNumber} />\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;AAEA,MAAM,sBAAsB;IAC1B,MAAM;IACN,aAAa;AACf;AAEA,MAAM,qBAAqB;IACzB,MAAM;IACN,aAAa;AACf;AAEO,eAAe;IACpB,MAAM,YAAY,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;IAEnC,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAKnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;;wBAAoC;wBAClC,UAAU,MAAM;wBAAC;;;;;;;;;;;;0BAInC,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;wBAAsB,WAAU;kCAC/B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb,kBAAkB,CAAC,SAAS,aAAa,CAAC;;;;;;0EAE7C,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFACV,SAAS,cAAc;;;;;;kFAE1B,8OAAC;wEAAE,WAAU;kFACV,mBAAmB,CAAC,SAAS,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;8DAKpD,8OAAC,sJAAA,CAAA,sBAAmB;oDAAC,QAAQ,SAAS,MAAM;;;;;;;;;;;;sDAI9C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,8OAAC;oEAAE,WAAU;8EACV,SAAS,KAAK,CAAC,QAAQ,CAAC,IAAI;;;;;;;;;;;;;;;;;;8DAKnC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,8OAAC;oEAAE,WAAU;8EACV,SAAS,OAAO,CAAC,IAAI;;;;;;;;;;;;;;;;;;8DAK5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,8OAAC;oEAAE,WAAU;;wEACV,SAAS,UAAU;wEAAC;wEAAI,SAAS,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;sDAOzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,8OAAC;oEAAE,WAAU;8EACV,IAAI,KAAK,SAAS,iBAAiB,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;8DAK/D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,8OAAC;oEAAE,WAAU;;wEACV,SAAS,MAAM;wEAAC;;;;;;;;;;;;;;;;;;;8DAKvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,8OAAC;oEAAE,WAAU;;wEACV,SAAS,aAAa,CAAC,cAAc;wEAAG;;;;;;;;;;;;;;;;;;;;;;;;;wCAOhD,SAAS,eAAe,kBACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;0EACV,SAAS,eAAe;;;;;;;;;;;;;;;;;;;;;;;wCAQlC,SAAS,cAAc,CAAC,MAAM,GAAG,mBAChC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAC1C,8OAAC;wDAAE,WAAU;kEACV,SAAS,cAAc,CAAC,EAAE,CAAC,WAAW;;;;;;kEAEzC,8OAAC;wDAAE,WAAU;;4DACV,IAAI,KAAK,SAAS,cAAc,CAAC,EAAE,CAAC,SAAS,EAAE,cAAc,CAAC;4DAAS;4DACrE,SAAS,cAAc,CAAC,EAAE,CAAC,QAAQ;;;;;;;;;;;;;;;;;;wCAO7C,SAAS,KAAK,kBACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAyB,SAAS,KAAK;;;;;;;;;;;;;;;;;;8CAM1D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iJAAA,CAAA,iBAAc;wCAAC,gBAAgB,SAAS,cAAc;;;;;;;;;;;;;;;;;uBAnInD,SAAS,EAAE;;;;;;;;;;;;;;;;AA2I/B", "debugId": null}}, {"offset": {"line": 1683, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/AddCarrierButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AddCarrierButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call AddCarrierButton() from the server but AddCarrierButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/logistics/AddCarrierButton.tsx <module evaluation>\",\n    \"AddCarrierButton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,+EACA", "debugId": null}}, {"offset": {"line": 1697, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/AddCarrierButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AddCarrierButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call AddCarrierButton() from the server but AddCarrierButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/logistics/AddCarrierButton.tsx\",\n    \"AddCarrierButton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,2DACA", "debugId": null}}, {"offset": {"line": 1711, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1721, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/CarriersList.tsx"], "sourcesContent": ["import { getCarriers } from '@/lib/actions/carriers'\nimport { Truck, Star, Clock, Package, DollarSign, Phone, Mail, MapPin } from 'lucide-react'\nimport { AddCarrierButton } from './AddCarrierButton'\n\nconst cityLabels = {\n  LAGOS: 'Lagos',\n  ABUJA: 'Abuja',\n  KANO: 'Kano'\n}\n\nexport async function CarriersList() {\n  const carriers = await getCarriers()\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-medium text-gray-900\">\n          Transporteurs ({carriers.length})\n        </h3>\n        <AddCarrierButton />\n      </div>\n\n      {carriers.length === 0 ? (\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center\">\n          <Truck className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Aucun transporteur</h3>\n          <p className=\"text-gray-600 mb-4\">\n            Ajoutez des transporteurs pour gérer vos expéditions.\n          </p>\n          <AddCarrierButton />\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {carriers.map((carrier) => (\n            <div key={carrier.id} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              {/* En-tête du transporteur */}\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n                    <Truck className=\"h-6 w-6 text-blue-600\" />\n                  </div>\n                  <div>\n                    <h4 className=\"text-lg font-medium text-gray-900\">{carrier.name}</h4>\n                    <p className=\"text-sm text-gray-600\">{cityLabels[carrier.city]}</p>\n                  </div>\n                </div>\n                <div className=\"flex items-center space-x-1\">\n                  <Star className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                  <span className=\"text-sm font-medium text-gray-900\">\n                    {carrier.stats.averageRating.toFixed(1)}\n                  </span>\n                </div>\n              </div>\n\n              {/* Informations de contact */}\n              <div className=\"space-y-2 mb-4\">\n                <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n                  <Phone className=\"h-4 w-4\" />\n                  <span>{carrier.phone}</span>\n                </div>\n                {carrier.email && (\n                  <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n                    <Mail className=\"h-4 w-4\" />\n                    <span>{carrier.email}</span>\n                  </div>\n                )}\n                <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n                  <MapPin className=\"h-4 w-4\" />\n                  <span>{carrier.address}</span>\n                </div>\n              </div>\n\n              {/* Modes de transport */}\n              <div className=\"mb-4\">\n                <p className=\"text-xs text-gray-500 mb-2\">Modes de transport</p>\n                <div className=\"flex flex-wrap gap-2\">\n                  {carrier.transportModes.split(',').map((mode, index) => (\n                    <span\n                      key={index}\n                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                        mode === 'ROAD' \n                          ? 'bg-blue-100 text-blue-800' \n                          : 'bg-purple-100 text-purple-800'\n                      }`}\n                    >\n                      {mode === 'ROAD' ? '🚛 Routier' : '✈️ Aérien'}\n                    </span>\n                  ))}\n                </div>\n              </div>\n\n              {/* Capacité */}\n              {carrier.capacity && (\n                <div className=\"mb-4\">\n                  <p className=\"text-xs text-gray-500 mb-1\">Capacité</p>\n                  <p className=\"text-sm font-medium text-gray-900\">\n                    {carrier.capacity.toLocaleString()} kg\n                  </p>\n                </div>\n              )}\n\n              {/* Statistiques de performance */}\n              <div className=\"grid grid-cols-2 gap-4 mb-4\">\n                <div className=\"bg-gray-50 rounded-lg p-3\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Package className=\"h-4 w-4 text-gray-400\" />\n                    <div>\n                      <p className=\"text-xs text-gray-500\">Expéditions</p>\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {carrier.stats.totalShipments}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"bg-gray-50 rounded-lg p-3\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Clock className=\"h-4 w-4 text-gray-400\" />\n                    <div>\n                      <p className=\"text-xs text-gray-500\">Ponctualité</p>\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {carrier.stats.onTimeRate}%\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"bg-gray-50 rounded-lg p-3\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Package className=\"h-4 w-4 text-gray-400\" />\n                    <div>\n                      <p className=\"text-xs text-gray-500\">Livrées</p>\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {carrier.stats.deliveredShipments}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"bg-gray-50 rounded-lg p-3\">\n                  <div className=\"flex items-center space-x-2\">\n                    <DollarSign className=\"h-4 w-4 text-gray-400\" />\n                    <div>\n                      <p className=\"text-xs text-gray-500\">Revenus</p>\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {carrier.stats.totalCost.toLocaleString()} XOF\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Indicateur de statut */}\n              <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center justify-between\">\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                    carrier.isActive \n                      ? 'bg-green-100 text-green-800' \n                      : 'bg-gray-100 text-gray-800'\n                  }`}>\n                    {carrier.isActive ? 'Actif' : 'Inactif'}\n                  </span>\n                  \n                  <button className=\"text-sm text-blue-600 hover:text-blue-700\">\n                    Voir détails\n                  </button>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;AAEA,MAAM,aAAa;IACjB,OAAO;IACP,OAAO;IACP,MAAM;AACR;AAEO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD;IAEjC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAoC;4BAChC,SAAS,MAAM;4BAAC;;;;;;;kCAElC,8OAAC,mJAAA,CAAA,mBAAgB;;;;;;;;;;;YAGlB,SAAS,MAAM,KAAK,kBACnB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC,mJAAA,CAAA,mBAAgB;;;;;;;;;;qCAGnB,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wBAAqB,WAAU;;0CAE9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqC,QAAQ,IAAI;;;;;;kEAC/D,8OAAC;wDAAE,WAAU;kEAAyB,UAAU,CAAC,QAAQ,IAAI,CAAC;;;;;;;;;;;;;;;;;;kDAGlE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DACb,QAAQ,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;0CAM3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAM,QAAQ,KAAK;;;;;;;;;;;;oCAErB,QAAQ,KAAK,kBACZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAM,QAAQ,KAAK;;;;;;;;;;;;kDAGxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAM,QAAQ,OAAO;;;;;;;;;;;;;;;;;;0CAK1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,cAAc,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,sBAC5C,8OAAC;gDAEC,WAAW,CAAC,wEAAwE,EAClF,SAAS,SACL,8BACA,iCACJ;0DAED,SAAS,SAAS,eAAe;+CAP7B;;;;;;;;;;;;;;;;4BAcZ,QAAQ,QAAQ,kBACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,8OAAC;wCAAE,WAAU;;4CACV,QAAQ,QAAQ,CAAC,cAAc;4CAAG;;;;;;;;;;;;;0CAMzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEACV,QAAQ,KAAK,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;kDAMrC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;;gEACV,QAAQ,KAAK,CAAC,UAAU;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEACV,QAAQ,KAAK,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;kDAMzC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;;gEACV,QAAQ,KAAK,CAAC,SAAS,CAAC,cAAc;gEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQpD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAW,CAAC,wEAAwE,EACxF,QAAQ,QAAQ,GACZ,gCACA,6BACJ;sDACC,QAAQ,QAAQ,GAAG,UAAU;;;;;;sDAGhC,8OAAC;4CAAO,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;uBAnI1D,QAAQ,EAAE;;;;;;;;;;;;;;;;AA8IhC", "debugId": null}}, {"offset": {"line": 2302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/LogisticsFilters.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LogisticsFilters = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogisticsFilters() from the server but LogisticsFilters is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/logistics/LogisticsFilters.tsx <module evaluation>\",\n    \"LogisticsFilters\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,+EACA", "debugId": null}}, {"offset": {"line": 2316, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/LogisticsFilters.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LogisticsFilters = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogisticsFilters() from the server but LogisticsFilters is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/logistics/LogisticsFilters.tsx\",\n    \"LogisticsFilters\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,2DACA", "debugId": null}}, {"offset": {"line": 2330, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/CreateShipmentButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CreateShipmentButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call CreateShipmentButton() from the server but CreateShipmentButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/logistics/CreateShipmentButton.tsx <module evaluation>\",\n    \"CreateShipmentButton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,mFACA", "debugId": null}}, {"offset": {"line": 2354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/logistics/CreateShipmentButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CreateShipmentButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call CreateShipmentButton() from the server but CreateShipmentButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/logistics/CreateShipmentButton.tsx\",\n    \"CreateShipmentButton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,+DACA", "debugId": null}}, {"offset": {"line": 2368, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/tabs.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Tabs = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx <module evaluation>\",\n    \"Tabs\",\n);\nexport const TabsContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsContent() from the server but TabsContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx <module evaluation>\",\n    \"TabsContent\",\n);\nexport const TabsList = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsList() from the server but <PERSON><PERSON>List is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx <module evaluation>\",\n    \"TabsList\",\n);\nexport const TabsTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsTrigger() from the server but TabsTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx <module evaluation>\",\n    \"TabsTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,4DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,4DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4DACA", "debugId": null}}, {"offset": {"line": 2404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/tabs.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Tabs = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx\",\n    \"Tabs\",\n);\nexport const TabsContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsContent() from the server but TabsContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx\",\n    \"TabsContent\",\n);\nexport const TabsList = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsList() from the server but TabsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx\",\n    \"TabsList\",\n);\nexport const TabsTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsTrigger() from the server but TabsTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx\",\n    \"TabsTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,wCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,wCACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,wCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,wCACA", "debugId": null}}, {"offset": {"line": 2430, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2440, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/logistics/page.tsx"], "sourcesContent": ["import { Suspense } from 'react'\nimport { LogisticsStats } from '@/components/logistics/LogisticsStats'\nimport { ShipmentsList } from '@/components/logistics/ShipmentsList'\nimport { CarriersList } from '@/components/logistics/CarriersList'\nimport { LogisticsFilters } from '@/components/logistics/LogisticsFilters'\nimport { CreateShipmentButton } from '@/components/logistics/CreateShipmentButton'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\n\nexport default function LogisticsPage() {\n  return (\n    <div className=\"p-6\">\n      <div className=\"border-b border-gray-200 pb-4 mb-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Logistique</h1>\n            <p className=\"text-gray-600 mt-1\">\n              Gestion des expéditions Nigeria → Dakar\n            </p>\n          </div>\n          <CreateShipmentButton />\n        </div>\n      </div>\n\n      <Suspense fallback={<div>Chargement des statistiques...</div>}>\n        <LogisticsStats />\n      </Suspense>\n\n      <div className=\"mt-6\">\n        <Tabs defaultValue=\"shipments\" className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-2\">\n            <TabsTrigger value=\"shipments\">Expéditions</TabsTrigger>\n            <TabsTrigger value=\"carriers\">Transporteurs</TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"shipments\" className=\"mt-6\">\n            <div className=\"space-y-6\">\n              <LogisticsFilters />\n              <Suspense fallback={<div>Chargement des expéditions...</div>}>\n                <ShipmentsList />\n              </Suspense>\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"carriers\" className=\"mt-6\">\n            <Suspense fallback={<div>Chargement des transporteurs...</div>}>\n              <CarriersList />\n            </Suspense>\n          </TabsContent>\n        </Tabs>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;sCAIpC,8OAAC,uJAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;;0BAIzB,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;8BAAI;;;;;;0BACvB,cAAA,8OAAC,iJAAA,CAAA,iBAAc;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,cAAa;oBAAY,WAAU;;sCACvC,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAY;;;;;;8CAC/B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;;;;;;;sCAGhC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAY,WAAU;sCACvC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,mJAAA,CAAA,mBAAgB;;;;;kDACjB,8OAAC,qMAAA,CAAA,WAAQ;wCAAC,wBAAU,8OAAC;sDAAI;;;;;;kDACvB,cAAA,8OAAC,gJAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;;;;;sCAKpB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,8OAAC,qMAAA,CAAA,WAAQ;gCAAC,wBAAU,8OAAC;8CAAI;;;;;;0CACvB,cAAA,8OAAC,+IAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B", "debugId": null}}]}