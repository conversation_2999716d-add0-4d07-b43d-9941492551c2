'use client'

import { useState } from 'react'
import { Search, Filter, Truck, Plane, Clock, CheckCircle } from 'lucide-react'

export function ShipmentFilters() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<'ALL' | 'PENDING' | 'IN_TRANSIT' | 'DELIVERED' | 'CANCELLED'>('ALL')
  const [selectedTransport, setSelectedTransport] = useState<'ALL' | 'TRUCK' | 'AIR'>('ALL')
  const [selectedOrigin, setSelectedOrigin] = useState('')

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Search */}
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Rechercher par numéro de suivi..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Status Filter */}
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <select
            title="Statut"
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value as 'ALL' | 'PENDING' | 'IN_TRANSIT' | 'DELIVERED' | 'CANCELLED')}
          >
            <option value="ALL">Tous les statuts</option>
            <option value="PENDING">⏳ En attente</option>
            <option value="IN_TRANSIT">🚛 En transit</option>
            <option value="DELIVERED">✅ Livré</option>
            <option value="CANCELLED">❌ Annulé</option>
          </select>
        </div>

        {/* Transport Filter */}
        <div>
          <select
            title="Mode de transport"
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={selectedTransport}
            onChange={(e) => setSelectedTransport(e.target.value as 'ALL' | 'TRUCK' | 'AIR')}
          >
            <option value="ALL">Tous les transports</option>
            <option value="TRUCK">🚛 Routier</option>
            <option value="AIR">✈️ Aérien</option>
          </select>
        </div>

        {/* Origin Filter */}
        <div>
          <select
            title="Origine"
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={selectedOrigin}
            onChange={(e) => setSelectedOrigin(e.target.value)}
          >
            <option value="">Toutes les origines</option>
            <option value="Lagos">Lagos</option>
            <option value="Abuja">Abuja</option>
            <option value="Kano">Kano</option>
          </select>
        </div>

        {/* Quick Status Filters */}
        <div className="flex gap-2">
          <button
            type="button"
            onClick={() => setSelectedStatus(selectedStatus === 'IN_TRANSIT' ? 'ALL' : 'IN_TRANSIT')}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedStatus === 'IN_TRANSIT'
                ? 'bg-blue-100 text-blue-800 border border-blue-200'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Truck className="h-4 w-4" />
            En transit
          </button>
          <button
            type="button"
            onClick={() => setSelectedStatus(selectedStatus === 'DELIVERED' ? 'ALL' : 'DELIVERED')}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedStatus === 'DELIVERED'
                ? 'bg-green-100 text-green-800 border border-green-200'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <CheckCircle className="h-4 w-4" />
            Livré
          </button>
        </div>
      </div>

      {/* Active Filters */}
      {(searchTerm || selectedStatus !== 'ALL' || selectedTransport !== 'ALL' || selectedOrigin) && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center gap-2 flex-wrap">
            <span className="text-sm text-gray-600">Filtres actifs:</span>
            {searchTerm && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                Recherche: "{searchTerm}"
                <button
                  type="button"
                  onClick={() => setSearchTerm('')}
                  className="hover:bg-blue-200 rounded-full p-0.5"
                >
                  ×
                </button>
              </span>
            )}
            {selectedStatus !== 'ALL' && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                Statut: {selectedStatus}
                <button
                  type="button"
                  onClick={() => setSelectedStatus('ALL')}
                  className="hover:bg-purple-200 rounded-full p-0.5"
                >
                  ×
                </button>
              </span>
            )}
            {selectedTransport !== 'ALL' && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                Transport: {selectedTransport === 'TRUCK' ? 'Routier' : 'Aérien'}
                <button
                  type="button"
                  onClick={() => setSelectedTransport('ALL')}
                  className="hover:bg-orange-200 rounded-full p-0.5"
                >
                  ×
                </button>
              </span>
            )}
            {selectedOrigin && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                Origine: {selectedOrigin}
                <button
                  type="button"
                  onClick={() => setSelectedOrigin('')}
                  className="hover:bg-green-200 rounded-full p-0.5"
                >
                  ×
                </button>
              </span>
            )}
            <button
              type="button"
              onClick={() => {
                setSearchTerm('')
                setSelectedStatus('ALL')
                setSelectedTransport('ALL')
                setSelectedOrigin('')
              }}
              className="text-xs text-gray-500 hover:text-gray-700 underline"
            >
              Effacer tous les filtres
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
