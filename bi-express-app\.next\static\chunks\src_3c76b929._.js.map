{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/hooks/useApi.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\ninterface ApiState<T> {\n  data: T | null\n  loading: boolean\n  error: string | null\n}\n\ninterface ApiOptions {\n  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'\n  body?: any\n  headers?: Record<string, string>\n}\n\nexport function useApi<T>(url: string, options?: ApiOptions): ApiState<T> & { refetch: () => void } {\n  const [state, setState] = useState<ApiState<T>>({\n    data: null,\n    loading: true,\n    error: null\n  })\n\n  const fetchData = async () => {\n    try {\n      setState(prev => ({ ...prev, loading: true, error: null }))\n      \n      const fetchOptions: RequestInit = {\n        method: options?.method || 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          ...options?.headers\n        }\n      }\n\n      if (options?.body && options.method !== 'GET') {\n        fetchOptions.body = JSON.stringify(options.body)\n      }\n\n      const response = await fetch(url, fetchOptions)\n      \n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`)\n      }\n      \n      const data = await response.json()\n      setState({ data, loading: false, error: null })\n    } catch (error) {\n      setState({\n        data: null,\n        loading: false,\n        error: error instanceof Error ? error.message : 'Une erreur est survenue'\n      })\n    }\n  }\n\n  useEffect(() => {\n    fetchData()\n  }, [url, JSON.stringify(options)])\n\n  return {\n    ...state,\n    refetch: fetchData\n  }\n}\n\nexport async function apiCall<T>(url: string, options?: ApiOptions): Promise<T> {\n  const fetchOptions: RequestInit = {\n    method: options?.method || 'GET',\n    headers: {\n      'Content-Type': 'application/json',\n      ...options?.headers\n    }\n  }\n\n  if (options?.body && options.method !== 'GET') {\n    fetchOptions.body = JSON.stringify(options.body)\n  }\n\n  const response = await fetch(url, fetchOptions)\n  \n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}))\n    throw new Error(errorData.error || `HTTP error! status: ${response.status}`)\n  }\n  \n  return response.json()\n}\n\n// Hooks spécialisés pour chaque entité\nexport function useSuppliers() {\n  return useApi<any[]>('/api/suppliers')\n}\n\nexport function useCustomers() {\n  return useApi<any[]>('/api/customers')\n}\n\nexport function useProducts() {\n  return useApi<any[]>('/api/products')\n}\n\nexport function useOrders() {\n  return useApi<any[]>('/api/orders')\n}\n\nexport function useCarriers() {\n  return useApi<any[]>('/api/carriers')\n}\n\nexport function useShipments() {\n  return useApi<any[]>('/api/shipments')\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;AAFA;;AAgBO,SAAS,OAAU,GAAW,EAAE,OAAoB;;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAC9C,MAAM;QACN,SAAS;QACT,OAAO;IACT;IAEA,MAAM,YAAY;QAChB,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK,CAAC;YAEzD,MAAM,eAA4B;gBAChC,QAAQ,SAAS,UAAU;gBAC3B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,SAAS,OAAO;gBACrB;YACF;YAEA,IAAI,SAAS,QAAQ,QAAQ,MAAM,KAAK,OAAO;gBAC7C,aAAa,IAAI,GAAG,KAAK,SAAS,CAAC,QAAQ,IAAI;YACjD;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS;gBAAE;gBAAM,SAAS;gBAAO,OAAO;YAAK;QAC/C,EAAE,OAAO,OAAO;YACd,SAAS;gBACP,MAAM;gBACN,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR;QACF;2BAAG;QAAC;QAAK,KAAK,SAAS,CAAC;KAAS;IAEjC,OAAO;QACL,GAAG,KAAK;QACR,SAAS;IACX;AACF;GAhDgB;AAkDT,eAAe,QAAW,GAAW,EAAE,OAAoB;IAChE,MAAM,eAA4B;QAChC,QAAQ,SAAS,UAAU;QAC3B,SAAS;YACP,gBAAgB;YAChB,GAAG,SAAS,OAAO;QACrB;IACF;IAEA,IAAI,SAAS,QAAQ,QAAQ,MAAM,KAAK,OAAO;QAC7C,aAAa,IAAI,GAAG,KAAK,SAAS,CAAC,QAAQ,IAAI;IACjD;IAEA,MAAM,WAAW,MAAM,MAAM,KAAK;IAElC,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QACvD,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;IAC7E;IAEA,OAAO,SAAS,IAAI;AACtB;AAGO,SAAS;;IACd,OAAO,OAAc;AACvB;IAFgB;;QACP;;;AAGF,SAAS;;IACd,OAAO,OAAc;AACvB;IAFgB;;QACP;;;AAGF,SAAS;;IACd,OAAO,OAAc;AACvB;IAFgB;;QACP;;;AAGF,SAAS;;IACd,OAAO,OAAc;AACvB;IAFgB;;QACP;;;AAGF,SAAS;;IACd,OAAO,OAAc;AACvB;IAFgB;;QACP;;;AAGF,SAAS;;IACd,OAAO,OAAc;AACvB;IAFgB;;QACP", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/pricing.ts"], "sourcesContent": ["\n// Utilitaires pour les calculs de prix et marges\nimport { CustomerType, TransportMode, UrgencyLevel } from '@prisma/client'\n\nexport interface PricingCalculation {\n  supplierPrice: number;\n  logisticCosts: number;\n  costPrice: number; // Prix de revient\n  sellingPrice: number;\n  profit: number;\n  marginPercentage: number;\n}\n\n// Types pour les calculs de tarification avancée\nexport interface PricingRequest {\n  customerType: CustomerType\n  transportMode: TransportMode\n  originCity: string\n  destinationCity: string\n  weight: number\n  volume?: number\n  declaredValue?: number\n  urgency?: UrgencyLevel\n  insuranceRequired?: boolean\n}\n\nexport interface PricingResult {\n  baseTransportCost: number\n  handlingFees: number\n  fuelSurcharge: number\n  insuranceCost: number\n  urgencySurcharge: number\n  bulkDiscount: number\n  totalCost: number\n  estimatedDays: number\n  breakdown: PricingBreakdown[]\n}\n\nexport interface PricingBreakdown {\n  item: string\n  amount: number\n  description: string\n}\n\n/**\n * Calcule tous les prix et marges pour un produit\n */\nexport function calculatePricing(\n  supplierPrice: number,\n  logisticRate: number = 0.30,\n  marginRate: number = 0.20\n): PricingCalculation {\n  const logisticCosts = supplierPrice * logisticRate;\n  const costPrice = supplierPrice + logisticCosts;\n  const sellingPrice = costPrice * (1 + marginRate);\n  const profit = sellingPrice - costPrice;\n  const marginPercentage = (profit / sellingPrice) * 100;\n\n  return {\n    supplierPrice,\n    logisticCosts,\n    costPrice,\n    sellingPrice,\n    profit,\n    marginPercentage\n  };\n}\n\n/**\n * Calcule le prix de vente à partir d'une marge souhaitée\n */\nexport function calculateSellingPriceFromMargin(\n  supplierPrice: number,\n  logisticRate: number,\n  desiredMarginPercentage: number\n): number {\n  const logisticCosts = supplierPrice * logisticRate;\n  const costPrice = supplierPrice + logisticCosts;\n  return costPrice / (1 - desiredMarginPercentage / 100);\n}\n\n/**\n * Calcule les frais de transport selon le mode\n */\nexport function calculateTransportCosts(\n  weight: number,\n  mode: 'ROAD' | 'AIR_EXPRESS'\n): number {\n  const rates = {\n    ROAD: 500, // 500 XOF par kg\n    AIR_EXPRESS: 2000 // 2000 XOF par kg\n  };\n  \n  return weight * rates[mode];\n}\n\n/**\n * Formate un montant en devise\n */\nexport function formatCurrency(\n  amount: number,\n  currency: 'NGN' | 'XOF' = 'XOF'\n): string {\n  const symbols = {\n    NGN: '₦',\n    XOF: 'CFA'\n  };\n  \n  return `${amount.toLocaleString('fr-FR')} ${symbols[currency]}`;\n}\n\n/**\n * Convertit NGN vers XOF\n */\nexport function convertNGNtoXOF(\n  amountNGN: number,\n  exchangeRate: number = 0.85\n): number {\n  return amountNGN * exchangeRate;\n}\n\n// Configuration des tarifs de base pour transport différencié\nconst BASE_PRICING = {\n  // Transport routier (par kg)\n  ROAD: {\n    LAGOS_DAKAR: { pricePerKg: 850, minimumCharge: 25000, estimatedDays: 6 },\n    ABUJA_DAKAR: { pricePerKg: 900, minimumCharge: 27000, estimatedDays: 7 },\n    KANO_DAKAR: { pricePerKg: 750, minimumCharge: 22000, estimatedDays: 5 }\n  },\n  // Transport aérien express (par kg)\n  AIR_EXPRESS: {\n    LAGOS_DAKAR: { pricePerKg: 2500, minimumCharge: 45000, estimatedDays: 2 },\n    ABUJA_DAKAR: { pricePerKg: 2800, minimumCharge: 50000, estimatedDays: 2 },\n    KANO_DAKAR: { pricePerKg: 3200, minimumCharge: 55000, estimatedDays: 2 }\n  }\n}\n\n// Frais additionnels\nconst ADDITIONAL_FEES = {\n  handlingFeeRate: 0.05, // 5% du coût de transport\n  fuelSurchargeRate: 0.12, // 12% du coût de transport\n  insuranceRate: 0.005, // 0.5% de la valeur déclarée\n  urgencyMultiplier: {\n    STANDARD: 1.0,\n    EXPRESS: 1.3,\n    URGENT: 1.8\n  }\n}\n\n// Remises volume (seuils en kg)\nconst BULK_DISCOUNTS = [\n  { minWeight: 1000, discount: 0.05 }, // 5% à partir de 1 tonne\n  { minWeight: 2000, discount: 0.08 }, // 8% à partir de 2 tonnes\n  { minWeight: 5000, discount: 0.12 }, // 12% à partir de 5 tonnes\n  { minWeight: 10000, discount: 0.15 } // 15% à partir de 10 tonnes\n]\n\n// Marges par type de client\nconst CLIENT_MARGINS = {\n  LOGISTICS: 0.20, // 20% de marge pour clients logistique\n  COMMERCE: 0.35   // 35% de marge intégrée pour clients commerce\n}\n\n/**\n * Calcule le tarif pour un client logistique (transport seul)\n */\nexport function calculateLogisticsPricing(request: PricingRequest): PricingResult {\n  const routeKey = `${request.originCity.toUpperCase()}_${request.destinationCity.toUpperCase()}`\n  const modeConfig = BASE_PRICING[request.transportMode]\n  const routeConfig = modeConfig[routeKey as keyof typeof modeConfig]\n\n  // Coût de transport de base\n  const baseTransportCost = Math.max(\n    request.weight * routeConfig.pricePerKg,\n    routeConfig.minimumCharge\n  )\n  \n  // Application de la marge client logistique\n  const totalCost = subtotal * (1 + CLIENT_MARGINS.LOGISTICS)\n\n"], "names": [], "mappings": "AACA,iDAAiD;;;;;;;;;AA8C1C,SAAS,iBACd,aAAqB,EACrB,eAAuB,IAAI,EAC3B,aAAqB,IAAI;IAEzB,MAAM,gBAAgB,gBAAgB;IACtC,MAAM,YAAY,gBAAgB;IAClC,MAAM,eAAe,YAAY,CAAC,IAAI,UAAU;IAChD,MAAM,SAAS,eAAe;IAC9B,MAAM,mBAAmB,AAAC,SAAS,eAAgB;IAEnD,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS,gCACd,aAAqB,EACrB,YAAoB,EACpB,uBAA+B;IAE/B,MAAM,gBAAgB,gBAAgB;IACtC,MAAM,YAAY,gBAAgB;IAClC,OAAO,YAAY,CAAC,IAAI,0BAA0B,GAAG;AACvD;AAKO,SAAS,wBACd,MAAc,EACd,IAA4B;IAE5B,MAAM,QAAQ;QACZ,MAAM;QACN,aAAa,KAAK,kBAAkB;IACtC;IAEA,OAAO,SAAS,KAAK,CAAC,KAAK;AAC7B;AAKO,SAAS,eACd,MAAc,EACd,WAA0B,KAAK;IAE/B,MAAM,UAAU;QACd,KAAK;QACL,KAAK;IACP;IAEA,OAAO,GAAG,OAAO,cAAc,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,SAAS,EAAE;AACjE;AAKO,SAAS,gBACd,SAAiB,EACjB,eAAuB,IAAI;IAE3B,OAAO,YAAY;AACrB;AAEA,8DAA8D;AAC9D,MAAM,eAAe;IACnB,6BAA6B;IAC7B,MAAM;QACJ,aAAa;YAAE,YAAY;YAAK,eAAe;YAAO,eAAe;QAAE;QACvE,aAAa;YAAE,YAAY;YAAK,eAAe;YAAO,eAAe;QAAE;QACvE,YAAY;YAAE,YAAY;YAAK,eAAe;YAAO,eAAe;QAAE;IACxE;IACA,oCAAoC;IACpC,aAAa;QACX,aAAa;YAAE,YAAY;YAAM,eAAe;YAAO,eAAe;QAAE;QACxE,aAAa;YAAE,YAAY;YAAM,eAAe;YAAO,eAAe;QAAE;QACxE,YAAY;YAAE,YAAY;YAAM,eAAe;YAAO,eAAe;QAAE;IACzE;AACF;AAEA,qBAAqB;AACrB,MAAM,kBAAkB;IACtB,iBAAiB;IACjB,mBAAmB;IACnB,eAAe;IACf,mBAAmB;QACjB,UAAU;QACV,SAAS;QACT,QAAQ;IACV;AACF;AAEA,gCAAgC;AAChC,MAAM,iBAAiB;IACrB;QAAE,WAAW;QAAM,UAAU;IAAK;IAClC;QAAE,WAAW;QAAM,UAAU;IAAK;IAClC;QAAE,WAAW;QAAM,UAAU;IAAK;IAClC;QAAE,WAAW;QAAO,UAAU;IAAK,EAAE,4BAA4B;CAClE;AAED,4BAA4B;AAC5B,MAAM,iBAAiB;IACrB,WAAW;IACX,UAAU,KAAO,8CAA8C;AACjE;AAKO,SAAS,0BAA0B,OAAuB;IAC/D,MAAM,WAAW,GAAG,QAAQ,UAAU,CAAC,WAAW,GAAG,CAAC,EAAE,QAAQ,eAAe,CAAC,WAAW,IAAI;IAC/F,MAAM,aAAa,YAAY,CAAC,QAAQ,aAAa,CAAC;IACtD,MAAM,cAAc,UAAU,CAAC,SAAoC;IAEnE,4BAA4B;IAC5B,MAAM,oBAAoB,KAAK,GAAG,CAChC,QAAQ,MAAM,GAAG,YAAY,UAAU,EACvC,YAAY,aAAa;IAG3B,4CAA4C;IAC5C,MAAM,YAAY,WAAW,CAAC,IAAI,eAAe,SAAS;AAAA", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/ProductsList.tsx"], "sourcesContent": ["'use client'\n\nimport { useProducts } from '@/hooks/useApi'\nimport { formatCurrency } from '@/lib/utils'\nimport { calculatePricing } from '@/lib/pricing'\nimport { Badge } from '@/components/ui/badge-component'\nimport { Package, AlertTriangle, TrendingUp, Edit, Eye, Loader2, AlertCircle } from 'lucide-react'\n\nconst categoryColors = {\n  TISSUS: 'bg-blue-100 text-blue-800',\n  COSMETIQUES: 'bg-pink-100 text-pink-800',\n  MECHES: 'bg-purple-100 text-purple-800'\n}\n\nconst categoryLabels = {\n  TISSUS: 'Tissus',\n  COSMETIQUES: 'Cosmétiques',\n  MECHES: 'Mèches'\n}\n\nconst cityColors = {\n  LAGOS: 'bg-blue-50 text-blue-700',\n  ABUJA: 'bg-green-50 text-green-700',\n  KANO: 'bg-purple-50 text-purple-700'\n}\n\nexport function ProductsList() {\n  const { data: products, loading, error, refetch } = useProducts()\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center justify-center py-12\">\n          <Loader2 className=\"h-8 w-8 animate-spin text-blue-600\" />\n          <span className=\"ml-2 text-gray-600\">Chargement des produits...</span>\n        </div>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center justify-center py-12\">\n          <AlertCircle className=\"h-8 w-8 text-red-500\" />\n          <div className=\"ml-3\">\n            <p className=\"text-red-600 font-medium\">Erreur de chargement</p>\n            <p className=\"text-red-500 text-sm\">{error}</p>\n            <button\n              type=\"button\"\n              onClick={refetch}\n              className=\"mt-2 text-blue-600 hover:text-blue-700 text-sm font-medium\"\n            >\n              Réessayer\n            </button>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!products || products.length === 0) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"text-center py-12\">\n          <Package className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n          <p className=\"text-gray-500 text-lg\">Aucun produit trouvé</p>\n          <p className=\"text-gray-400 text-sm mt-1\">\n            Commencez par ajouter votre premier produit\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n      <div className=\"overflow-x-auto\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Produit\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Fournisseur\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Prix & Marges\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Stock\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Bénéfice\n              </th>\n              <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Actions\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {products.map((product) => {\n              const pricing = calculatePricing(\n                product.supplierPrice,\n                product.logisticRate,\n                product.margin\n              )\n              const isLowStock = product.stockQuantity <= product.minStockAlert\n\n              return (\n                <tr key={product.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0 h-10 w-10\">\n                        <div className=\"h-10 w-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center\">\n                          <Package className=\"h-5 w-5 text-white\" />\n                        </div>\n                      </div>\n                      <div className=\"ml-4\">\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {product.name}\n                        </div>\n                        <div className=\"flex items-center space-x-2 mt-1\">\n                          <Badge className={categoryColors[product.category]}>\n                            {categoryLabels[product.category]}\n                          </Badge>\n                          {/* Spécificités selon la catégorie */}\n                          {product.category === 'TISSUS' && product.fabricType && (\n                            <span className=\"text-xs text-gray-500\">\n                              {product.fabricType} - {product.width}m\n                            </span>\n                          )}\n                          {product.category === 'COSMETIQUES' && product.brand && (\n                            <span className=\"text-xs text-gray-500\">\n                              {product.brand} - {product.volume}ml\n                            </span>\n                          )}\n                          {product.category === 'MECHES' && product.length && (\n                            <span className=\"text-xs text-gray-500\">\n                              {product.length}\" - {product.hairType}\n                            </span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      {product.supplier.name}\n                    </div>\n                    <Badge className={cityColors[product.supplier.city]}>\n                      {product.supplier.city}\n                    </Badge>\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm text-gray-900\">\n                      <div className=\"font-medium\">\n                        {formatCurrency(pricing.sellingPrice)}\n                      </div>\n                      <div className=\"text-xs text-gray-500\">\n                        Fournisseur: {formatCurrency(pricing.supplierPrice)}\n                      </div>\n                      <div className=\"text-xs text-gray-500\">\n                        Logistique: {formatCurrency(pricing.logisticCosts)}\n                      </div>\n                      <div className=\"text-xs text-green-600\">\n                        Marge: {pricing.marginPercentage.toFixed(1)}%\n                      </div>\n                    </div>\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        {product.stockQuantity}\n                      </div>\n                      {isLowStock && (\n                        <AlertTriangle className=\"h-4 w-4 text-red-500 ml-2\" />\n                      )}\n                    </div>\n                    <div className=\"text-xs text-gray-500\">\n                      Min: {product.minStockAlert}\n                    </div>\n                    {isLowStock && (\n                      <div className=\"text-xs text-red-600 font-medium\">\n                        Stock faible !\n                      </div>\n                    )}\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-green-600\">\n                      {formatCurrency(pricing.profit)}\n                    </div>\n                    <div className=\"text-xs text-gray-500\">\n                      par unité\n                    </div>\n                    <div className=\"text-xs text-green-600\">\n                      Total: {formatCurrency(pricing.profit * product.stockQuantity)}\n                    </div>\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                    <div className=\"flex items-center justify-end space-x-2\">\n                      <button\n                        type=\"button\"\n                        title=\"Voir les détails\"\n                        className=\"text-blue-600 hover:text-blue-900\"\n                      >\n                        <Eye className=\"h-4 w-4\" />\n                      </button>\n                      <button\n                        type=\"button\"\n                        title=\"Modifier\"\n                        className=\"text-gray-600 hover:text-gray-900\"\n                      >\n                        <Edit className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              )\n            })}\n          </tbody>\n        </table>\n      </div>\n      \n      {products.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Package className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n          <p className=\"text-gray-500 text-lg\">Aucun produit trouvé</p>\n          <p className=\"text-gray-400 text-sm mt-1\">\n            Commencez par ajouter votre premier produit\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQA,MAAM,iBAAiB;IACrB,QAAQ;IACR,aAAa;IACb,QAAQ;AACV;AAEA,MAAM,iBAAiB;IACrB,QAAQ;IACR,aAAa;IACb,QAAQ;AACV;AAEA,MAAM,aAAa;IACjB,OAAO;IACP,OAAO;IACP,MAAM;AACR;AAEO,SAAS;;IACd,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IAE9D,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAK,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI7C;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA2B;;;;;;0CACxC,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;0CACrC,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;QACtC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAMlD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;4BAAM,WAAU;sCACf,cAAA,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAkF;;;;;;;;;;;;;;;;;sCAKpG,6LAAC;4BAAM,WAAU;sCACd,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,UAAU,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD,EAC7B,QAAQ,aAAa,EACrB,QAAQ,YAAY,EACpB,QAAQ,MAAM;gCAEhB,MAAM,aAAa,QAAQ,aAAa,IAAI,QAAQ,aAAa;gCAEjE,qBACE,6LAAC;oCAAoB,WAAU;;sDAC7B,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAGvB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACZ,QAAQ,IAAI;;;;;;0EAEf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,iJAAA,CAAA,QAAK;wEAAC,WAAW,cAAc,CAAC,QAAQ,QAAQ,CAAC;kFAC/C,cAAc,CAAC,QAAQ,QAAQ,CAAC;;;;;;oEAGlC,QAAQ,QAAQ,KAAK,YAAY,QAAQ,UAAU,kBAClD,6LAAC;wEAAK,WAAU;;4EACb,QAAQ,UAAU;4EAAC;4EAAI,QAAQ,KAAK;4EAAC;;;;;;;oEAGzC,QAAQ,QAAQ,KAAK,iBAAiB,QAAQ,KAAK,kBAClD,6LAAC;wEAAK,WAAU;;4EACb,QAAQ,KAAK;4EAAC;4EAAI,QAAQ,MAAM;4EAAC;;;;;;;oEAGrC,QAAQ,QAAQ,KAAK,YAAY,QAAQ,MAAM,kBAC9C,6LAAC;wEAAK,WAAU;;4EACb,QAAQ,MAAM;4EAAC;4EAAK,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQjD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,QAAQ,CAAC,IAAI;;;;;;8DAExB,6LAAC,iJAAA,CAAA,QAAK;oDAAC,WAAW,UAAU,CAAC,QAAQ,QAAQ,CAAC,IAAI,CAAC;8DAChD,QAAQ,QAAQ,CAAC,IAAI;;;;;;;;;;;;sDAI1B,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,YAAY;;;;;;kEAEtC,6LAAC;wDAAI,WAAU;;4DAAwB;4DACvB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,aAAa;;;;;;;kEAEpD,6LAAC;wDAAI,WAAU;;4DAAwB;4DACxB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,aAAa;;;;;;;kEAEnD,6LAAC;wDAAI,WAAU;;4DAAyB;4DAC9B,QAAQ,gBAAgB,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;sDAKlD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,QAAQ,aAAa;;;;;;wDAEvB,4BACC,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;;;;;;;8DAG7B,6LAAC;oDAAI,WAAU;;wDAAwB;wDAC/B,QAAQ,aAAa;;;;;;;gDAE5B,4BACC,6LAAC;oDAAI,WAAU;8DAAmC;;;;;;;;;;;;sDAMtD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;;;;;;8DAEhC,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;8DAGvC,6LAAC;oDAAI,WAAU;;wDAAyB;wDAC9B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM,GAAG,QAAQ,aAAa;;;;;;;;;;;;;sDAIjE,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,OAAM;wDACN,WAAU;kEAEV,cAAA,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;kEAEjB,6LAAC;wDACC,MAAK;wDACL,OAAM;wDACN,WAAU;kEAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mCA5Gf,QAAQ,EAAE;;;;;4BAkHvB;;;;;;;;;;;;;;;;;YAKL,SAAS,MAAM,KAAK,mBACnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;;AAOpD;GAvNgB;;QACsC,yHAAA,CAAA,cAAW;;;KADjD", "debugId": null}}, {"offset": {"line": 956, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/ProductFilters.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Search, Filter, AlertTriangle } from 'lucide-react'\n\nexport function ProductFilters() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState('')\n  const [selectedCity, setSelectedCity] = useState('')\n  const [showLowStock, setShowLowStock] = useState(false)\n  const [priceRange, setPriceRange] = useState('')\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4\">\n        {/* Search */}\n        <div className=\"lg:col-span-2 relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n          <input\n            type=\"text\"\n            placeholder=\"Rechercher un produit...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n        </div>\n\n        {/* Category Filter */}\n        <div>\n          <select\n            value={selectedCategory}\n            onChange={(e) => setSelectedCategory(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"\">Toutes catégories</option>\n            <option value=\"TISSUS\">Tissus</option>\n            <option value=\"COSMETIQUES\">Cosmétiques</option>\n            <option value=\"MECHES\">Mèches</option>\n          </select>\n        </div>\n\n        {/* City Filter */}\n        <div>\n          <select\n            value={selectedCity}\n            onChange={(e) => setSelectedCity(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"\">Toutes villes</option>\n            <option value=\"LAGOS\">Lagos</option>\n            <option value=\"ABUJA\">Abuja</option>\n            <option value=\"KANO\">Kano</option>\n          </select>\n        </div>\n\n        {/* Price Range */}\n        <div>\n          <select\n            value={priceRange}\n            onChange={(e) => setPriceRange(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"\">Tous prix</option>\n            <option value=\"0-10000\">0 - 10,000 CFA</option>\n            <option value=\"10000-50000\">10,000 - 50,000 CFA</option>\n            <option value=\"50000-100000\">50,000 - 100,000 CFA</option>\n            <option value=\"100000+\">100,000+ CFA</option>\n          </select>\n        </div>\n\n        {/* Actions */}\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={() => setShowLowStock(!showLowStock)}\n            className={`flex items-center px-3 py-2 rounded-md transition-colors ${\n              showLowStock\n                ? 'bg-red-100 text-red-700 border border-red-300'\n                : 'bg-gray-100 text-gray-700 border border-gray-300'\n            }`}\n          >\n            <AlertTriangle className=\"h-4 w-4 mr-1\" />\n            Stock faible\n          </button>\n        </div>\n      </div>\n\n      {/* Active Filters */}\n      {(searchTerm || selectedCategory || selectedCity || showLowStock || priceRange) && (\n        <div className=\"mt-4 flex flex-wrap items-center gap-2\">\n          <span className=\"text-sm text-gray-500\">Filtres actifs:</span>\n          \n          {searchTerm && (\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n              Recherche: {searchTerm}\n              <button\n                onClick={() => setSearchTerm('')}\n                className=\"ml-1 text-blue-600 hover:text-blue-800\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n          \n          {selectedCategory && (\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800\">\n              Catégorie: {selectedCategory}\n              <button\n                onClick={() => setSelectedCategory('')}\n                className=\"ml-1 text-purple-600 hover:text-purple-800\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n          \n          {selectedCity && (\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n              Ville: {selectedCity}\n              <button\n                onClick={() => setSelectedCity('')}\n                className=\"ml-1 text-green-600 hover:text-green-800\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n          \n          {showLowStock && (\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n              Stock faible\n              <button\n                onClick={() => setShowLowStock(false)}\n                className=\"ml-1 text-red-600 hover:text-red-800\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n          \n          {priceRange && (\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n              Prix: {priceRange} CFA\n              <button\n                onClick={() => setPriceRange('')}\n                className=\"ml-1 text-yellow-600 hover:text-yellow-800\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n          \n          <button\n            onClick={() => {\n              setSearchTerm('')\n              setSelectedCategory('')\n              setSelectedCity('')\n              setShowLowStock(false)\n              setPriceRange('')\n            }}\n            className=\"text-xs text-gray-500 hover:text-gray-700 underline\"\n          >\n            Effacer tous les filtres\n          </button>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;kCAKd,6LAAC;kCACC,cAAA,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4BACnD,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,6LAAC;oCAAO,OAAM;8CAAS;;;;;;8CACvB,6LAAC;oCAAO,OAAM;8CAAc;;;;;;8CAC5B,6LAAC;oCAAO,OAAM;8CAAS;;;;;;;;;;;;;;;;;kCAK3B,6LAAC;kCACC,cAAA,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4BAC/C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,6LAAC;oCAAO,OAAM;8CAAQ;;;;;;8CACtB,6LAAC;oCAAO,OAAM;8CAAQ;;;;;;8CACtB,6LAAC;oCAAO,OAAM;8CAAO;;;;;;;;;;;;;;;;;kCAKzB,6LAAC;kCACC,cAAA,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,6LAAC;oCAAO,OAAM;8CAAU;;;;;;8CACxB,6LAAC;oCAAO,OAAM;8CAAc;;;;;;8CAC5B,6LAAC;oCAAO,OAAM;8CAAe;;;;;;8CAC7B,6LAAC;oCAAO,OAAM;8CAAU;;;;;;;;;;;;;;;;;kCAK5B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS,IAAM,gBAAgB,CAAC;4BAChC,WAAW,CAAC,yDAAyD,EACnE,eACI,kDACA,oDACJ;;8CAEF,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;YAO/C,CAAC,cAAc,oBAAoB,gBAAgB,gBAAgB,UAAU,mBAC5E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;oBAEvC,4BACC,6LAAC;wBAAK,WAAU;;4BAAoG;4BACtG;0CACZ,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAU;0CACX;;;;;;;;;;;;oBAMJ,kCACC,6LAAC;wBAAK,WAAU;;4BAAwG;4BAC1G;0CACZ,6LAAC;gCACC,SAAS,IAAM,oBAAoB;gCACnC,WAAU;0CACX;;;;;;;;;;;;oBAMJ,8BACC,6LAAC;wBAAK,WAAU;;4BAAsG;4BAC5G;0CACR,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;;;;;;;oBAMJ,8BACC,6LAAC;wBAAK,WAAU;;4BAAkG;0CAEhH,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;;;;;;;oBAMJ,4BACC,6LAAC;wBAAK,WAAU;;4BAAwG;4BAC/G;4BAAW;0CAClB,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAU;0CACX;;;;;;;;;;;;kCAML,6LAAC;wBACC,SAAS;4BACP,cAAc;4BACd,oBAAoB;4BACpB,gBAAgB;4BAChB,gBAAgB;4BAChB,cAAc;wBAChB;wBACA,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAOX;GAlKgB;KAAA", "debugId": null}}, {"offset": {"line": 1346, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/AddProductButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Plus } from 'lucide-react'\n\nexport function AddProductButton() {\n  const [isOpen, setIsOpen] = useState(false)\n\n  return (\n    <>\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n      >\n        <Plus className=\"h-4 w-4 mr-2\" />\n        Ajouter un produit\n      </button>\n\n      {/* Modal placeholder - à implémenter plus tard */}\n      {isOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\n            <h2 className=\"text-lg font-semibold mb-4\">Ajouter un produit</h2>\n            \n            <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Nom du produit\n                  </label>\n                  <input\n                    type=\"text\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"Ex: Wax Hollandais Premium\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Catégorie\n                  </label>\n                  <select className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n                    <option value=\"\">Sélectionner une catégorie</option>\n                    <option value=\"TISSUS\">Tissus</option>\n                    <option value=\"COSMETIQUES\">Cosmétiques</option>\n                    <option value=\"MECHES\">Mèches</option>\n                  </select>\n                </div>\n              </div>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Prix fournisseur (NGN)\n                  </label>\n                  <input\n                    type=\"number\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"15000\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Taux logistique (%)\n                  </label>\n                  <input\n                    type=\"number\"\n                    defaultValue=\"30\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Marge (%)\n                  </label>\n                  <input\n                    type=\"number\"\n                    defaultValue=\"20\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n              \n              <div className=\"bg-blue-50 p-4 rounded-lg\">\n                <h3 className=\"text-sm font-medium text-blue-900 mb-2\">Calcul automatique des prix</h3>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-blue-700\">Prix de revient:</span>\n                    <span className=\"font-medium ml-2\">19,500 NGN</span>\n                  </div>\n                  <div>\n                    <span className=\"text-blue-700\">Prix de vente:</span>\n                    <span className=\"font-medium ml-2\">23,400 NGN</span>\n                  </div>\n                  <div>\n                    <span className=\"text-blue-700\">Bénéfice unitaire:</span>\n                    <span className=\"font-medium ml-2 text-green-600\">3,900 NGN</span>\n                  </div>\n                  <div>\n                    <span className=\"text-blue-700\">Prix CFA:</span>\n                    <span className=\"font-medium ml-2\">~15,600 CFA</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"flex justify-end space-x-3 mt-6\">\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors\"\n              >\n                Annuler\n              </button>\n              <button className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\">\n                Ajouter le produit\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE;;0BACE,6LAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAU;;kCAEV,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;YAKlC,wBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAE3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC;4DAAO,OAAM;sEAAG;;;;;;sEACjB,6LAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,6LAAC;4DAAO,OAAM;sEAAc;;;;;;sEAC5B,6LAAC;4DAAO,OAAM;sEAAS;;;;;;;;;;;;;;;;;;;;;;;;8CAK7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,cAAa;oDACb,WAAU;;;;;;;;;;;;sDAId,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,cAAa;oDACb,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;;;;;;;8DAErC,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;;;;;;;8DAErC,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAkC;;;;;;;;;;;;8DAEpD,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCAAO,WAAU;8CAAkF;;;;;;;;;;;;;;;;;;;;;;;;;AASlH;GAvHgB;KAAA", "debugId": null}}]}