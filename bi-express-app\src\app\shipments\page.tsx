import { Suspense } from 'react'
import { ShipmentsList } from '@/components/shipments/ShipmentsList'
import { ShipmentFilters } from '@/components/shipments/ShipmentFilters'
import { CreateShipmentButton } from '@/components/shipments/CreateShipmentButton'
import { ShipmentStats } from '@/components/shipments/ShipmentStats'

export default function ShipmentsPage() {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 pb-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Expéditions</h1>
          <p className="text-gray-600 mt-1">
            Suivi logistique Nigeria-Dakar avec tracking temps réel
          </p>
        </div>
        <CreateShipmentButton />
      </div>

      {/* Stats */}
      <Suspense fallback={<div className="animate-pulse h-24 bg-gray-200 rounded-lg" />}>
        <ShipmentStats />
      </Suspense>

      {/* Filters */}
      <Suspense fallback={<div className="animate-pulse h-16 bg-gray-200 rounded-lg" />}>
        <ShipmentFilters />
      </Suspense>

      {/* Shipments List */}
      <Suspense fallback={<div className="animate-pulse h-96 bg-gray-200 rounded-lg" />}>
        <ShipmentsList />
      </Suspense>
    </div>
  )
}
