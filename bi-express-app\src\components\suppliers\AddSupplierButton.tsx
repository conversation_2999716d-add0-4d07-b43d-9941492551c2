'use client'

import { useState } from 'react'
import { Plus } from 'lucide-react'

export function AddSupplierButton() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
      >
        <Plus className="h-4 w-4 mr-2" />
        Ajouter un fournisseur
      </button>

      {/* Modal placeholder - à implémenter plus tard */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-lg font-semibold mb-4">Ajouter un fournisseur</h2>
            <p className="text-gray-600 mb-4">
              Formulaire d'ajout de fournisseur à implémenter
            </p>
            <button
              onClick={() => setIsOpen(false)}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
            >
              Fermer
            </button>
          </div>
        </div>
      )}
    </>
  )
}
