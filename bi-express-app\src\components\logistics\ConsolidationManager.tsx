'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Truck, 
  Package, 
  Users, 
  TrendingUp, 
  Calendar,
  MapPin,
  Weight,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react'
import { optimizeConsolidation, type ConsolidationRequest, type ConsolidationGroup } from '@/lib/consolidation'

// Données de démonstration
const mockRequests: ConsolidationRequest[] = [
  {
    id: 'REQ-001',
    customerId: 'CUST-001',
    customerType: 'LOGISTICS',
    weight: 450,
    volume: 2.5,
    originCity: 'LAGOS',
    destinationCity: 'DAKAR',
    transportMode: 'ROAD',
    urgency: 'STANDARD',
    requestedPickupDate: new Date('2024-01-15'),
    declaredValue: 850000,
    description: 'Équipements électroniques'
  },
  {
    id: 'REQ-002',
    customerId: 'CUST-002',
    customerType: 'COMMERCE',
    weight: 320,
    volume: 1.8,
    originCity: 'LAGOS',
    destinationCity: 'DAKAR',
    transportMode: 'ROAD',
    urgency: 'STANDARD',
    requestedPickupDate: new Date('2024-01-16'),
    declaredValue: 650000,
    description: 'Produits cosmétiques'
  },
  {
    id: 'REQ-003',
    customerId: 'CUST-003',
    customerType: 'LOGISTICS',
    weight: 180,
    volume: 1.2,
    originCity: 'LAGOS',
    destinationCity: 'DAKAR',
    transportMode: 'ROAD',
    urgency: 'EXPRESS',
    requestedPickupDate: new Date('2024-01-17'),
    declaredValue: 420000,
    description: 'Pièces automobiles'
  },
  {
    id: 'REQ-004',
    customerId: 'CUST-004',
    customerType: 'LOGISTICS',
    weight: 75,
    volume: 0.8,
    originCity: 'ABUJA',
    destinationCity: 'DAKAR',
    transportMode: 'AIR_EXPRESS',
    urgency: 'URGENT',
    requestedPickupDate: new Date('2024-01-15'),
    declaredValue: 180000,
    description: 'Documents urgents'
  }
]

export function ConsolidationManager() {
  const [requests, setRequests] = useState<ConsolidationRequest[]>(mockRequests)
  const [optimizationResult, setOptimizationResult] = useState<any>(null)
  const [isOptimizing, setIsOptimizing] = useState(false)

  useEffect(() => {
    optimizeRequests()
  }, [requests])

  const optimizeRequests = async () => {
    setIsOptimizing(true)
    try {
      const result = optimizeConsolidation(requests, 3)
      setOptimizationResult(result)
    } catch (error) {
      console.error('Erreur optimisation:', error)
    } finally {
      setIsOptimizing(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const getUrgencyBadge = (urgency: string) => {
    const config = {
      STANDARD: { label: 'Standard', color: 'bg-green-100 text-green-800' },
      EXPRESS: { label: 'Express', color: 'bg-orange-100 text-orange-800' },
      URGENT: { label: 'Urgent', color: 'bg-red-100 text-red-800' }
    }
    const urgencyConfig = config[urgency as keyof typeof config] || config.STANDARD
    return <Badge className={urgencyConfig.color}>{urgencyConfig.label}</Badge>
  }

  const getCustomerTypeBadge = (type: string) => {
    const config = {
      LOGISTICS: { label: 'Logistique', color: 'bg-blue-100 text-blue-800' },
      COMMERCE: { label: 'Commerce', color: 'bg-purple-100 text-purple-800' }
    }
    const typeConfig = config[type as keyof typeof config] || config.LOGISTICS
    return <Badge className={typeConfig.color}>{typeConfig.label}</Badge>
  }

  return (
    <div className="p-6 space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Gestion des Consolidations
          </h1>
          <p className="text-gray-600">
            Optimisation intelligente des expéditions multi-clients
          </p>
        </div>
        <Button onClick={optimizeRequests} disabled={isOptimizing}>
          {isOptimizing ? 'Optimisation...' : 'Réoptimiser'}
        </Button>
      </div>

      {/* Métriques de performance */}
      {optimizationResult && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Économies totales</p>
                  <p className="text-2xl font-bold text-green-600">
                    {formatCurrency(optimizationResult.totalSavings)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Truck className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Groupes créés</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {optimizationResult.groups.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Score d'optimisation</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {(optimizationResult.optimizationScore * 100).toFixed(0)}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Users className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Clients impactés</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {requests.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Contenu principal */}
      <Tabs defaultValue="groups" className="space-y-6">
        <TabsList>
          <TabsTrigger value="groups">Groupes optimisés</TabsTrigger>
          <TabsTrigger value="requests">Demandes en attente</TabsTrigger>
          <TabsTrigger value="recommendations">Recommandations</TabsTrigger>
        </TabsList>

        <TabsContent value="groups">
          {optimizationResult?.groups.length > 0 ? (
            <div className="space-y-4">
              {optimizationResult.groups.map((group: ConsolidationGroup) => (
                <Card key={group.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="flex items-center">
                          <Truck className="h-5 w-5 mr-2 text-blue-600" />
                          Groupe {group.id}
                        </CardTitle>
                        <CardDescription>
                          {group.route.replace('_', ' → ')} • {group.transportMode === 'ROAD' ? 'Routier' : 'Aérien'}
                        </CardDescription>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-gray-900">
                          {formatCurrency(group.estimatedCost)}
                        </div>
                        <div className="text-sm text-gray-500">
                          Départ: {group.departureDate.toLocaleDateString('fr-FR')}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {/* Métriques du groupe */}
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {group.requests.length}
                        </div>
                        <div className="text-sm text-gray-500">Clients</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {group.currentWeight.toLocaleString()} kg
                        </div>
                        <div className="text-sm text-gray-500">
                          / {group.maxWeight.toLocaleString()} kg
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">
                          {(group.loadingRate * 100).toFixed(0)}%
                        </div>
                        <div className="text-sm text-gray-500">Taux de charge</div>
                      </div>
                    </div>

                    {/* Barre de progression */}
                    <div className="mb-6">
                      <div className="flex justify-between text-sm text-gray-600 mb-2">
                        <span>Capacité utilisée</span>
                        <span>{(group.loadingRate * 100).toFixed(1)}%</span>
                      </div>
                      <Progress value={group.loadingRate * 100} className="h-2" />
                    </div>

                    {/* Liste des demandes */}
                    <div className="space-y-3">
                      <h4 className="font-medium text-gray-900">Demandes consolidées</h4>
                      {group.requests.map((request) => {
                        const clientCost = group.costPerClient.find(c => c.requestId === request.id)
                        return (
                          <div key={request.id} className="border border-gray-200 rounded-lg p-3">
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-1">
                                  <span className="font-medium text-sm">{request.id}</span>
                                  {getCustomerTypeBadge(request.customerType)}
                                  {getUrgencyBadge(request.urgency)}
                                </div>
                                <p className="text-sm text-gray-600 mb-1">{request.description}</p>
                                <div className="flex items-center space-x-4 text-xs text-gray-500">
                                  <span className="flex items-center">
                                    <Weight className="h-3 w-3 mr-1" />
                                    {request.weight} kg
                                  </span>
                                  <span className="flex items-center">
                                    <Package className="h-3 w-3 mr-1" />
                                    {request.volume} m³
                                  </span>
                                  <span className="flex items-center">
                                    <Calendar className="h-3 w-3 mr-1" />
                                    {request.requestedPickupDate.toLocaleDateString('fr-FR')}
                                  </span>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="font-bold text-sm">
                                  {formatCurrency(clientCost?.allocatedCost || 0)}
                                </div>
                                {clientCost?.savings && clientCost.savings > 0 && (
                                  <div className="text-xs text-green-600">
                                    Économie: {formatCurrency(clientCost.savings)}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <Truck className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                <p className="text-gray-500">Aucun groupe de consolidation disponible</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="requests">
          <Card>
            <CardHeader>
              <CardTitle>Demandes en attente de consolidation</CardTitle>
              <CardDescription>
                {requests.length} demande{requests.length > 1 ? 's' : ''} à traiter
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {requests.map((request) => (
                  <div key={request.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="font-medium">{request.id}</span>
                          {getCustomerTypeBadge(request.customerType)}
                          {getUrgencyBadge(request.urgency)}
                        </div>
                        <p className="text-gray-600 mb-2">{request.description}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span className="flex items-center">
                            <MapPin className="h-4 w-4 mr-1" />
                            {request.originCity} → {request.destinationCity}
                          </span>
                          <span className="flex items-center">
                            <Weight className="h-4 w-4 mr-1" />
                            {request.weight} kg
                          </span>
                          <span className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {request.requestedPickupDate.toLocaleDateString('fr-FR')}
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-lg">
                          {formatCurrency(request.declaredValue || 0)}
                        </div>
                        <div className="text-sm text-gray-500">Valeur déclarée</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertCircle className="h-5 w-5 mr-2 text-orange-600" />
                Recommandations d'optimisation
              </CardTitle>
            </CardHeader>
            <CardContent>
              {optimizationResult?.recommendations.length > 0 ? (
                <div className="space-y-3">
                  {optimizationResult.recommendations.map((recommendation: string, index: number) => (
                    <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                      <p className="text-blue-800">{recommendation}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                  <p className="text-gray-500">Aucune recommandation disponible</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
