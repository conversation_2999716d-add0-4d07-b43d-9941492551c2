import * as React from "react"
import { cn } from "@/lib/utils"
import { Label } from "./label"
import { Input } from "./input"
import { Select } from "./select"
import { Textarea } from "./textarea"

interface BaseFormFieldProps {
  label: string
  required?: boolean
  error?: string
  helperText?: string
  className?: string
}

interface InputFormFieldProps extends BaseFormFieldProps {
  type: "input"
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>
}

interface SelectFormFieldProps extends BaseFormFieldProps {
  type: "select"
  selectProps?: React.SelectHTMLAttributes<HTMLSelectElement>
  options?: Array<{ value: string; label: string }>
  placeholder?: string
  children?: React.ReactNode
}

interface TextareaFormFieldProps extends BaseFormFieldProps {
  type: "textarea"
  textareaProps?: React.TextareaHTMLAttributes<HTMLTextAreaElement>
}

type FormFieldProps = InputFormFieldProps | SelectFormFieldProps | TextareaFormFieldProps

export function FormField(props: FormFieldProps) {
  const { label, required, error, helperText, className } = props
  const fieldId = React.useId()
  const hasError = !!error

  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor={fieldId} required={required}>
        {label}
      </Label>
      
      {props.type === "input" && (
        <Input
          id={fieldId}
          error={hasError}
          helperText={error || helperText}
          {...props.inputProps}
        />
      )}
      
      {props.type === "select" && (
        <Select
          id={fieldId}
          error={hasError}
          helperText={error || helperText}
          placeholder={props.placeholder}
          title={label}
          {...props.selectProps}
        >
          {props.options?.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
          {props.children}
        </Select>
      )}
      
      {props.type === "textarea" && (
        <Textarea
          id={fieldId}
          error={hasError}
          helperText={error || helperText}
          {...props.textareaProps}
        />
      )}
    </div>
  )
}
