'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge-component'
import { Plus, Minus, Search, ShoppingCart } from 'lucide-react'

interface Customer {
  id: string
  name: string
  email: string
  phone: string
  type: 'LOGISTICS' | 'COMMERCE'
  city: string
}

interface Product {
  id: string
  name: string
  category: string
  supplierPrice: number
  sellingPrice: number
  supplier: {
    name: string
    city: string
  }
}

interface OrderItem {
  productId: string
  product: Product
  quantity: number
  unitPrice: number
  totalPrice: number
}

export function NewOrderForm() {
  const router = useRouter()
  const [customers, setCustomers] = useState<Customer[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [orderItems, setOrderItems] = useState<OrderItem[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  // Charger les clients et produits
  useEffect(() => {
    const loadData = async () => {
      try {
        const [customersRes, productsRes] = await Promise.all([
          fetch('/api/customers'),
          fetch('/api/products')
        ])
        
        if (customersRes.ok) {
          const customersData = await customersRes.json()
          setCustomers(customersData)
        }
        
        if (productsRes.ok) {
          const productsData = await productsRes.json()
          setProducts(productsData)
        }
      } catch (error) {
        console.error('Erreur lors du chargement des données:', error)
      }
    }

    loadData()
  }, [])

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.category.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const addProductToOrder = (product: Product) => {
    const existingItem = orderItems.find(item => item.productId === product.id)
    
    if (existingItem) {
      setOrderItems(orderItems.map(item =>
        item.productId === product.id
          ? { ...item, quantity: item.quantity + 1, totalPrice: (item.quantity + 1) * item.unitPrice }
          : item
      ))
    } else {
      const newItem: OrderItem = {
        productId: product.id,
        product,
        quantity: 1,
        unitPrice: product.sellingPrice,
        totalPrice: product.sellingPrice
      }
      setOrderItems([...orderItems, newItem])
    }
  }

  const updateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      setOrderItems(orderItems.filter(item => item.productId !== productId))
    } else {
      setOrderItems(orderItems.map(item =>
        item.productId === productId
          ? { ...item, quantity: newQuantity, totalPrice: newQuantity * item.unitPrice }
          : item
      ))
    }
  }

  const calculateTotal = () => {
    return orderItems.reduce((sum, item) => sum + item.totalPrice, 0)
  }

  const handleSubmit = async () => {
    if (!selectedCustomer || orderItems.length === 0) {
      alert('Veuillez sélectionner un client et ajouter des produits')
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          customerId: selectedCustomer.id,
          items: orderItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: item.unitPrice
          }))
        })
      })

      if (response.ok) {
        router.push('/orders')
      } else {
        alert('Erreur lors de la création de la commande')
      }
    } catch (error) {
      console.error('Erreur:', error)
      alert('Erreur lors de la création de la commande')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Sélection du client */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Client
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {customers.map(customer => (
              <div
                key={customer.id}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedCustomer?.id === customer.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedCustomer(customer)}
              >
                <div className="font-medium">{customer.name}</div>
                <div className="text-sm text-gray-600">{customer.city}</div>
                <Badge variant={customer.type === 'COMMERCE' ? 'default' : 'secondary'}>
                  {customer.type}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Sélection des produits */}
      <Card>
        <CardHeader>
          <CardTitle>Produits</CardTitle>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher un produit..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {filteredProducts.map(product => (
              <div
                key={product.id}
                className="p-3 border border-gray-200 rounded-lg hover:border-gray-300 cursor-pointer"
                onClick={() => addProductToOrder(product)}
              >
                <div className="font-medium">{product.name}</div>
                <div className="text-sm text-gray-600">{product.category}</div>
                <div className="text-sm text-gray-600">{product.supplier.name}</div>
                <div className="font-semibold text-blue-600">
                  {product.sellingPrice.toLocaleString()} FCFA
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Panier */}
      <Card>
        <CardHeader>
          <CardTitle>Panier ({orderItems.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 mb-4">
            {orderItems.map(item => (
              <div key={item.productId} className="p-3 border border-gray-200 rounded-lg">
                <div className="font-medium">{item.product.name}</div>
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => updateQuantity(item.productId, item.quantity - 1)}
                    >
                      <Minus className="h-3 w-3" />
                    </Button>
                    <span className="w-8 text-center">{item.quantity}</span>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => updateQuantity(item.productId, item.quantity + 1)}
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                  <div className="font-semibold">
                    {item.totalPrice.toLocaleString()} FCFA
                  </div>
                </div>
              </div>
            ))}
          </div>

          {orderItems.length > 0 && (
            <div className="border-t pt-4">
              <div className="flex justify-between items-center mb-4">
                <span className="font-semibold">Total:</span>
                <span className="font-bold text-lg">
                  {calculateTotal().toLocaleString()} FCFA
                </span>
              </div>
              
              <Button
                onClick={handleSubmit}
                disabled={!selectedCustomer || orderItems.length === 0 || isLoading}
                className="w-full"
              >
                {isLoading ? 'Création...' : 'Créer la commande'}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
