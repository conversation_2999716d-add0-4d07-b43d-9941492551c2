'use client'

import { useState } from 'react'
import { Plus, Truck, Package } from 'lucide-react'

export function CreateOrderButton() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
      >
        <Plus className="h-4 w-4 mr-2" />
        Nouvelle commande
      </button>

      {/* Modal placeholder - à implémenter plus tard */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-lg font-semibold mb-6">Créer une nouvelle commande</h2>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Informations client */}
              <div className="space-y-4">
                <h3 className="text-md font-medium text-gray-900 border-b pb-2">
                  Informations client
                </h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Client
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="">Sélectionner un client</option>
                    <option value="1">Aminata Diallo - Dakar</option>
                    <option value="2">Moussa Sow - Dakar</option>
                    <option value="3">Fatou Ndiaye - Dakar</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Adresse de livraison
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={3}
                    placeholder="Adresse complète à Dakar..."
                  />
                </div>
              </div>

              {/* Mode de transport */}
              <div className="space-y-4">
                <h3 className="text-md font-medium text-gray-900 border-b pb-2">
                  Mode de transport
                </h3>
                
                <div className="grid grid-cols-1 gap-3">
                  <div className="border border-gray-300 rounded-lg p-4 hover:border-blue-500 cursor-pointer">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Truck className="h-6 w-6 text-blue-600 mr-3" />
                        <div>
                          <p className="font-medium text-gray-900">Transport routier</p>
                          <p className="text-sm text-gray-600">5-7 jours • Économique</p>
                        </div>
                      </div>
                      <input type="radio" name="transport" value="ROAD" className="text-blue-600" />
                    </div>
                  </div>
                  
                  <div className="border border-gray-300 rounded-lg p-4 hover:border-purple-500 cursor-pointer">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="h-6 w-6 bg-purple-600 rounded flex items-center justify-center mr-3">
                          <span className="text-white text-sm font-bold">✈</span>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">Transport aérien</p>
                          <p className="text-sm text-gray-600">24-48h • Express (+30%)</p>
                        </div>
                      </div>
                      <input type="radio" name="transport" value="AIR" className="text-purple-600" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Sélection des produits */}
            <div className="mt-6">
              <h3 className="text-md font-medium text-gray-900 border-b pb-2 mb-4">
                Produits à commander
              </h3>
              
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <Package className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">Sélectionnez les produits à ajouter</p>
                    <button className="mt-2 text-blue-600 hover:text-blue-800 font-medium">
                      + Ajouter des produits
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Résumé des coûts */}
            <div className="mt-6 bg-blue-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-blue-900 mb-3">Résumé des coûts</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-blue-700">Sous-total produits:</span>
                  <span className="font-medium ml-2">0 CFA</span>
                </div>
                <div>
                  <span className="text-blue-700">Frais logistiques:</span>
                  <span className="font-medium ml-2">0 CFA</span>
                </div>
                <div>
                  <span className="text-blue-700">Total commande:</span>
                  <span className="font-medium ml-2 text-lg">0 CFA</span>
                </div>
                <div>
                  <span className="text-blue-700">Bénéfice estimé:</span>
                  <span className="font-medium ml-2 text-green-600">0 CFA</span>
                </div>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setIsOpen(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                Annuler
              </button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                Créer la commande
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
