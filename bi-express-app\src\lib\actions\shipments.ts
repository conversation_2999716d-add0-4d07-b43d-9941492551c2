'use server'

import { prisma } from '@/lib/prisma'
import { revalidatePath } from 'next/cache'
import { 
  generateTrackingNumber, 
  calculateTransportCosts, 
  calculateDeliveryTime,
  validateShipmentData 
} from '@/lib/logistics'
import { TransportMode, SupplierCity, ShipmentStatus, TrackingEventType } from '@/types'

/**
 * Crée une nouvelle expédition pour une commande
 */
export async function createShipment(data: {
  orderId: string
  carrierId: string
  weight: number
  transportMode: TransportMode
  originCity: SupplierCity
  fuelSurcharge?: number
  notes?: string
}) {
  try {
    // Validation des données
    const errors = validateShipmentData(data)
    if (errors.length > 0) {
      return { success: false, error: errors.join(', ') }
    }

    // Vérifier que la commande existe et n'a pas déjà d'expédition
    const existingOrder = await prisma.order.findUnique({
      where: { id: data.orderId },
      include: { shipment: true }
    })

    if (!existingOrder) {
      return { success: false, error: 'Commande introuvable' }
    }

    if (existingOrder.shipment) {
      return { success: false, error: 'Cette commande a déjà une expédition' }
    }

    // Générer le numéro de suivi
    const trackingNumber = generateTrackingNumber(data.transportMode, data.originCity)

    // Calculer les coûts et délais
    const transportCost = calculateTransportCosts(
      data.weight,
      data.transportMode,
      data.originCity,
      data.fuelSurcharge || 0
    )

    const estimatedDelivery = calculateDeliveryTime(
      data.transportMode,
      data.originCity,
      existingOrder.orderDate
    )

    // Créer l'expédition
    const shipment = await prisma.shipment.create({
      data: {
        trackingNumber,
        orderId: data.orderId,
        carrierId: data.carrierId,
        status: 'PENDING',
        transportMode: data.transportMode,
        originCity: data.originCity,
        destinationCity: 'DAKAR',
        weight: data.weight,
        transportCost,
        fuelSurcharge: data.fuelSurcharge || 0,
        estimatedDelivery,
        notes: data.notes
      },
      include: {
        carrier: true,
        order: true
      }
    })

    // Créer l'événement de suivi initial
    await prisma.trackingEvent.create({
      data: {
        shipmentId: shipment.id,
        eventType: 'PICKUP_SCHEDULED',
        location: data.originCity,
        description: `Enlèvement programmé chez ${shipment.carrier.name}`,
        timestamp: new Date()
      }
    })

    // Mettre à jour le statut de la commande
    await prisma.order.update({
      where: { id: data.orderId },
      data: { status: 'SHIPPED' }
    })

    revalidatePath('/logistics')
    revalidatePath('/orders')
    
    return { success: true, shipment }
  } catch (error) {
    console.error('Erreur lors de la création de l\'expédition:', error)
    return { success: false, error: 'Erreur lors de la création de l\'expédition' }
  }
}

/**
 * Met à jour le statut d'une expédition
 */
export async function updateShipmentStatus(
  shipmentId: string,
  status: ShipmentStatus,
  location?: string,
  notes?: string
) {
  try {
    const shipment = await prisma.shipment.findUnique({
      where: { id: shipmentId },
      include: {
        orders: true,
        shipmentRequests: true
      }
    })

    if (!shipment) {
      return { success: false, error: 'Expédition introuvable' }
    }

    // Mettre à jour l'expédition
    const updatedShipment = await prisma.shipment.update({
      where: { id: shipmentId },
      data: {
        status,
        currentLocation: location,
        ...(status === 'PICKED_UP' && { pickupDate: new Date() }),
        ...(status === 'DELIVERED' && { deliveryDate: new Date() }),
        ...(notes && { notes })
      }
    })

    // Créer un événement de suivi
    const eventTypeMap: Record<ShipmentStatus, TrackingEventType> = {
      PENDING: 'PICKUP_SCHEDULED',
      PICKED_UP: 'PICKED_UP',
      IN_TRANSIT: 'IN_TRANSIT',
      CUSTOMS: 'CUSTOMS_CLEARANCE',
      OUT_FOR_DELIVERY: 'OUT_FOR_DELIVERY',
      DELIVERED: 'DELIVERED',
      DELAYED: 'DELAYED',
      CANCELLED: 'DELIVERY_FAILED'
    }

    await prisma.trackingEvent.create({
      data: {
        shipmentId,
        eventType: eventTypeMap[status],
        location: location || shipment.currentLocation || shipment.originCity,
        description: notes || `Statut mis à jour: ${status}`,
        timestamp: new Date()
      }
    })

    // Mettre à jour le statut de la commande si nécessaire
    if (status === 'DELIVERED') {
      await prisma.order.update({
        where: { id: shipment.orderId },
        data: { 
          status: 'DELIVERED',
          deliveredAt: new Date()
        }
      })
    }

    revalidatePath('/logistics')
    revalidatePath('/orders')
    
    return { success: true, shipment: updatedShipment }
  } catch (error) {
    console.error('Erreur lors de la mise à jour du statut:', error)
    return { success: false, error: 'Erreur lors de la mise à jour du statut' }
  }
}

/**
 * Récupère toutes les expéditions avec filtres
 */
export async function getShipments(filters?: {
  status?: ShipmentStatus
  carrierId?: string
  transportMode?: TransportMode
  dateFrom?: Date
  dateTo?: Date
}) {
  try {
    const where: any = {}

    if (filters?.status) where.status = filters.status
    if (filters?.carrierId) where.carrierId = filters.carrierId
    if (filters?.transportMode) where.transportMode = filters.transportMode
    if (filters?.dateFrom || filters?.dateTo) {
      where.createdAt = {}
      if (filters.dateFrom) where.createdAt.gte = filters.dateFrom
      if (filters.dateTo) where.createdAt.lte = filters.dateTo
    }

    const shipments = await prisma.shipment.findMany({
      where,
      include: {
        carrier: true,
        orders: {
          include: {
            customer: true,
            supplier: true
          }
        },
        shipmentRequests: {
          include: {
            customer: true
          }
        },
        trackingEvents: {
          orderBy: { timestamp: 'desc' }
        },
        shipmentTracking: {
          orderBy: { timestamp: 'desc' }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return shipments
  } catch (error) {
    console.error('Erreur lors de la récupération des expéditions:', error)
    return []
  }
}

/**
 * Récupère une expédition par son numéro de suivi
 */
export async function getShipmentByTracking(trackingNumber: string) {
  try {
    const shipment = await prisma.shipment.findUnique({
      where: { trackingNumber },
      include: {
        carrier: true,
        order: {
          include: {
            customer: true,
            supplier: true,
            orderItems: {
              include: {
                product: true
              }
            }
          }
        },
        trackingEvents: {
          orderBy: { timestamp: 'asc' }
        }
      }
    })

    return shipment
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'expédition:', error)
    return null
  }
}

/**
 * Ajoute un événement de suivi manuel
 */
export async function addTrackingEvent(
  shipmentId: string,
  eventType: TrackingEventType,
  location: string,
  description: string
) {
  try {
    const event = await prisma.trackingEvent.create({
      data: {
        shipmentId,
        eventType,
        location,
        description,
        timestamp: new Date()
      }
    })

    revalidatePath('/logistics')
    
    return { success: true, event }
  } catch (error) {
    console.error('Erreur lors de l\'ajout de l\'événement:', error)
    return { success: false, error: 'Erreur lors de l\'ajout de l\'événement' }
  }
}
