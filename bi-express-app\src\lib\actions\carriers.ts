'use server'

import { prisma } from '@/lib/prisma'
import { revalidatePath } from 'next/cache'
import { SupplierCity } from '@/types'

/**
 * Récupère tous les transporteurs
 */
export async function getCarriers() {
  try {
    const carriers = await prisma.carrier.findMany({
      where: { isActive: true },
      include: {
        shipments: {
          select: {
            id: true,
            status: true,
            deliveryDate: true,
            estimatedDelivery: true,
            totalTransportCost: true,
            pickupDate: true,
            arrivalDate: true
          }
        },
        evaluations: {
          select: {
            overall: true,
            punctuality: true,
            condition: true,
            communication: true
          }
        }
      },
      orderBy: { name: 'asc' }
    })

    // Calculer les statistiques pour chaque transporteur
    const carriersWithStats = carriers.map(carrier => {
      const totalShipments = carrier.shipments.length
      const deliveredShipments = carrier.shipments.filter(s => s.status === 'DELIVERED')
      
      // Calcul du taux de ponctualité
      const onTimeDeliveries = deliveredShipments.filter(s => {
        if (!s.deliveryDate || !s.estimatedDelivery) return false
        return new Date(s.deliveryDate) <= new Date(s.estimatedDelivery)
      }).length
      
      const onTimeRate = deliveredShipments.length > 0 
        ? (onTimeDeliveries / deliveredShipments.length) * 100 
        : 0

      // Calcul de la note moyenne
      const averageRating = carrier.evaluations.length > 0
        ? carrier.evaluations.reduce((sum, evaluation) => sum + evaluation.overall, 0) / carrier.evaluations.length
        : 0

      // Coût total
      const totalCost = carrier.shipments.reduce((sum, s) => sum + (s.totalTransportCost || 0), 0)

      return {
        ...carrier,
        stats: {
          totalShipments,
          deliveredShipments: deliveredShipments.length,
          onTimeRate: Math.round(onTimeRate),
          averageRating: Math.round(averageRating * 10) / 10,
          totalCost
        }
      }
    })

    return carriersWithStats
  } catch (error) {
    console.error('Erreur lors de la récupération des transporteurs:', error)
    return []
  }
}

/**
 * Crée un nouveau transporteur
 */
export async function createCarrier(data: {
  name: string
  phone: string
  email?: string
  address: string
  city: SupplierCity
  transportModes: string[]
  capacity?: number
}) {
  try {
    const carrier = await prisma.carrier.create({
      data: {
        name: data.name,
        phone: data.phone,
        email: data.email,
        address: data.address,
        city: data.city,
        transportModes: data.transportModes.join(','),
        capacity: data.capacity,
        rating: 0,
        onTimeRate: 0,
        isActive: true
      }
    })

    revalidatePath('/logistics')
    
    return { success: true, carrier }
  } catch (error) {
    console.error('Erreur lors de la création du transporteur:', error)
    return { success: false, error: 'Erreur lors de la création du transporteur' }
  }
}

/**
 * Met à jour un transporteur
 */
export async function updateCarrier(
  carrierId: string,
  data: {
    name?: string
    phone?: string
    email?: string
    address?: string
    city?: SupplierCity
    transportModes?: string[]
    capacity?: number
    isActive?: boolean
  }
) {
  try {
    const updateData: any = { ...data }
    
    if (data.transportModes) {
      updateData.transportModes = data.transportModes.join(',')
    }

    const carrier = await prisma.carrier.update({
      where: { id: carrierId },
      data: updateData
    })

    revalidatePath('/logistics')
    
    return { success: true, carrier }
  } catch (error) {
    console.error('Erreur lors de la mise à jour du transporteur:', error)
    return { success: false, error: 'Erreur lors de la mise à jour du transporteur' }
  }
}

/**
 * Évalue un transporteur
 */
export async function evaluateCarrier(data: {
  carrierId: string
  shipmentId?: string
  punctuality: number
  condition: number
  communication: number
  overall: number
  comment?: string
}) {
  try {
    // Créer l'évaluation
    const evaluation = await prisma.carrierEvaluation.create({
      data: {
        carrierId: data.carrierId,
        shipmentId: data.shipmentId,
        punctuality: data.punctuality,
        condition: data.condition,
        communication: data.communication,
        overall: data.overall,
        comment: data.comment
      }
    })

    // Recalculer la note moyenne du transporteur
    const allEvaluations = await prisma.carrierEvaluation.findMany({
      where: { carrierId: data.carrierId },
      select: { overall: true }
    })

    const averageRating = allEvaluations.reduce((sum, evaluation) => sum + evaluation.overall, 0) / allEvaluations.length

    // Mettre à jour la note du transporteur
    await prisma.carrier.update({
      where: { id: data.carrierId },
      data: { rating: averageRating }
    })

    revalidatePath('/logistics')
    
    return { success: true, evaluation }
  } catch (error) {
    console.error('Erreur lors de l\'évaluation du transporteur:', error)
    return { success: false, error: 'Erreur lors de l\'évaluation du transporteur' }
  }
}

/**
 * Récupère les transporteurs disponibles pour un mode de transport
 */
export async function getAvailableCarriers(transportMode: string, city?: SupplierCity) {
  try {
    const where: any = {
      isActive: true,
      transportModes: {
        contains: transportMode
      }
    }

    if (city) {
      where.city = city
    }

    const carriers = await prisma.carrier.findMany({
      where,
      select: {
        id: true,
        name: true,
        city: true,
        rating: true,
        onTimeRate: true,
        capacity: true
      },
      orderBy: [
        { rating: 'desc' },
        { onTimeRate: 'desc' }
      ]
    })

    return carriers
  } catch (error) {
    console.error('Erreur lors de la récupération des transporteurs disponibles:', error)
    return []
  }
}

/**
 * Récupère les statistiques d'un transporteur
 */
export async function getCarrierStats(carrierId: string) {
  try {
    const carrier = await prisma.carrier.findUnique({
      where: { id: carrierId },
      include: {
        shipments: {
          include: {
            order: true
          }
        },
        evaluations: true
      }
    })

    if (!carrier) {
      return null
    }

    const totalShipments = carrier.shipments.length
    const deliveredShipments = carrier.shipments.filter(s => s.status === 'DELIVERED')
    const inTransitShipments = carrier.shipments.filter(s => 
      ['PICKED_UP', 'IN_TRANSIT', 'CUSTOMS', 'OUT_FOR_DELIVERY'].includes(s.status)
    )

    // Calcul du taux de ponctualité
    const onTimeDeliveries = deliveredShipments.filter(s => {
      if (!s.deliveryDate || !s.estimatedDelivery) return false
      return new Date(s.deliveryDate) <= new Date(s.estimatedDelivery)
    }).length

    const onTimeRate = deliveredShipments.length > 0 
      ? (onTimeDeliveries / deliveredShipments.length) * 100 
      : 0

    // Revenus générés
    const totalRevenue = carrier.shipments.reduce((sum, s) => sum + (s.totalTransportCost || 0), 0)

    // Temps moyen de livraison
    const deliveryTimes = deliveredShipments
      .filter(s => s.pickupDate && s.deliveryDate)
      .map(s => {
        const pickup = new Date(s.pickupDate!).getTime()
        const delivery = new Date(s.deliveryDate!).getTime()
        return (delivery - pickup) / (1000 * 60 * 60) // en heures
      })

    const averageDeliveryTime = deliveryTimes.length > 0
      ? deliveryTimes.reduce((sum, time) => sum + time, 0) / deliveryTimes.length
      : 0

    // Évaluations moyennes
    const evaluationStats = carrier.evaluations.length > 0 ? {
      punctuality: carrier.evaluations.reduce((sum, e) => sum + e.punctuality, 0) / carrier.evaluations.length,
      condition: carrier.evaluations.reduce((sum, e) => sum + e.condition, 0) / carrier.evaluations.length,
      communication: carrier.evaluations.reduce((sum, e) => sum + e.communication, 0) / carrier.evaluations.length,
      overall: carrier.evaluations.reduce((sum, e) => sum + e.overall, 0) / carrier.evaluations.length
    } : {
      punctuality: 0,
      condition: 0,
      communication: 0,
      overall: 0
    }

    return {
      carrier,
      stats: {
        totalShipments,
        deliveredShipments: deliveredShipments.length,
        inTransitShipments: inTransitShipments.length,
        onTimeRate: Math.round(onTimeRate),
        totalRevenue,
        averageDeliveryTime: Math.round(averageDeliveryTime),
        evaluations: evaluationStats
      }
    }
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques du transporteur:', error)
    return null
  }
}
