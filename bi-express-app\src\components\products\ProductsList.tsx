'use client'

import { useProducts } from '@/hooks/useApi'
import { formatCurrency } from '@/lib/utils'
import { calculatePricing } from '@/lib/pricing'
import { Badge } from '@/components/ui/badge-component'
import { Package, AlertTriangle, TrendingUp, Edit, Eye, Loader2, AlertCircle } from 'lucide-react'

const categoryColors = {
  TISSUS: 'bg-blue-100 text-blue-800',
  COSMETIQUES: 'bg-pink-100 text-pink-800',
  MECHES: 'bg-purple-100 text-purple-800'
}

const categoryLabels = {
  TISSUS: 'Tissus',
  COSMETIQUES: 'Cosmétiques',
  MECHES: 'Mèches'
}

const cityColors = {
  LAGOS: 'bg-blue-50 text-blue-700',
  ABUJA: 'bg-green-50 text-green-700',
  KANO: 'bg-purple-50 text-purple-700'
}

export function ProductsList() {
  const { data: products, loading, error, refetch } = useProducts()

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Chargement des produits...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-center py-12">
          <AlertCircle className="h-8 w-8 text-red-500" />
          <div className="ml-3">
            <p className="text-red-600 font-medium">Erreur de chargement</p>
            <p className="text-red-500 text-sm">{error}</p>
            <button
              type="button"
              onClick={refetch}
              className="mt-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Réessayer
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (!products || products.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center py-12">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 text-lg">Aucun produit trouvé</p>
          <p className="text-gray-400 text-sm mt-1">
            Commencez par ajouter votre premier produit
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Produit
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Fournisseur
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Prix & Marges
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Stock
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Bénéfice
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {products.map((product) => {
              const pricing = calculatePricing(
                product.supplierPrice,
                product.logisticRate,
                product.margin
              )
              const isLowStock = product.stockQuantity <= product.minStockAlert

              return (
                <tr key={product.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                          <Package className="h-5 w-5 text-white" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {product.name}
                        </div>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge className={categoryColors[product.category]}>
                            {categoryLabels[product.category]}
                          </Badge>
                          {/* Spécificités selon la catégorie */}
                          {product.category === 'TISSUS' && product.fabricType && (
                            <span className="text-xs text-gray-500">
                              {product.fabricType} - {product.width}m
                            </span>
                          )}
                          {product.category === 'COSMETIQUES' && product.brand && (
                            <span className="text-xs text-gray-500">
                              {product.brand} - {product.volume}ml
                            </span>
                          )}
                          {product.category === 'MECHES' && product.length && (
                            <span className="text-xs text-gray-500">
                              {product.length}" - {product.hairType}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {product.supplier.name}
                    </div>
                    <Badge className={cityColors[product.supplier.city]}>
                      {product.supplier.city}
                    </Badge>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="font-medium">
                        {formatCurrency(pricing.sellingPrice)}
                      </div>
                      <div className="text-xs text-gray-500">
                        Fournisseur: {formatCurrency(pricing.supplierPrice)}
                      </div>
                      <div className="text-xs text-gray-500">
                        Logistique: {formatCurrency(pricing.logisticCosts)}
                      </div>
                      <div className="text-xs text-green-600">
                        Marge: {pricing.marginPercentage.toFixed(1)}%
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="text-sm font-medium text-gray-900">
                        {product.stockQuantity}
                      </div>
                      {isLowStock && (
                        <AlertTriangle className="h-4 w-4 text-red-500 ml-2" />
                      )}
                    </div>
                    <div className="text-xs text-gray-500">
                      Min: {product.minStockAlert}
                    </div>
                    {isLowStock && (
                      <div className="text-xs text-red-600 font-medium">
                        Stock faible !
                      </div>
                    )}
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-green-600">
                      {formatCurrency(pricing.profit)}
                    </div>
                    <div className="text-xs text-gray-500">
                      par unité
                    </div>
                    <div className="text-xs text-green-600">
                      Total: {formatCurrency(pricing.profit * product.stockQuantity)}
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        type="button"
                        title="Voir les détails"
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        type="button"
                        title="Modifier"
                        className="text-gray-600 hover:text-gray-900"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>
      
      {products.length === 0 && (
        <div className="text-center py-12">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 text-lg">Aucun produit trouvé</p>
          <p className="text-gray-400 text-sm mt-1">
            Commencez par ajouter votre premier produit
          </p>
        </div>
      )}
    </div>
  )
}
