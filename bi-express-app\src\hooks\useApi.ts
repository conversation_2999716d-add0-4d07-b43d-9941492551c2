'use client'

import { useState, useEffect } from 'react'

interface ApiState<T> {
  data: T | null
  loading: boolean
  error: string | null
}

interface ApiOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  body?: any
  headers?: Record<string, string>
}

export function useApi<T>(url: string, options?: ApiOptions): ApiState<T> & { refetch: () => void } {
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: true,
    error: null
  })

  const fetchData = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))
      
      const fetchOptions: RequestInit = {
        method: options?.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers
        }
      }

      if (options?.body && options.method !== 'GET') {
        fetchOptions.body = JSON.stringify(options.body)
      }

      const response = await fetch(url, fetchOptions)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      setState({ data, loading: false, error: null })
    } catch (error) {
      setState({
        data: null,
        loading: false,
        error: error instanceof Error ? error.message : 'Une erreur est survenue'
      })
    }
  }

  useEffect(() => {
    fetchData()
  }, [url, JSON.stringify(options)])

  return {
    ...state,
    refetch: fetchData
  }
}

export async function apiCall<T>(url: string, options?: ApiOptions): Promise<T> {
  const fetchOptions: RequestInit = {
    method: options?.method || 'GET',
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers
    }
  }

  if (options?.body && options.method !== 'GET') {
    fetchOptions.body = JSON.stringify(options.body)
  }

  const response = await fetch(url, fetchOptions)
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}))
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`)
  }
  
  return response.json()
}

// Hooks spécialisés pour chaque entité
export function useSuppliers() {
  return useApi<any[]>('/api/suppliers')
}

export function useCustomers() {
  return useApi<any[]>('/api/customers')
}

export function useProducts() {
  return useApi<any[]>('/api/products')
}

export function useOrders() {
  return useApi<any[]>('/api/orders')
}

export function useCarriers() {
  return useApi<any[]>('/api/carriers')
}

export function useShipments() {
  return useApi<any[]>('/api/shipments')
}
