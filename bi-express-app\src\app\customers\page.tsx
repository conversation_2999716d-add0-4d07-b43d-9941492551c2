import { Suspense } from 'react'
import { CustomersList } from '@/components/customers/CustomersList'
import { CustomerFilters } from '@/components/customers/CustomerFilters'
import { AddCustomerButton } from '@/components/customers/AddCustomerButton'

export default function CustomersPage() {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 pb-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Clients</h1>
          <p className="text-gray-600 mt-1">
            Gestion de votre clientèle à Dakar, Sénégal
          </p>
        </div>
        <AddCustomerButton />
      </div>

      {/* Filters */}
      <Suspense fallback={<div className="animate-pulse h-16 bg-gray-200 rounded-lg" />}>
        <CustomerFilters />
      </Suspense>

      {/* Customers List */}
      <Suspense fallback={<div className="animate-pulse h-96 bg-gray-200 rounded-lg" />}>
        <CustomersList />
      </Suspense>
    </div>
  )
}
