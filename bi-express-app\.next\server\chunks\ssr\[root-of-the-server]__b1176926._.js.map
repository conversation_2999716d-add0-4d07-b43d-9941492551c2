{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: 'NGN' | 'XOF' = 'XOF'): string {\n  const symbols = {\n    NGN: '₦',\n    XOF: 'CFA'\n  }\n  \n  return `${amount.toLocaleString('fr-FR')} ${symbols[currency]}`\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric'\n  }).format(date)\n}\n\nexport function formatDateTime(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  }).format(date)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAA0B,KAAK;IAC5E,MAAM,UAAU;QACd,KAAK;QACL,KAAK;IACP;IAEA,OAAO,GAAG,OAAO,cAAc,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,SAAS,EAAE;AACjE;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;IACR,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAU;IACvC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/pricing.ts"], "sourcesContent": ["// Utilitaires pour les calculs de prix et marges\n\nexport interface PricingCalculation {\n  supplierPrice: number;\n  logisticCosts: number;\n  costPrice: number; // Prix de revient\n  sellingPrice: number;\n  profit: number;\n  marginPercentage: number;\n}\n\n/**\n * Calcule tous les prix et marges pour un produit\n */\nexport function calculatePricing(\n  supplierPrice: number,\n  logisticRate: number = 0.30,\n  marginRate: number = 0.20\n): PricingCalculation {\n  const logisticCosts = supplierPrice * logisticRate;\n  const costPrice = supplierPrice + logisticCosts;\n  const sellingPrice = costPrice * (1 + marginRate);\n  const profit = sellingPrice - costPrice;\n  const marginPercentage = (profit / sellingPrice) * 100;\n\n  return {\n    supplierPrice,\n    logisticCosts,\n    costPrice,\n    sellingPrice,\n    profit,\n    marginPercentage\n  };\n}\n\n/**\n * Calcule le prix de vente à partir d'une marge souhaitée\n */\nexport function calculateSellingPriceFromMargin(\n  supplierPrice: number,\n  logisticRate: number,\n  desiredMarginPercentage: number\n): number {\n  const logisticCosts = supplierPrice * logisticRate;\n  const costPrice = supplierPrice + logisticCosts;\n  return costPrice / (1 - desiredMarginPercentage / 100);\n}\n\n/**\n * Calcule les frais de transport selon le mode\n */\nexport function calculateTransportCosts(\n  weight: number,\n  mode: 'ROAD' | 'AIR_EXPRESS'\n): number {\n  const rates = {\n    ROAD: 500, // 500 XOF par kg\n    AIR_EXPRESS: 2000 // 2000 XOF par kg\n  };\n  \n  return weight * rates[mode];\n}\n\n/**\n * Formate un montant en devise\n */\nexport function formatCurrency(\n  amount: number,\n  currency: 'NGN' | 'XOF' = 'XOF'\n): string {\n  const symbols = {\n    NGN: '₦',\n    XOF: 'CFA'\n  };\n  \n  return `${amount.toLocaleString('fr-FR')} ${symbols[currency]}`;\n}\n\n/**\n * Convertit NGN vers XOF\n */\nexport function convertNGNtoXOF(\n  amountNGN: number,\n  exchangeRate: number = 0.85\n): number {\n  return amountNGN * exchangeRate;\n}\n"], "names": [], "mappings": "AAAA,iDAAiD;;;;;;;;AAc1C,SAAS,iBACd,aAAqB,EACrB,eAAuB,IAAI,EAC3B,aAAqB,IAAI;IAEzB,MAAM,gBAAgB,gBAAgB;IACtC,MAAM,YAAY,gBAAgB;IAClC,MAAM,eAAe,YAAY,CAAC,IAAI,UAAU;IAChD,MAAM,SAAS,eAAe;IAC9B,MAAM,mBAAmB,AAAC,SAAS,eAAgB;IAEnD,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS,gCACd,aAAqB,EACrB,YAAoB,EACpB,uBAA+B;IAE/B,MAAM,gBAAgB,gBAAgB;IACtC,MAAM,YAAY,gBAAgB;IAClC,OAAO,YAAY,CAAC,IAAI,0BAA0B,GAAG;AACvD;AAKO,SAAS,wBACd,MAAc,EACd,IAA4B;IAE5B,MAAM,QAAQ;QACZ,MAAM;QACN,aAAa,KAAK,kBAAkB;IACtC;IAEA,OAAO,SAAS,KAAK,CAAC,KAAK;AAC7B;AAKO,SAAS,eACd,MAAc,EACd,WAA0B,KAAK;IAE/B,MAAM,UAAU;QACd,KAAK;QACL,KAAK;IACP;IAEA,OAAO,GAAG,OAAO,cAAc,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,SAAS,EAAE;AACjE;AAKO,SAAS,gBACd,SAAiB,EACjB,eAAuB,IAAI;IAE3B,OAAO,YAAY;AACrB", "debugId": null}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/Badge.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface BadgeProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function Badge({ children, className }: BadgeProps) {\n  return (\n    <span className={cn(\n      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',\n      className\n    )}>\n      {children}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOO,SAAS,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAc;IACvD,qBACE,8OAAC;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,2EACA;kBAEC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/ProductsList.tsx"], "sourcesContent": ["import { prisma } from '@/lib/prisma'\nimport { formatCurrency } from '@/lib/utils'\nimport { calculatePricing } from '@/lib/pricing'\nimport { Badge } from '@/components/ui/Badge'\nimport { Package, AlertTriangle, TrendingUp, Edit, Eye } from 'lucide-react'\n\nasync function getProducts() {\n  return await prisma.product.findMany({\n    where: { isActive: true },\n    include: {\n      supplier: true\n    },\n    orderBy: { createdAt: 'desc' }\n  })\n}\n\nconst categoryColors = {\n  TISSUS: 'bg-blue-100 text-blue-800',\n  COSMETIQUES: 'bg-pink-100 text-pink-800',\n  MECHES: 'bg-purple-100 text-purple-800'\n}\n\nconst categoryLabels = {\n  TISSUS: 'Tissus',\n  COSMETIQUES: 'Cosmétiques',\n  MECHES: 'Mèches'\n}\n\nconst cityColors = {\n  LAGOS: 'bg-blue-50 text-blue-700',\n  ABUJA: 'bg-green-50 text-green-700',\n  KANO: 'bg-purple-50 text-purple-700'\n}\n\nexport async function ProductsList() {\n  const products = await getProducts()\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n      <div className=\"overflow-x-auto\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Produit\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Fournisseur\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Prix & Marges\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Stock\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Bénéfice\n              </th>\n              <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Actions\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {products.map((product) => {\n              const pricing = calculatePricing(\n                product.supplierPrice,\n                product.logisticRate,\n                product.margin\n              )\n              const isLowStock = product.stockQuantity <= product.minStockAlert\n\n              return (\n                <tr key={product.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0 h-10 w-10\">\n                        <div className=\"h-10 w-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center\">\n                          <Package className=\"h-5 w-5 text-white\" />\n                        </div>\n                      </div>\n                      <div className=\"ml-4\">\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {product.name}\n                        </div>\n                        <div className=\"flex items-center space-x-2 mt-1\">\n                          <Badge className={categoryColors[product.category]}>\n                            {categoryLabels[product.category]}\n                          </Badge>\n                          {/* Spécificités selon la catégorie */}\n                          {product.category === 'TISSUS' && product.fabricType && (\n                            <span className=\"text-xs text-gray-500\">\n                              {product.fabricType} - {product.width}m\n                            </span>\n                          )}\n                          {product.category === 'COSMETIQUES' && product.brand && (\n                            <span className=\"text-xs text-gray-500\">\n                              {product.brand} - {product.volume}ml\n                            </span>\n                          )}\n                          {product.category === 'MECHES' && product.length && (\n                            <span className=\"text-xs text-gray-500\">\n                              {product.length}\" - {product.hairType}\n                            </span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      {product.supplier.name}\n                    </div>\n                    <Badge className={cityColors[product.supplier.city]}>\n                      {product.supplier.city}\n                    </Badge>\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm text-gray-900\">\n                      <div className=\"font-medium\">\n                        {formatCurrency(pricing.sellingPrice)}\n                      </div>\n                      <div className=\"text-xs text-gray-500\">\n                        Fournisseur: {formatCurrency(pricing.supplierPrice)}\n                      </div>\n                      <div className=\"text-xs text-gray-500\">\n                        Logistique: {formatCurrency(pricing.logisticCosts)}\n                      </div>\n                      <div className=\"text-xs text-green-600\">\n                        Marge: {pricing.marginPercentage.toFixed(1)}%\n                      </div>\n                    </div>\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        {product.stockQuantity}\n                      </div>\n                      {isLowStock && (\n                        <AlertTriangle className=\"h-4 w-4 text-red-500 ml-2\" />\n                      )}\n                    </div>\n                    <div className=\"text-xs text-gray-500\">\n                      Min: {product.minStockAlert}\n                    </div>\n                    {isLowStock && (\n                      <div className=\"text-xs text-red-600 font-medium\">\n                        Stock faible !\n                      </div>\n                    )}\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-green-600\">\n                      {formatCurrency(pricing.profit)}\n                    </div>\n                    <div className=\"text-xs text-gray-500\">\n                      par unité\n                    </div>\n                    <div className=\"text-xs text-green-600\">\n                      Total: {formatCurrency(pricing.profit * product.stockQuantity)}\n                    </div>\n                  </td>\n                  \n                  <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                    <div className=\"flex items-center justify-end space-x-2\">\n                      <button className=\"text-blue-600 hover:text-blue-900\">\n                        <Eye className=\"h-4 w-4\" />\n                      </button>\n                      <button className=\"text-gray-600 hover:text-gray-900\">\n                        <Edit className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              )\n            })}\n          </tbody>\n        </table>\n      </div>\n      \n      {products.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Package className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n          <p className=\"text-gray-500 text-lg\">Aucun produit trouvé</p>\n          <p className=\"text-gray-400 text-sm mt-1\">\n            Commencez par ajouter votre premier produit\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;;AAEA,eAAe;IACb,OAAO,MAAM,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;QACnC,OAAO;YAAE,UAAU;QAAK;QACxB,SAAS;YACP,UAAU;QACZ;QACA,SAAS;YAAE,WAAW;QAAO;IAC/B;AACF;AAEA,MAAM,iBAAiB;IACrB,QAAQ;IACR,aAAa;IACb,QAAQ;AACV;AAEA,MAAM,iBAAiB;IACrB,QAAQ;IACR,aAAa;IACb,QAAQ;AACV;AAEA,MAAM,aAAa;IACjB,OAAO;IACP,OAAO;IACP,MAAM;AACR;AAEO,eAAe;IACpB,MAAM,WAAW,MAAM;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,WAAU;sCACf,cAAA,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAkF;;;;;;;;;;;;;;;;;sCAKpG,8OAAC;4BAAM,WAAU;sCACd,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD,EAC7B,QAAQ,aAAa,EACrB,QAAQ,YAAY,EACpB,QAAQ,MAAM;gCAEhB,MAAM,aAAa,QAAQ,aAAa,IAAI,QAAQ,aAAa;gCAEjE,qBACE,8OAAC;oCAAoB,WAAU;;sDAC7B,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAGvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,QAAQ,IAAI;;;;;;0EAEf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,WAAW,cAAc,CAAC,QAAQ,QAAQ,CAAC;kFAC/C,cAAc,CAAC,QAAQ,QAAQ,CAAC;;;;;;oEAGlC,QAAQ,QAAQ,KAAK,YAAY,QAAQ,UAAU,kBAClD,8OAAC;wEAAK,WAAU;;4EACb,QAAQ,UAAU;4EAAC;4EAAI,QAAQ,KAAK;4EAAC;;;;;;;oEAGzC,QAAQ,QAAQ,KAAK,iBAAiB,QAAQ,KAAK,kBAClD,8OAAC;wEAAK,WAAU;;4EACb,QAAQ,KAAK;4EAAC;4EAAI,QAAQ,MAAM;4EAAC;;;;;;;oEAGrC,QAAQ,QAAQ,KAAK,YAAY,QAAQ,MAAM,kBAC9C,8OAAC;wEAAK,WAAU;;4EACb,QAAQ,MAAM;4EAAC;4EAAK,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQjD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,QAAQ,CAAC,IAAI;;;;;;8DAExB,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAW,UAAU,CAAC,QAAQ,QAAQ,CAAC,IAAI,CAAC;8DAChD,QAAQ,QAAQ,CAAC,IAAI;;;;;;;;;;;;sDAI1B,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,YAAY;;;;;;kEAEtC,8OAAC;wDAAI,WAAU;;4DAAwB;4DACvB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,aAAa;;;;;;;kEAEpD,8OAAC;wDAAI,WAAU;;4DAAwB;4DACxB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,aAAa;;;;;;;kEAEnD,8OAAC;wDAAI,WAAU;;4DAAyB;4DAC9B,QAAQ,gBAAgB,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;sDAKlD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,aAAa;;;;;;wDAEvB,4BACC,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;;;;;;;8DAG7B,8OAAC;oDAAI,WAAU;;wDAAwB;wDAC/B,QAAQ,aAAa;;;;;;;gDAE5B,4BACC,8OAAC;oDAAI,WAAU;8DAAmC;;;;;;;;;;;;sDAMtD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;;;;;;8DAEhC,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;8DAGvC,8OAAC;oDAAI,WAAU;;wDAAyB;wDAC9B,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM,GAAG,QAAQ,aAAa;;;;;;;;;;;;;sDAIjE,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAO,WAAU;kEAChB,cAAA,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;kEAEjB,8OAAC;wDAAO,WAAU;kEAChB,cAAA,8OAAC,2MAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mCApGf,QAAQ,EAAE;;;;;4BA0GvB;;;;;;;;;;;;;;;;;YAKL,SAAS,MAAM,KAAK,mBACnB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wMAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}, {"offset": {"line": 681, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/ProductFilters.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProductFilters = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductFilters() from the server but ProductFilters is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/products/ProductFilters.tsx <module evaluation>\",\n    \"ProductFilters\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,4EACA", "debugId": null}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/ProductFilters.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProductFilters = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductFilters() from the server but ProductFilters is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/products/ProductFilters.tsx\",\n    \"ProductFilters\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,wDACA", "debugId": null}}, {"offset": {"line": 709, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/AddProductButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AddProductButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call AddProductButton() from the server but AddProductButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/products/AddProductButton.tsx <module evaluation>\",\n    \"AddProductButton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8EACA", "debugId": null}}, {"offset": {"line": 733, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/AddProductButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AddProductButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call AddProductButton() from the server but AddProductButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/products/AddProductButton.tsx\",\n    \"AddProductButton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0DACA", "debugId": null}}, {"offset": {"line": 747, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/ProductStats.tsx"], "sourcesContent": ["import { prisma } from '@/lib/prisma'\nimport { formatCurrency } from '@/lib/utils'\nimport { Package, AlertTriangle, TrendingUp, DollarSign } from 'lucide-react'\n\nasync function getProductStats() {\n  const [\n    totalProducts,\n    lowStockProducts,\n    products\n  ] = await Promise.all([\n    prisma.product.count({ where: { isActive: true } }),\n    prisma.product.count({ \n      where: { \n        isActive: true,\n        stockQuantity: { lte: prisma.product.fields.minStockAlert }\n      }\n    }),\n    prisma.product.findMany({\n      where: { isActive: true },\n      select: {\n        supplierPrice: true,\n        margin: true,\n        logisticRate: true,\n        stockQuantity: true\n      }\n    })\n  ])\n\n  // Calculs des statistiques\n  const totalValue = products.reduce((sum, product) => {\n    const costPrice = product.supplierPrice * (1 + product.logisticRate)\n    return sum + (costPrice * product.stockQuantity)\n  }, 0)\n\n  const averageMargin = products.length > 0 \n    ? products.reduce((sum, product) => sum + product.margin, 0) / products.length * 100\n    : 0\n\n  const totalStock = products.reduce((sum, product) => sum + product.stockQuantity, 0)\n\n  return {\n    totalProducts,\n    lowStockProducts,\n    totalValue,\n    averageMargin,\n    totalStock\n  }\n}\n\nexport async function ProductStats() {\n  const stats = await getProductStats()\n\n  const statCards = [\n    {\n      title: 'Total Produits',\n      value: stats.totalProducts.toString(),\n      icon: Package,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-50',\n      description: 'Produits actifs'\n    },\n    {\n      title: 'Stock Total',\n      value: stats.totalStock.toString(),\n      icon: TrendingUp,\n      color: 'text-green-600',\n      bgColor: 'bg-green-50',\n      description: 'Unités en stock'\n    },\n    {\n      title: 'Valeur Stock',\n      value: formatCurrency(stats.totalValue),\n      icon: DollarSign,\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-50',\n      description: 'Valeur totale'\n    },\n    {\n      title: 'Stock Faible',\n      value: stats.lowStockProducts.toString(),\n      icon: AlertTriangle,\n      color: 'text-red-600',\n      bgColor: 'bg-red-50',\n      description: 'Produits à réapprovisionner'\n    }\n  ]\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n      {statCards.map((stat, index) => (\n        <div key={index} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">{stat.title}</p>\n              <p className=\"text-2xl font-bold text-gray-900 mt-1\">{stat.value}</p>\n              <p className=\"text-xs text-gray-500 mt-1\">{stat.description}</p>\n            </div>\n            <div className={`p-3 rounded-lg ${stat.bgColor}`}>\n              <stat.icon className={`h-6 w-6 ${stat.color}`} />\n            </div>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;;;;;AAEA,eAAe;IACb,MAAM,CACJ,eACA,kBACA,SACD,GAAG,MAAM,QAAQ,GAAG,CAAC;QACpB,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YAAE,OAAO;gBAAE,UAAU;YAAK;QAAE;QACjD,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YACnB,OAAO;gBACL,UAAU;gBACV,eAAe;oBAAE,KAAK,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa;gBAAC;YAC5D;QACF;QACA,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YACtB,OAAO;gBAAE,UAAU;YAAK;YACxB,QAAQ;gBACN,eAAe;gBACf,QAAQ;gBACR,cAAc;gBACd,eAAe;YACjB;QACF;KACD;IAED,2BAA2B;IAC3B,MAAM,aAAa,SAAS,MAAM,CAAC,CAAC,KAAK;QACvC,MAAM,YAAY,QAAQ,aAAa,GAAG,CAAC,IAAI,QAAQ,YAAY;QACnE,OAAO,MAAO,YAAY,QAAQ,aAAa;IACjD,GAAG;IAEH,MAAM,gBAAgB,SAAS,MAAM,GAAG,IACpC,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE,KAAK,SAAS,MAAM,GAAG,MAC/E;IAEJ,MAAM,aAAa,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,aAAa,EAAE;IAElF,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAEO,eAAe;IACpB,MAAM,QAAQ,MAAM;IAEpB,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,MAAM,aAAa,CAAC,QAAQ;YACnC,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,MAAM,UAAU,CAAC,QAAQ;YAChC,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,UAAU;YACtC,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,MAAM,gBAAgB,CAAC,QAAQ;YACtC,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,SAAS;YACT,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;gBAAgB,WAAU;0BACzB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAAqC,KAAK,KAAK;;;;;;8CAC5D,8OAAC;oCAAE,WAAU;8CAAyC,KAAK,KAAK;;;;;;8CAChE,8OAAC;oCAAE,WAAU;8CAA8B,KAAK,WAAW;;;;;;;;;;;;sCAE7D,8OAAC;4BAAI,WAAW,CAAC,eAAe,EAAE,KAAK,OAAO,EAAE;sCAC9C,cAAA,8OAAC,KAAK,IAAI;gCAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;;;;;;;eARzC;;;;;;;;;;AAelB", "debugId": null}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/products/page.tsx"], "sourcesContent": ["import { Suspense } from 'react'\nimport { ProductsList } from '@/components/products/ProductsList'\nimport { ProductFilters } from '@/components/products/ProductFilters'\nimport { AddProductButton } from '@/components/products/AddProductButton'\nimport { ProductStats } from '@/components/products/ProductStats'\n\nexport default function ProductsPage() {\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between border-b border-gray-200 pb-4\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Catalogue Produits</h1>\n          <p className=\"text-gray-600 mt-1\">\n            Gérez vos produits avec calcul automatique des prix et marges\n          </p>\n        </div>\n        <AddProductButton />\n      </div>\n\n      {/* Stats */}\n      <Suspense fallback={<div className=\"animate-pulse h-24 bg-gray-200 rounded-lg\" />}>\n        <ProductStats />\n      </Suspense>\n\n      {/* Filters */}\n      <Suspense fallback={<div className=\"animate-pulse h-16 bg-gray-200 rounded-lg\" />}>\n        <ProductFilters />\n      </Suspense>\n\n      {/* Products List */}\n      <Suspense fallback={<div className=\"animate-pulse h-96 bg-gray-200 rounded-lg\" />}>\n        <ProductsList />\n      </Suspense>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,8OAAC,kJAAA,CAAA,mBAAgB;;;;;;;;;;;0BAInB,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;oBAAI,WAAU;;;;;;0BACjC,cAAA,8OAAC,8IAAA,CAAA,eAAY;;;;;;;;;;0BAIf,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;oBAAI,WAAU;;;;;;0BACjC,cAAA,8OAAC,gJAAA,CAAA,iBAAc;;;;;;;;;;0BAIjB,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;oBAAI,WAAU;;;;;;0BACjC,cAAA,8OAAC,8IAAA,CAAA,eAAY;;;;;;;;;;;;;;;;AAIrB", "debugId": null}}]}