'use client'

import { useState } from 'react'
import { Plus } from 'lucide-react'
import { NewShipmentForm } from './NewShipmentForm'

export function CreateShipmentButton() {
  const [showForm, setShowForm] = useState(false)

  return (
    <>
      <button
        type="button"
        onClick={() => setShowForm(true)}
        className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
      >
        <Plus className="h-4 w-4" />
        Nouvelle expédition
      </button>

      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">Nouvelle expédition</h2>
                <button
                  type="button"
                  onClick={() => setShowForm(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <span className="sr-only">Fermer</span>
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <NewShipmentForm onSuccess={() => setShowForm(false)} onCancel={() => setShowForm(false)} />
            </div>
          </div>
        </div>
      )}
    </>
  )
}
