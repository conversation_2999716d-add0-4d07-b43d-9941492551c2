{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/suppliers/SupplierFilters.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Search, Filter } from 'lucide-react'\n\nexport function SupplierFilters() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedCity, setSelectedCity] = useState('')\n  const [minRating, setMinRating] = useState('')\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n      <div className=\"flex items-center space-x-4\">\n        {/* Search */}\n        <div className=\"flex-1 relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n          <input\n            type=\"text\"\n            placeholder=\"Rechercher un fournisseur...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n        </div>\n\n        {/* City Filter */}\n        <div className=\"min-w-0 flex-shrink-0\">\n          <select\n            value={selectedCity}\n            onChange={(e) => setSelectedCity(e.target.value)}\n            className=\"px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"\">Toutes les villes</option>\n            <option value=\"LAGOS\">Lagos</option>\n            <option value=\"ABUJA\">Abuja</option>\n            <option value=\"KANO\">Kano</option>\n          </select>\n        </div>\n\n        {/* Rating Filter */}\n        <div className=\"min-w-0 flex-shrink-0\">\n          <select\n            value={minRating}\n            onChange={(e) => setMinRating(e.target.value)}\n            className=\"px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"\">Toutes les notes</option>\n            <option value=\"4\">4+ étoiles</option>\n            <option value=\"3\">3+ étoiles</option>\n            <option value=\"2\">2+ étoiles</option>\n          </select>\n        </div>\n\n        {/* Filter Button */}\n        <button className=\"flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors\">\n          <Filter className=\"h-4 w-4 mr-2\" />\n          Filtrer\n        </button>\n      </div>\n\n      {/* Active Filters */}\n      {(searchTerm || selectedCity || minRating) && (\n        <div className=\"mt-4 flex items-center space-x-2\">\n          <span className=\"text-sm text-gray-500\">Filtres actifs:</span>\n          {searchTerm && (\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n              Recherche: {searchTerm}\n              <button\n                onClick={() => setSearchTerm('')}\n                className=\"ml-1 text-blue-600 hover:text-blue-800\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n          {selectedCity && (\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n              Ville: {selectedCity}\n              <button\n                onClick={() => setSelectedCity('')}\n                className=\"ml-1 text-green-600 hover:text-green-800\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n          {minRating && (\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n              Note: {minRating}+ étoiles\n              <button\n                onClick={() => setMinRating('')}\n                className=\"ml-1 text-yellow-600 hover:text-yellow-800\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4BAC/C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,6LAAC;oCAAO,OAAM;8CAAQ;;;;;;8CACtB,6LAAC;oCAAO,OAAM;8CAAQ;;;;;;8CACtB,6LAAC;oCAAO,OAAM;8CAAO;;;;;;;;;;;;;;;;;kCAKzB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4BAC5C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,6LAAC;oCAAO,OAAM;8CAAI;;;;;;8CAClB,6LAAC;oCAAO,OAAM;8CAAI;;;;;;8CAClB,6LAAC;oCAAO,OAAM;8CAAI;;;;;;;;;;;;;;;;;kCAKtB,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMtC,CAAC,cAAc,gBAAgB,SAAS,mBACvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;oBACvC,4BACC,6LAAC;wBAAK,WAAU;;4BAAoG;4BACtG;0CACZ,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAU;0CACX;;;;;;;;;;;;oBAKJ,8BACC,6LAAC;wBAAK,WAAU;;4BAAsG;4BAC5G;0CACR,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;;;;;;;oBAKJ,2BACC,6LAAC;wBAAK,WAAU;;4BAAwG;4BAC/G;4BAAU;0CACjB,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAhGgB;KAAA", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/suppliers/AddSupplierButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Plus } from 'lucide-react'\n\nexport function AddSupplierButton() {\n  const [isOpen, setIsOpen] = useState(false)\n\n  return (\n    <>\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n      >\n        <Plus className=\"h-4 w-4 mr-2\" />\n        Ajouter un fournisseur\n      </button>\n\n      {/* Modal placeholder - à implémenter plus tard */}\n      {isOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 w-full max-w-md\">\n            <h2 className=\"text-lg font-semibold mb-4\">Ajouter un fournisseur</h2>\n            <p className=\"text-gray-600 mb-4\">\n              Formulaire d'ajout de fournisseur à implémenter\n            </p>\n            <button\n              onClick={() => setIsOpen(false)}\n              className=\"px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors\"\n            >\n              Fermer\n            </button>\n          </div>\n        </div>\n      )}\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE;;0BACE,6LAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAU;;kCAEV,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;YAKlC,wBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,6LAAC;4BACC,SAAS,IAAM,UAAU;4BACzB,WAAU;sCACX;;;;;;;;;;;;;;;;;;;AAQb;GAhCgB;KAAA", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "file": "search.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm21 21-4.34-4.34', key: '14j7rj' }],\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n];\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEgMjEtNC4zNC00LjM0IiAvPgogIDxjaXJjbGUgY3g9IjExIiBjeT0iMTEiIHI9IjgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('search', __iconNode);\n\nexport default Search;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "file": "funnel.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/funnel.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z',\n      key: 'sc7q7i',\n    },\n  ],\n];\n\n/**\n * @component @name Funnel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMjBhMSAxIDAgMCAwIC41NTMuODk1bDIgMUExIDEgMCAwIDAgMTQgMjF2LTdhMiAyIDAgMCAxIC41MTctMS4zNDFMMjEuNzQgNC42N0ExIDEgMCAwIDAgMjEgM0gzYTEgMSAwIDAgMC0uNzQyIDEuNjdsNy4yMjUgNy45ODlBMiAyIDAgMCAxIDEwIDE0eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/funnel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Funnel = createLucideIcon('funnel', __iconNode);\n\nexport default Funnel;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}