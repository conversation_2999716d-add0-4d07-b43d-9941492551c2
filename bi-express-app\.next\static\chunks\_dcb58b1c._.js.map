{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/currency.ts"], "sourcesContent": ["// Service de conversion de devises NGN/XOF\nexport interface ExchangeRate {\n  from: 'NGN' | 'XOF'\n  to: 'NGN' | 'XOF'\n  rate: number\n  lastUpdated: Date\n}\n\nexport interface CurrencyConversion {\n  amount: number\n  fromCurrency: 'NGN' | 'XOF'\n  toCurrency: 'NGN' | 'XOF'\n  convertedAmount: number\n  exchangeRate: number\n  timestamp: Date\n}\n\n// Cache des taux de change pour éviter trop d'appels API\nlet exchangeRateCache: {\n  NGN_TO_XOF: { rate: number; timestamp: Date } | null\n  XOF_TO_NGN: { rate: number; timestamp: Date } | null\n} = {\n  NGN_TO_XOF: null,\n  XOF_TO_NGN: null\n}\n\nconst CACHE_DURATION = 30 * 60 * 1000 // 30 minutes en millisecondes\n\n// Taux de change par défaut (fallback) basés sur les taux moyens récents\nconst DEFAULT_RATES = {\n  NGN_TO_XOF: 0.38, // 1 NGN ≈ 0.38 XOF\n  XOF_TO_NGN: 2.63   // 1 XOF ≈ 2.63 NGN\n}\n\n/**\n * Récupère les taux de change depuis l'API ExchangeRate-API\n */\nasync function fetchExchangeRates(): Promise<{ NGN_TO_XOF: number; XOF_TO_NGN: number }> {\n  try {\n    // Utilisation de l'API gratuite ExchangeRate-API\n    const response = await fetch('https://api.exchangerate-api.com/v4/latest/NGN', {\n      next: { revalidate: 1800 } // Cache pendant 30 minutes\n    })\n    \n    if (!response.ok) {\n      throw new Error(`API Error: ${response.status}`)\n    }\n    \n    const data = await response.json()\n    \n    // Vérifier si XOF est disponible dans les taux\n    if (!data.rates || !data.rates.XOF) {\n      throw new Error('XOF rate not available')\n    }\n    \n    const ngnToXof = data.rates.XOF\n    const xofToNgn = 1 / ngnToXof\n    \n    return {\n      NGN_TO_XOF: ngnToXof,\n      XOF_TO_NGN: xofToNgn\n    }\n  } catch (error) {\n    console.warn('Erreur lors de la récupération des taux de change:', error)\n    \n    // Fallback vers les taux par défaut\n    return {\n      NGN_TO_XOF: DEFAULT_RATES.NGN_TO_XOF,\n      XOF_TO_NGN: DEFAULT_RATES.XOF_TO_NGN\n    }\n  }\n}\n\n/**\n * Obtient le taux de change actuel avec mise en cache\n */\nexport async function getExchangeRate(from: 'NGN' | 'XOF', to: 'NGN' | 'XOF'): Promise<ExchangeRate> {\n  if (from === to) {\n    return {\n      from,\n      to,\n      rate: 1,\n      lastUpdated: new Date()\n    }\n  }\n  \n  const cacheKey = `${from}_TO_${to}` as keyof typeof exchangeRateCache\n  const cached = exchangeRateCache[cacheKey]\n  \n  // Vérifier si le cache est encore valide\n  if (cached && (Date.now() - cached.timestamp.getTime()) < CACHE_DURATION) {\n    return {\n      from,\n      to,\n      rate: cached.rate,\n      lastUpdated: cached.timestamp\n    }\n  }\n  \n  // Récupérer les nouveaux taux\n  try {\n    const rates = await fetchExchangeRates()\n    const now = new Date()\n    \n    // Mettre à jour le cache\n    exchangeRateCache.NGN_TO_XOF = {\n      rate: rates.NGN_TO_XOF,\n      timestamp: now\n    }\n    exchangeRateCache.XOF_TO_NGN = {\n      rate: rates.XOF_TO_NGN,\n      timestamp: now\n    }\n    \n    const rate = cacheKey === 'NGN_TO_XOF' ? rates.NGN_TO_XOF : rates.XOF_TO_NGN\n    \n    return {\n      from,\n      to,\n      rate,\n      lastUpdated: now\n    }\n  } catch (error) {\n    console.error('Erreur lors de la récupération du taux de change:', error)\n    \n    // Utiliser les taux par défaut\n    const rate = from === 'NGN' ? DEFAULT_RATES.NGN_TO_XOF : DEFAULT_RATES.XOF_TO_NGN\n    \n    return {\n      from,\n      to,\n      rate,\n      lastUpdated: new Date()\n    }\n  }\n}\n\n/**\n * Convertit un montant d'une devise à une autre\n */\nexport async function convertCurrency(\n  amount: number,\n  fromCurrency: 'NGN' | 'XOF',\n  toCurrency: 'NGN' | 'XOF'\n): Promise<CurrencyConversion> {\n  const exchangeRate = await getExchangeRate(fromCurrency, toCurrency)\n  const convertedAmount = amount * exchangeRate.rate\n  \n  return {\n    amount,\n    fromCurrency,\n    toCurrency,\n    convertedAmount: Math.round(convertedAmount * 100) / 100, // Arrondir à 2 décimales\n    exchangeRate: exchangeRate.rate,\n    timestamp: new Date()\n  }\n}\n\n/**\n * Formate un montant avec la devise appropriée\n */\nexport function formatCurrency(amount: number, currency: 'NGN' | 'XOF'): string {\n  const formatter = new Intl.NumberFormat('fr-FR', {\n    style: 'currency',\n    currency: currency,\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0\n  })\n  \n  return formatter.format(amount)\n}\n\n/**\n * Affiche un montant dans les deux devises\n */\nexport async function formatDualCurrency(\n  amount: number,\n  baseCurrency: 'NGN' | 'XOF'\n): Promise<string> {\n  const otherCurrency = baseCurrency === 'NGN' ? 'XOF' : 'NGN'\n  const conversion = await convertCurrency(amount, baseCurrency, otherCurrency)\n  \n  const baseFormatted = formatCurrency(amount, baseCurrency)\n  const convertedFormatted = formatCurrency(conversion.convertedAmount, otherCurrency)\n  \n  return `${baseFormatted} (≈ ${convertedFormatted})`\n}\n\n/**\n * Obtient les taux de change actuels pour l'affichage\n */\nexport async function getCurrentRates(): Promise<{\n  ngnToXof: number\n  xofToNgn: number\n  lastUpdated: Date\n}> {\n  const ngnRate = await getExchangeRate('NGN', 'XOF')\n  const xofRate = await getExchangeRate('XOF', 'NGN')\n  \n  return {\n    ngnToXof: ngnRate.rate,\n    xofToNgn: xofRate.rate,\n    lastUpdated: ngnRate.lastUpdated\n  }\n}\n\n/**\n * Vide le cache des taux de change (utile pour forcer une mise à jour)\n */\nexport function clearExchangeRateCache(): void {\n  exchangeRateCache = {\n    NGN_TO_XOF: null,\n    XOF_TO_NGN: null\n  }\n}\n\n/**\n * Calcule la marge en tenant compte de la conversion de devise\n */\nexport async function calculateMarginWithCurrency(\n  costPrice: number,\n  costCurrency: 'NGN' | 'XOF',\n  sellingPrice: number,\n  sellingCurrency: 'NGN' | 'XOF'\n): Promise<{\n  marginAmount: number\n  marginPercentage: number\n  currency: 'NGN' | 'XOF'\n}> {\n  let normalizedCostPrice = costPrice\n  let normalizedSellingPrice = sellingPrice\n  let resultCurrency = sellingCurrency\n  \n  // Convertir le prix de coût dans la même devise que le prix de vente\n  if (costCurrency !== sellingCurrency) {\n    const conversion = await convertCurrency(costPrice, costCurrency, sellingCurrency)\n    normalizedCostPrice = conversion.convertedAmount\n  }\n  \n  const marginAmount = normalizedSellingPrice - normalizedCostPrice\n  const marginPercentage = normalizedCostPrice > 0 \n    ? (marginAmount / normalizedCostPrice) * 100 \n    : 0\n  \n  return {\n    marginAmount: Math.round(marginAmount * 100) / 100,\n    marginPercentage: Math.round(marginPercentage * 100) / 100,\n    currency: resultCurrency\n  }\n}\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;;;;;;;AAiB3C,yDAAyD;AACzD,IAAI,oBAGA;IACF,YAAY;IACZ,YAAY;AACd;AAEA,MAAM,iBAAiB,KAAK,KAAK,KAAK,8BAA8B;;AAEpE,yEAAyE;AACzE,MAAM,gBAAgB;IACpB,YAAY;IACZ,YAAY,KAAO,mBAAmB;AACxC;AAEA;;CAEC,GACD,eAAe;IACb,IAAI;QACF,iDAAiD;QACjD,MAAM,WAAW,MAAM,MAAM,kDAAkD;YAC7E,MAAM;gBAAE,YAAY;YAAK,EAAE,2BAA2B;QACxD;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,EAAE;QACjD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,+CAA+C;QAC/C,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,GAAG,EAAE;YAClC,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,KAAK,KAAK,CAAC,GAAG;QAC/B,MAAM,WAAW,IAAI;QAErB,OAAO;YACL,YAAY;YACZ,YAAY;QACd;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,sDAAsD;QAEnE,oCAAoC;QACpC,OAAO;YACL,YAAY,cAAc,UAAU;YACpC,YAAY,cAAc,UAAU;QACtC;IACF;AACF;AAKO,eAAe,gBAAgB,IAAmB,EAAE,EAAiB;IAC1E,IAAI,SAAS,IAAI;QACf,OAAO;YACL;YACA;YACA,MAAM;YACN,aAAa,IAAI;QACnB;IACF;IAEA,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE,IAAI;IACnC,MAAM,SAAS,iBAAiB,CAAC,SAAS;IAE1C,yCAAyC;IACzC,IAAI,UAAU,AAAC,KAAK,GAAG,KAAK,OAAO,SAAS,CAAC,OAAO,KAAM,gBAAgB;QACxE,OAAO;YACL;YACA;YACA,MAAM,OAAO,IAAI;YACjB,aAAa,OAAO,SAAS;QAC/B;IACF;IAEA,8BAA8B;IAC9B,IAAI;QACF,MAAM,QAAQ,MAAM;QACpB,MAAM,MAAM,IAAI;QAEhB,yBAAyB;QACzB,kBAAkB,UAAU,GAAG;YAC7B,MAAM,MAAM,UAAU;YACtB,WAAW;QACb;QACA,kBAAkB,UAAU,GAAG;YAC7B,MAAM,MAAM,UAAU;YACtB,WAAW;QACb;QAEA,MAAM,OAAO,aAAa,eAAe,MAAM,UAAU,GAAG,MAAM,UAAU;QAE5E,OAAO;YACL;YACA;YACA;YACA,aAAa;QACf;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QAEnE,+BAA+B;QAC/B,MAAM,OAAO,SAAS,QAAQ,cAAc,UAAU,GAAG,cAAc,UAAU;QAEjF,OAAO;YACL;YACA;YACA;YACA,aAAa,IAAI;QACnB;IACF;AACF;AAKO,eAAe,gBACpB,MAAc,EACd,YAA2B,EAC3B,UAAyB;IAEzB,MAAM,eAAe,MAAM,gBAAgB,cAAc;IACzD,MAAM,kBAAkB,SAAS,aAAa,IAAI;IAElD,OAAO;QACL;QACA;QACA;QACA,iBAAiB,KAAK,KAAK,CAAC,kBAAkB,OAAO;QACrD,cAAc,aAAa,IAAI;QAC/B,WAAW,IAAI;IACjB;AACF;AAKO,SAAS,eAAe,MAAc,EAAE,QAAuB;IACpE,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,SAAS;QAC/C,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB;IAEA,OAAO,UAAU,MAAM,CAAC;AAC1B;AAKO,eAAe,mBACpB,MAAc,EACd,YAA2B;IAE3B,MAAM,gBAAgB,iBAAiB,QAAQ,QAAQ;IACvD,MAAM,aAAa,MAAM,gBAAgB,QAAQ,cAAc;IAE/D,MAAM,gBAAgB,eAAe,QAAQ;IAC7C,MAAM,qBAAqB,eAAe,WAAW,eAAe,EAAE;IAEtE,OAAO,GAAG,cAAc,IAAI,EAAE,mBAAmB,CAAC,CAAC;AACrD;AAKO,eAAe;IAKpB,MAAM,UAAU,MAAM,gBAAgB,OAAO;IAC7C,MAAM,UAAU,MAAM,gBAAgB,OAAO;IAE7C,OAAO;QACL,UAAU,QAAQ,IAAI;QACtB,UAAU,QAAQ,IAAI;QACtB,aAAa,QAAQ,WAAW;IAClC;AACF;AAKO,SAAS;IACd,oBAAoB;QAClB,YAAY;QACZ,YAAY;IACd;AACF;AAKO,eAAe,4BACpB,SAAiB,EACjB,YAA2B,EAC3B,YAAoB,EACpB,eAA8B;IAM9B,IAAI,sBAAsB;IAC1B,IAAI,yBAAyB;IAC7B,IAAI,iBAAiB;IAErB,qEAAqE;IACrE,IAAI,iBAAiB,iBAAiB;QACpC,MAAM,aAAa,MAAM,gBAAgB,WAAW,cAAc;QAClE,sBAAsB,WAAW,eAAe;IAClD;IAEA,MAAM,eAAe,yBAAyB;IAC9C,MAAM,mBAAmB,sBAAsB,IAC3C,AAAC,eAAe,sBAAuB,MACvC;IAEJ,OAAO;QACL,cAAc,KAAK,KAAK,CAAC,eAAe,OAAO;QAC/C,kBAAkB,KAAK,KAAK,CAAC,mBAAmB,OAAO;QACvD,UAAU;IACZ;AACF", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/currency/ExchangeRateWidget.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { getCurrentRates, clearExchangeRateCache } from '@/lib/currency'\nimport { RefreshCw, TrendingUp, TrendingDown, Clock } from 'lucide-react'\n\ninterface ExchangeRateWidgetProps {\n  className?: string\n  showRefreshButton?: boolean\n}\n\nexport function ExchangeRateWidget({ \n  className = '', \n  showRefreshButton = true \n}: ExchangeRateWidgetProps) {\n  const [rates, setRates] = useState<{\n    ngnToXof: number\n    xofToNgn: number\n    lastUpdated: Date\n  } | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [isRefreshing, setIsRefreshing] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  const fetchRates = async (forceRefresh = false) => {\n    try {\n      setError(null)\n      if (forceRefresh) {\n        setIsRefreshing(true)\n        clearExchangeRateCache()\n      } else {\n        setIsLoading(true)\n      }\n\n      const currentRates = await getCurrentRates()\n      setRates(currentRates)\n    } catch (err) {\n      setError('Erreur lors du chargement des taux')\n      console.error('Erreur taux de change:', err)\n    } finally {\n      setIsLoading(false)\n      setIsRefreshing(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchRates()\n    \n    // Actualiser automatiquement toutes les 30 minutes\n    const interval = setInterval(() => {\n      fetchRates(true)\n    }, 30 * 60 * 1000)\n\n    return () => clearInterval(interval)\n  }, [])\n\n  const formatTime = (date: Date) => {\n    return new Intl.DateTimeFormat('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    }).format(date)\n  }\n\n  const formatRate = (rate: number) => {\n    return rate.toFixed(4)\n  }\n\n  if (isLoading && !rates) {\n    return (\n      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 ${className}`}>\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-2\"></div>\n          <div className=\"h-6 bg-gray-200 rounded w-3/4 mb-1\"></div>\n          <div className=\"h-6 bg-gray-200 rounded w-3/4\"></div>\n        </div>\n      </div>\n    )\n  }\n\n  if (error && !rates) {\n    return (\n      <div className={`bg-white rounded-lg shadow-sm border border-red-200 p-4 ${className}`}>\n        <div className=\"text-red-600 text-sm\">\n          <p className=\"font-medium\">Erreur de chargement</p>\n          <p>{error}</p>\n          <button\n            onClick={() => fetchRates(true)}\n            className=\"mt-2 text-xs underline hover:no-underline\"\n          >\n            Réessayer\n          </button>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 ${className}`}>\n      <div className=\"flex items-center justify-between mb-3\">\n        <h3 className=\"text-sm font-medium text-gray-900 flex items-center\">\n          <TrendingUp className=\"h-4 w-4 mr-2 text-green-600\" />\n          Taux de change\n        </h3>\n        {showRefreshButton && (\n          <button\n            onClick={() => fetchRates(true)}\n            disabled={isRefreshing}\n            className=\"p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50\"\n            title=\"Actualiser les taux\"\n          >\n            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />\n          </button>\n        )}\n      </div>\n\n      {rates && (\n        <div className=\"space-y-3\">\n          {/* NGN vers XOF */}\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-sm font-medium text-gray-700\">1 NGN</span>\n              <span className=\"text-xs text-gray-400\">→</span>\n              <span className=\"text-sm font-medium text-blue-600\">\n                {formatRate(rates.ngnToXof)} XOF\n              </span>\n            </div>\n            <TrendingUp className=\"h-3 w-3 text-green-500\" />\n          </div>\n\n          {/* XOF vers NGN */}\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-sm font-medium text-gray-700\">1 XOF</span>\n              <span className=\"text-xs text-gray-400\">→</span>\n              <span className=\"text-sm font-medium text-blue-600\">\n                {formatRate(rates.xofToNgn)} NGN\n              </span>\n            </div>\n            <TrendingDown className=\"h-3 w-3 text-red-500\" />\n          </div>\n\n          {/* Dernière mise à jour */}\n          <div className=\"pt-2 border-t border-gray-100\">\n            <div className=\"flex items-center text-xs text-gray-500\">\n              <Clock className=\"h-3 w-3 mr-1\" />\n              <span>Mis à jour à {formatTime(rates.lastUpdated)}</span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {error && rates && (\n        <div className=\"mt-2 text-xs text-amber-600 bg-amber-50 p-2 rounded\">\n          ⚠️ Utilisation des taux en cache - {error}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAWO,SAAS,mBAAmB,EACjC,YAAY,EAAE,EACd,oBAAoB,IAAI,EACA;;IACxB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAIvB;IACV,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,aAAa,OAAO,eAAe,KAAK;QAC5C,IAAI;YACF,SAAS;YACT,IAAI,cAAc;gBAChB,gBAAgB;gBAChB,CAAA,GAAA,yHAAA,CAAA,yBAAsB,AAAD;YACvB,OAAO;gBACL,aAAa;YACf;YAEA,MAAM,eAAe,MAAM,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD;YACzC,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;YACb,gBAAgB;QAClB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;YAEA,mDAAmD;YACnD,MAAM,WAAW;yDAAY;oBAC3B,WAAW;gBACb;wDAAG,KAAK,KAAK;YAEb;gDAAO,IAAM,cAAc;;QAC7B;uCAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,OAAO,CAAC;IACtB;IAEA,IAAI,aAAa,CAAC,OAAO;QACvB,qBACE,6LAAC;YAAI,WAAW,CAAC,yDAAyD,EAAE,WAAW;sBACrF,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,SAAS,CAAC,OAAO;QACnB,qBACE,6LAAC;YAAI,WAAW,CAAC,wDAAwD,EAAE,WAAW;sBACpF,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAc;;;;;;kCAC3B,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;wBACC,SAAS,IAAM,WAAW;wBAC1B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,yDAAyD,EAAE,WAAW;;0BACrF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAgC;;;;;;;oBAGvD,mCACC,6LAAC;wBACC,SAAS,IAAM,WAAW;wBAC1B,UAAU;wBACV,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAW,CAAC,QAAQ,EAAE,eAAe,iBAAiB,IAAI;;;;;;;;;;;;;;;;;YAK1E,uBACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;kDACpD,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAU;;4CACb,WAAW,MAAM,QAAQ;4CAAE;;;;;;;;;;;;;0CAGhC,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;kCAIxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;kDACpD,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAU;;4CACb,WAAW,MAAM,QAAQ;4CAAE;;;;;;;;;;;;;0CAGhC,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;kCAI1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;;wCAAK;wCAAc,WAAW,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;;YAMvD,SAAS,uBACR,6LAAC;gBAAI,WAAU;;oBAAsD;oBAC/B;;;;;;;;;;;;;AAK9C;GApJgB;KAAA", "debugId": null}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/currency/CurrencyConverter.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { convertCurrency } from '@/lib/currency'\nimport { ArrowRightLeft, Calculator } from 'lucide-react'\n\ninterface CurrencyConverterProps {\n  className?: string\n  defaultAmount?: number\n  defaultFromCurrency?: 'NGN' | 'XOF'\n}\n\nexport function CurrencyConverter({ \n  className = '',\n  defaultAmount = 1000,\n  defaultFromCurrency = 'NGN'\n}: CurrencyConverterProps) {\n  const [amount, setAmount] = useState<string>(defaultAmount.toString())\n  const [fromCurrency, setFromCurrency] = useState<'NGN' | 'XOF'>(defaultFromCurrency)\n  const [toCurrency, setToCurrency] = useState<'NGN' | 'XOF'>(\n    defaultFromCurrency === 'NGN' ? 'XOF' : 'NGN'\n  )\n  const [convertedAmount, setConvertedAmount] = useState<number | null>(null)\n  const [exchangeRate, setExchangeRate] = useState<number | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  const performConversion = async () => {\n    const numAmount = parseFloat(amount)\n    \n    if (isNaN(numAmount) || numAmount <= 0) {\n      setConvertedAmount(null)\n      setExchangeRate(null)\n      setError('Montant invalide')\n      return\n    }\n\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      const result = await convertCurrency(numAmount, fromCurrency, toCurrency)\n      setConvertedAmount(result.convertedAmount)\n      setExchangeRate(result.exchangeRate)\n    } catch (err) {\n      setError('Erreur de conversion')\n      console.error('Erreur conversion:', err)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    if (amount && parseFloat(amount) > 0) {\n      performConversion()\n    }\n  }, [amount, fromCurrency, toCurrency])\n\n  const swapCurrencies = () => {\n    setFromCurrency(toCurrency)\n    setToCurrency(fromCurrency)\n  }\n\n  const formatCurrency = (value: number, currency: 'NGN' | 'XOF') => {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: currency,\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 2\n    }).format(value)\n  }\n\n  return (\n    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>\n      <div className=\"flex items-center mb-4\">\n        <Calculator className=\"h-5 w-5 text-blue-600 mr-2\" />\n        <h3 className=\"text-lg font-medium text-gray-900\">\n          Convertisseur de devises\n        </h3>\n      </div>\n\n      <div className=\"space-y-4\">\n        {/* Montant source */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Montant à convertir\n          </label>\n          <div className=\"flex space-x-2\">\n            <input\n              type=\"number\"\n              value={amount}\n              onChange={(e) => setAmount(e.target.value)}\n              className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"Entrez le montant\"\n              min=\"0\"\n              step=\"0.01\"\n            />\n            <select\n              value={fromCurrency}\n              onChange={(e) => setFromCurrency(e.target.value as 'NGN' | 'XOF')}\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"NGN\">NGN (Naira)</option>\n              <option value=\"XOF\">XOF (CFA)</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Bouton d'échange */}\n        <div className=\"flex justify-center\">\n          <button\n            onClick={swapCurrencies}\n            className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-full transition-colors\"\n            title=\"Inverser les devises\"\n          >\n            <ArrowRightLeft className=\"h-5 w-5\" />\n          </button>\n        </div>\n\n        {/* Résultat */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Montant converti\n          </label>\n          <div className=\"flex space-x-2\">\n            <div className=\"flex-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-md\">\n              {isLoading ? (\n                <div className=\"animate-pulse\">\n                  <div className=\"h-5 bg-gray-200 rounded w-3/4\"></div>\n                </div>\n              ) : error ? (\n                <span className=\"text-red-500 text-sm\">{error}</span>\n              ) : convertedAmount !== null ? (\n                <span className=\"text-lg font-medium text-gray-900\">\n                  {formatCurrency(convertedAmount, toCurrency)}\n                </span>\n              ) : (\n                <span className=\"text-gray-400\">Résultat</span>\n              )}\n            </div>\n            <div className=\"px-3 py-2 bg-gray-100 border border-gray-300 rounded-md text-gray-600\">\n              {toCurrency === 'NGN' ? 'NGN (Naira)' : 'XOF (CFA)'}\n            </div>\n          </div>\n        </div>\n\n        {/* Taux de change */}\n        {exchangeRate && !error && (\n          <div className=\"bg-blue-50 p-3 rounded-md\">\n            <div className=\"text-sm text-blue-800\">\n              <strong>Taux de change:</strong> 1 {fromCurrency} = {exchangeRate.toFixed(4)} {toCurrency}\n            </div>\n            {convertedAmount && (\n              <div className=\"text-xs text-blue-600 mt-1\">\n                {formatCurrency(parseFloat(amount), fromCurrency)} = {formatCurrency(convertedAmount, toCurrency)}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Exemples rapides */}\n        <div className=\"border-t border-gray-200 pt-4\">\n          <h4 className=\"text-sm font-medium text-gray-700 mb-2\">Conversions rapides</h4>\n          <div className=\"grid grid-cols-2 gap-2 text-xs\">\n            <button\n              onClick={() => setAmount('1000')}\n              className=\"p-2 text-left bg-gray-50 hover:bg-gray-100 rounded border\"\n            >\n              1 000 {fromCurrency}\n            </button>\n            <button\n              onClick={() => setAmount('5000')}\n              className=\"p-2 text-left bg-gray-50 hover:bg-gray-100 rounded border\"\n            >\n              5 000 {fromCurrency}\n            </button>\n            <button\n              onClick={() => setAmount('10000')}\n              className=\"p-2 text-left bg-gray-50 hover:bg-gray-100 rounded border\"\n            >\n              10 000 {fromCurrency}\n            </button>\n            <button\n              onClick={() => setAmount('50000')}\n              className=\"p-2 text-left bg-gray-50 hover:bg-gray-100 rounded border\"\n            >\n              50 000 {fromCurrency}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAYO,SAAS,kBAAkB,EAChC,YAAY,EAAE,EACd,gBAAgB,IAAI,EACpB,sBAAsB,KAAK,EACJ;;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,cAAc,QAAQ;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACzC,wBAAwB,QAAQ,QAAQ;IAE1C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,oBAAoB;QACxB,MAAM,YAAY,WAAW;QAE7B,IAAI,MAAM,cAAc,aAAa,GAAG;YACtC,mBAAmB;YACnB,gBAAgB;YAChB,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE,WAAW,cAAc;YAC9D,mBAAmB,OAAO,eAAe;YACzC,gBAAgB,OAAO,YAAY;QACrC,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,sBAAsB;QACtC,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,UAAU,WAAW,UAAU,GAAG;gBACpC;YACF;QACF;sCAAG;QAAC;QAAQ;QAAc;KAAW;IAErC,MAAM,iBAAiB;QACrB,gBAAgB;QAChB,cAAc;IAChB;IAEA,MAAM,iBAAiB,CAAC,OAAe;QACrC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,yDAAyD,EAAE,WAAW;;0BACrF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;kCACtB,6LAAC;wBAAG,WAAU;kCAAoC;;;;;;;;;;;;0BAKpD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;wCACV,aAAY;wCACZ,KAAI;wCACJ,MAAK;;;;;;kDAEP,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAM;;;;;;;;;;;;;;;;;;;;;;;;kCAM1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAM;sCAEN,cAAA,6LAAC,iOAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAK9B,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,0BACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;;;;;;;;;mDAEf,sBACF,6LAAC;4CAAK,WAAU;sDAAwB;;;;;mDACtC,oBAAoB,qBACtB,6LAAC;4CAAK,WAAU;sDACb,eAAe,iBAAiB;;;;;iEAGnC,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAGpC,6LAAC;wCAAI,WAAU;kDACZ,eAAe,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;oBAM7C,gBAAgB,CAAC,uBAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAO;;;;;;oCAAwB;oCAAI;oCAAa;oCAAI,aAAa,OAAO,CAAC;oCAAG;oCAAE;;;;;;;4BAEhF,iCACC,6LAAC;gCAAI,WAAU;;oCACZ,eAAe,WAAW,SAAS;oCAAc;oCAAI,eAAe,iBAAiB;;;;;;;;;;;;;kCAO9F,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;;4CACX;4CACQ;;;;;;;kDAET,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;;4CACX;4CACQ;;;;;;;kDAET,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;;4CACX;4CACS;;;;;;;kDAEV,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;;4CACX;4CACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;GArLgB;KAAA", "debugId": null}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "file": "refresh-cw.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/refresh-cw.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8', key: 'v9h5vc' }],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n  ['path', { d: 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16', key: '3uifl3' }],\n  ['path', { d: 'M8 16H3v5', key: '1cv678' }],\n];\n\n/**\n * @component @name RefreshCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RefreshCw = createLucideIcon('refresh-cw', __iconNode);\n\nexport default RefreshCw;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAuD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACpF;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1015, "column": 0}, "map": {"version": 3, "file": "trending-up.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1061, "column": 0}, "map": {"version": 3, "file": "trending-down.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/trending-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 17h6v-6', key: 't6n2it' }],\n  ['path', { d: 'm22 17-8.5-8.5-5 5L2 7', key: 'x473p' }],\n];\n\n/**\n * @component @name TrendingDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMTdoNnYtNiIgLz4KICA8cGF0aCBkPSJtMjIgMTctOC41LTguNS01IDVMMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trending-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingDown = createLucideIcon('trending-down', __iconNode);\n\nexport default TrendingDown;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAS,CAAA;KAAA;CACxD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1107, "column": 0}, "map": {"version": 3, "file": "arrow-right-left.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/arrow-right-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm16 3 4 4-4 4', key: '1x1c3m' }],\n  ['path', { d: 'M20 7H4', key: 'zbl0bi' }],\n  ['path', { d: 'm8 21-4-4 4-4', key: 'h9nckh' }],\n  ['path', { d: 'M4 17h16', key: 'g4d7ey' }],\n];\n\n/**\n * @component @name ArrowRightLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTYgMyA0IDQtNCA0IiAvPgogIDxwYXRoIGQ9Ik0yMCA3SDQiIC8+CiAgPHBhdGggZD0ibTggMjEtNC00IDQtNCIgLz4KICA8cGF0aCBkPSJNNCAxN2gxNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-right-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowRightLeft = createLucideIcon('arrow-right-left', __iconNode);\n\nexport default ArrowRightLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAoB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1167, "column": 0}, "map": {"version": 3, "file": "calculator.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/calculator.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '16', height: '20', x: '4', y: '2', rx: '2', key: '1nb95v' }],\n  ['line', { x1: '8', x2: '16', y1: '6', y2: '6', key: 'x4nwl0' }],\n  ['line', { x1: '16', x2: '16', y1: '14', y2: '18', key: 'wjye3r' }],\n  ['path', { d: 'M16 10h.01', key: '1m94wz' }],\n  ['path', { d: 'M12 10h.01', key: '1nrarc' }],\n  ['path', { d: 'M8 10h.01', key: '19clt8' }],\n  ['path', { d: 'M12 14h.01', key: '1etili' }],\n  ['path', { d: 'M8 14h.01', key: '6423bh' }],\n  ['path', { d: 'M12 18h.01', key: 'mhygvu' }],\n  ['path', { d: 'M8 18h.01', key: 'lrp35t' }],\n];\n\n/**\n * @component @name Calculator\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMjAiIHg9IjQiIHk9IjIiIHJ4PSIyIiAvPgogIDxsaW5lIHgxPSI4IiB4Mj0iMTYiIHkxPSI2IiB5Mj0iNiIgLz4KICA8bGluZSB4MT0iMTYiIHgyPSIxNiIgeTE9IjE0IiB5Mj0iMTgiIC8+CiAgPHBhdGggZD0iTTE2IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTBoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTRoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDE0aC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMThoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDE4aC4wMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/calculator\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calculator = createLucideIcon('calculator', __iconNode);\n\nexport default Calculator;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}