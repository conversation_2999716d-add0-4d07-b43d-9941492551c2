'use client'

import { useShipments } from '@/hooks/useApi'
import { formatCurrency, formatDateTime } from '@/lib/utils'
import { Badge } from '@/components/ui/badge-component'
import { 
  Package, 
  Truck, 
  Plane,
  Clock, 
  CheckCircle, 
  XCircle,
  Eye,
  Edit,
  MapPin,
  Calendar,
  Loader2,
  AlertCircle
} from 'lucide-react'

const statusColors = {
  PENDING: 'bg-yellow-100 text-yellow-800',
  IN_TRANSIT: 'bg-blue-100 text-blue-800',
  DELIVERED: 'bg-green-100 text-green-800',
  CANCELLED: 'bg-red-100 text-red-800'
}

const statusLabels = {
  PENDING: 'En attente',
  IN_TRANSIT: 'En transit',
  DELIVERED: 'Livré',
  CANCELLED: 'Annulé'
}

const statusIcons = {
  PENDING: Clock,
  IN_TRANSIT: Truck,
  DELIVERED: CheckCircle,
  CANCELLED: XCircle
}

const transportModeColors = {
  TRUCK: 'bg-orange-100 text-orange-800',
  AIR: 'bg-sky-100 text-sky-800'
}

const transportModeLabels = {
  TRUCK: 'Routier',
  AIR: 'Aérien'
}

const transportModeIcons = {
  TRUCK: Truck,
  AIR: Plane
}

export function ShipmentsList() {
  const { data: shipments, loading, error, refetch } = useShipments()

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Chargement des expéditions...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-center py-12">
          <AlertCircle className="h-8 w-8 text-red-500" />
          <div className="ml-3">
            <p className="text-red-600 font-medium">Erreur de chargement</p>
            <p className="text-red-500 text-sm">{error}</p>
            <button 
              type="button"
              onClick={refetch}
              className="mt-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Réessayer
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (!shipments || shipments.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center py-12">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 text-lg">Aucune expédition trouvée</p>
          <p className="text-gray-400 text-sm mt-1">
            Commencez par créer votre première expédition
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Expédition
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Client
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Transport
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Statut
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Dates
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Coût
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {shipments.map((shipment) => {
              const StatusIcon = statusIcons[shipment.status]
              const TransportIcon = transportModeIcons[shipment.transportMode]
              
              return (
                <tr key={shipment.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                          <Package className="h-5 w-5 text-white" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {shipment.trackingNumber}
                        </div>
                        <div className="text-sm text-gray-500">
                          {shipment.origin} → {shipment.destination}
                        </div>
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {shipment.customer?.name || 'N/A'}
                    </div>
                    <div className="text-sm text-gray-500">
                      {shipment.customer?.city || ''}
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Badge className={transportModeColors[shipment.transportMode]}>
                        <TransportIcon className="h-3 w-3 mr-1" />
                        {transportModeLabels[shipment.transportMode]}
                      </Badge>
                    </div>
                    {shipment.carrier && (
                      <div className="text-xs text-gray-500 mt-1">
                        {shipment.carrier.name}
                      </div>
                    )}
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge className={statusColors[shipment.status]}>
                      <StatusIcon className="h-3 w-3 mr-1" />
                      {statusLabels[shipment.status]}
                    </Badge>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                      <div>
                        <div className="font-medium">
                          {formatDateTime(shipment.departureDate)}
                        </div>
                        {shipment.estimatedArrival && (
                          <div className="text-xs text-gray-500">
                            ETA: {formatDateTime(shipment.estimatedArrival)}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="font-medium">
                      {formatCurrency(shipment.totalCost, 'XOF')}
                    </div>
                    <div className="text-xs text-gray-500">
                      {shipment.weight}kg
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button 
                        type="button"
                        title="Voir les détails"
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button 
                        type="button"
                        title="Modifier"
                        className="text-gray-600 hover:text-gray-900"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>
    </div>
  )
}
