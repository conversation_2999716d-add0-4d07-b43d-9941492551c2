{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/currency/ExchangeRateWidget.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ExchangeRateWidget = registerClientReference(\n    function() { throw new Error(\"Attempted to call ExchangeRateWidget() from the server but ExchangeRateWidget is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/currency/ExchangeRateWidget.tsx <module evaluation>\",\n    \"ExchangeRateWidget\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,gFACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/currency/ExchangeRateWidget.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ExchangeRateWidget = registerClientReference(\n    function() { throw new Error(\"Attempted to call ExchangeRateWidget() from the server but ExchangeRateWidget is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/currency/ExchangeRateWidget.tsx\",\n    \"ExchangeRateWidget\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,4DACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/currency/CurrencyConverter.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CurrencyConverter = registerClientReference(\n    function() { throw new Error(\"Attempted to call CurrencyConverter() from the server but CurrencyConverter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/currency/CurrencyConverter.tsx <module evaluation>\",\n    \"CurrencyConverter\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,+EACA", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/currency/CurrencyConverter.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CurrencyConverter = registerClientReference(\n    function() { throw new Error(\"Attempted to call CurrencyConverter() from the server but CurrencyConverter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/currency/CurrencyConverter.tsx\",\n    \"CurrencyConverter\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,2DACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/currency.ts"], "sourcesContent": ["// Service de conversion de devises NGN/XOF\nexport interface ExchangeRate {\n  from: 'NGN' | 'XOF'\n  to: 'NGN' | 'XOF'\n  rate: number\n  lastUpdated: Date\n}\n\nexport interface CurrencyConversion {\n  amount: number\n  fromCurrency: 'NGN' | 'XOF'\n  toCurrency: 'NGN' | 'XOF'\n  convertedAmount: number\n  exchangeRate: number\n  timestamp: Date\n}\n\n// Cache des taux de change pour éviter trop d'appels API\nlet exchangeRateCache: {\n  NGN_TO_XOF: { rate: number; timestamp: Date } | null\n  XOF_TO_NGN: { rate: number; timestamp: Date } | null\n} = {\n  NGN_TO_XOF: null,\n  XOF_TO_NGN: null\n}\n\nconst CACHE_DURATION = 30 * 60 * 1000 // 30 minutes en millisecondes\n\n// Taux de change par défaut (fallback) basés sur les taux moyens récents\nconst DEFAULT_RATES = {\n  NGN_TO_XOF: 0.38, // 1 NGN ≈ 0.38 XOF\n  XOF_TO_NGN: 2.63   // 1 XOF ≈ 2.63 NGN\n}\n\n/**\n * Récupère les taux de change depuis l'API ExchangeRate-API\n */\nasync function fetchExchangeRates(): Promise<{ NGN_TO_XOF: number; XOF_TO_NGN: number }> {\n  try {\n    // Utilisation de l'API gratuite ExchangeRate-API\n    const response = await fetch('https://api.exchangerate-api.com/v4/latest/NGN', {\n      next: { revalidate: 1800 } // Cache pendant 30 minutes\n    })\n    \n    if (!response.ok) {\n      throw new Error(`API Error: ${response.status}`)\n    }\n    \n    const data = await response.json()\n    \n    // Vérifier si XOF est disponible dans les taux\n    if (!data.rates || !data.rates.XOF) {\n      throw new Error('XOF rate not available')\n    }\n    \n    const ngnToXof = data.rates.XOF\n    const xofToNgn = 1 / ngnToXof\n    \n    return {\n      NGN_TO_XOF: ngnToXof,\n      XOF_TO_NGN: xofToNgn\n    }\n  } catch (error) {\n    console.warn('Erreur lors de la récupération des taux de change:', error)\n    \n    // Fallback vers les taux par défaut\n    return {\n      NGN_TO_XOF: DEFAULT_RATES.NGN_TO_XOF,\n      XOF_TO_NGN: DEFAULT_RATES.XOF_TO_NGN\n    }\n  }\n}\n\n/**\n * Obtient le taux de change actuel avec mise en cache\n */\nexport async function getExchangeRate(from: 'NGN' | 'XOF', to: 'NGN' | 'XOF'): Promise<ExchangeRate> {\n  if (from === to) {\n    return {\n      from,\n      to,\n      rate: 1,\n      lastUpdated: new Date()\n    }\n  }\n  \n  const cacheKey = `${from}_TO_${to}` as keyof typeof exchangeRateCache\n  const cached = exchangeRateCache[cacheKey]\n  \n  // Vérifier si le cache est encore valide\n  if (cached && (Date.now() - cached.timestamp.getTime()) < CACHE_DURATION) {\n    return {\n      from,\n      to,\n      rate: cached.rate,\n      lastUpdated: cached.timestamp\n    }\n  }\n  \n  // Récupérer les nouveaux taux\n  try {\n    const rates = await fetchExchangeRates()\n    const now = new Date()\n    \n    // Mettre à jour le cache\n    exchangeRateCache.NGN_TO_XOF = {\n      rate: rates.NGN_TO_XOF,\n      timestamp: now\n    }\n    exchangeRateCache.XOF_TO_NGN = {\n      rate: rates.XOF_TO_NGN,\n      timestamp: now\n    }\n    \n    const rate = cacheKey === 'NGN_TO_XOF' ? rates.NGN_TO_XOF : rates.XOF_TO_NGN\n    \n    return {\n      from,\n      to,\n      rate,\n      lastUpdated: now\n    }\n  } catch (error) {\n    console.error('Erreur lors de la récupération du taux de change:', error)\n    \n    // Utiliser les taux par défaut\n    const rate = from === 'NGN' ? DEFAULT_RATES.NGN_TO_XOF : DEFAULT_RATES.XOF_TO_NGN\n    \n    return {\n      from,\n      to,\n      rate,\n      lastUpdated: new Date()\n    }\n  }\n}\n\n/**\n * Convertit un montant d'une devise à une autre\n */\nexport async function convertCurrency(\n  amount: number,\n  fromCurrency: 'NGN' | 'XOF',\n  toCurrency: 'NGN' | 'XOF'\n): Promise<CurrencyConversion> {\n  const exchangeRate = await getExchangeRate(fromCurrency, toCurrency)\n  const convertedAmount = amount * exchangeRate.rate\n  \n  return {\n    amount,\n    fromCurrency,\n    toCurrency,\n    convertedAmount: Math.round(convertedAmount * 100) / 100, // Arrondir à 2 décimales\n    exchangeRate: exchangeRate.rate,\n    timestamp: new Date()\n  }\n}\n\n/**\n * Formate un montant avec la devise appropriée\n */\nexport function formatCurrency(amount: number, currency: 'NGN' | 'XOF'): string {\n  const formatter = new Intl.NumberFormat('fr-FR', {\n    style: 'currency',\n    currency: currency,\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0\n  })\n  \n  return formatter.format(amount)\n}\n\n/**\n * Affiche un montant dans les deux devises\n */\nexport async function formatDualCurrency(\n  amount: number,\n  baseCurrency: 'NGN' | 'XOF'\n): Promise<string> {\n  const otherCurrency = baseCurrency === 'NGN' ? 'XOF' : 'NGN'\n  const conversion = await convertCurrency(amount, baseCurrency, otherCurrency)\n  \n  const baseFormatted = formatCurrency(amount, baseCurrency)\n  const convertedFormatted = formatCurrency(conversion.convertedAmount, otherCurrency)\n  \n  return `${baseFormatted} (≈ ${convertedFormatted})`\n}\n\n/**\n * Obtient les taux de change actuels pour l'affichage\n */\nexport async function getCurrentRates(): Promise<{\n  ngnToXof: number\n  xofToNgn: number\n  lastUpdated: Date\n}> {\n  const ngnRate = await getExchangeRate('NGN', 'XOF')\n  const xofRate = await getExchangeRate('XOF', 'NGN')\n  \n  return {\n    ngnToXof: ngnRate.rate,\n    xofToNgn: xofRate.rate,\n    lastUpdated: ngnRate.lastUpdated\n  }\n}\n\n/**\n * Vide le cache des taux de change (utile pour forcer une mise à jour)\n */\nexport function clearExchangeRateCache(): void {\n  exchangeRateCache = {\n    NGN_TO_XOF: null,\n    XOF_TO_NGN: null\n  }\n}\n\n/**\n * Calcule la marge en tenant compte de la conversion de devise\n */\nexport async function calculateMarginWithCurrency(\n  costPrice: number,\n  costCurrency: 'NGN' | 'XOF',\n  sellingPrice: number,\n  sellingCurrency: 'NGN' | 'XOF'\n): Promise<{\n  marginAmount: number\n  marginPercentage: number\n  currency: 'NGN' | 'XOF'\n}> {\n  let normalizedCostPrice = costPrice\n  let normalizedSellingPrice = sellingPrice\n  let resultCurrency = sellingCurrency\n  \n  // Convertir le prix de coût dans la même devise que le prix de vente\n  if (costCurrency !== sellingCurrency) {\n    const conversion = await convertCurrency(costPrice, costCurrency, sellingCurrency)\n    normalizedCostPrice = conversion.convertedAmount\n  }\n  \n  const marginAmount = normalizedSellingPrice - normalizedCostPrice\n  const marginPercentage = normalizedCostPrice > 0 \n    ? (marginAmount / normalizedCostPrice) * 100 \n    : 0\n  \n  return {\n    marginAmount: Math.round(marginAmount * 100) / 100,\n    marginPercentage: Math.round(marginPercentage * 100) / 100,\n    currency: resultCurrency\n  }\n}\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;;;;;;;AAiB3C,yDAAyD;AACzD,IAAI,oBAGA;IACF,YAAY;IACZ,YAAY;AACd;AAEA,MAAM,iBAAiB,KAAK,KAAK,KAAK,8BAA8B;;AAEpE,yEAAyE;AACzE,MAAM,gBAAgB;IACpB,YAAY;IACZ,YAAY,KAAO,mBAAmB;AACxC;AAEA;;CAEC,GACD,eAAe;IACb,IAAI;QACF,iDAAiD;QACjD,MAAM,WAAW,MAAM,MAAM,kDAAkD;YAC7E,MAAM;gBAAE,YAAY;YAAK,EAAE,2BAA2B;QACxD;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,EAAE;QACjD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,+CAA+C;QAC/C,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,GAAG,EAAE;YAClC,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,KAAK,KAAK,CAAC,GAAG;QAC/B,MAAM,WAAW,IAAI;QAErB,OAAO;YACL,YAAY;YACZ,YAAY;QACd;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,sDAAsD;QAEnE,oCAAoC;QACpC,OAAO;YACL,YAAY,cAAc,UAAU;YACpC,YAAY,cAAc,UAAU;QACtC;IACF;AACF;AAKO,eAAe,gBAAgB,IAAmB,EAAE,EAAiB;IAC1E,IAAI,SAAS,IAAI;QACf,OAAO;YACL;YACA;YACA,MAAM;YACN,aAAa,IAAI;QACnB;IACF;IAEA,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE,IAAI;IACnC,MAAM,SAAS,iBAAiB,CAAC,SAAS;IAE1C,yCAAyC;IACzC,IAAI,UAAU,AAAC,KAAK,GAAG,KAAK,OAAO,SAAS,CAAC,OAAO,KAAM,gBAAgB;QACxE,OAAO;YACL;YACA;YACA,MAAM,OAAO,IAAI;YACjB,aAAa,OAAO,SAAS;QAC/B;IACF;IAEA,8BAA8B;IAC9B,IAAI;QACF,MAAM,QAAQ,MAAM;QACpB,MAAM,MAAM,IAAI;QAEhB,yBAAyB;QACzB,kBAAkB,UAAU,GAAG;YAC7B,MAAM,MAAM,UAAU;YACtB,WAAW;QACb;QACA,kBAAkB,UAAU,GAAG;YAC7B,MAAM,MAAM,UAAU;YACtB,WAAW;QACb;QAEA,MAAM,OAAO,aAAa,eAAe,MAAM,UAAU,GAAG,MAAM,UAAU;QAE5E,OAAO;YACL;YACA;YACA;YACA,aAAa;QACf;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QAEnE,+BAA+B;QAC/B,MAAM,OAAO,SAAS,QAAQ,cAAc,UAAU,GAAG,cAAc,UAAU;QAEjF,OAAO;YACL;YACA;YACA;YACA,aAAa,IAAI;QACnB;IACF;AACF;AAKO,eAAe,gBACpB,MAAc,EACd,YAA2B,EAC3B,UAAyB;IAEzB,MAAM,eAAe,MAAM,gBAAgB,cAAc;IACzD,MAAM,kBAAkB,SAAS,aAAa,IAAI;IAElD,OAAO;QACL;QACA;QACA;QACA,iBAAiB,KAAK,KAAK,CAAC,kBAAkB,OAAO;QACrD,cAAc,aAAa,IAAI;QAC/B,WAAW,IAAI;IACjB;AACF;AAKO,SAAS,eAAe,MAAc,EAAE,QAAuB;IACpE,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,SAAS;QAC/C,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB;IAEA,OAAO,UAAU,MAAM,CAAC;AAC1B;AAKO,eAAe,mBACpB,MAAc,EACd,YAA2B;IAE3B,MAAM,gBAAgB,iBAAiB,QAAQ,QAAQ;IACvD,MAAM,aAAa,MAAM,gBAAgB,QAAQ,cAAc;IAE/D,MAAM,gBAAgB,eAAe,QAAQ;IAC7C,MAAM,qBAAqB,eAAe,WAAW,eAAe,EAAE;IAEtE,OAAO,GAAG,cAAc,IAAI,EAAE,mBAAmB,CAAC,CAAC;AACrD;AAKO,eAAe;IAKpB,MAAM,UAAU,MAAM,gBAAgB,OAAO;IAC7C,MAAM,UAAU,MAAM,gBAAgB,OAAO;IAE7C,OAAO;QACL,UAAU,QAAQ,IAAI;QACtB,UAAU,QAAQ,IAAI;QACtB,aAAa,QAAQ,WAAW;IAClC;AACF;AAKO,SAAS;IACd,oBAAoB;QAClB,YAAY;QACZ,YAAY;IACd;AACF;AAKO,eAAe,4BACpB,SAAiB,EACjB,YAA2B,EAC3B,YAAoB,EACpB,eAA8B;IAM9B,IAAI,sBAAsB;IAC1B,IAAI,yBAAyB;IAC7B,IAAI,iBAAiB;IAErB,qEAAqE;IACrE,IAAI,iBAAiB,iBAAiB;QACpC,MAAM,aAAa,MAAM,gBAAgB,WAAW,cAAc;QAClE,sBAAsB,WAAW,eAAe;IAClD;IAEA,MAAM,eAAe,yBAAyB;IAC9C,MAAM,mBAAmB,sBAAsB,IAC3C,AAAC,eAAe,sBAAuB,MACvC;IAEJ,OAAO;QACL,cAAc,KAAK,KAAK,CAAC,eAAe,OAAO;QAC/C,kBAAkB,KAAK,KAAK,CAAC,mBAAmB,OAAO;QACvD,UAAU;IACZ;AACF", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/currency/page.tsx"], "sourcesContent": ["import { Suspense } from 'react'\nimport { ExchangeRateWidget } from '@/components/currency/ExchangeRateWidget'\nimport { CurrencyConverter } from '@/components/currency/CurrencyConverter'\nimport { getCurrentRates } from '@/lib/currency'\nimport { TrendingUp, Calculator, Globe, Clock, AlertCircle } from 'lucide-react'\n\nexport default async function CurrencyPage() {\n  let rates = null\n  let error = null\n\n  try {\n    rates = await getCurrentRates()\n  } catch (err) {\n    error = 'Impossible de charger les taux de change'\n    console.error('Erreur chargement taux:', err)\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* En-tête */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 flex items-center\">\n                <Globe className=\"h-6 w-6 mr-3 text-blue-600\" />\n                Gestion des devises\n              </h1>\n              <p className=\"text-gray-600 mt-1\">\n                Conversion automatique NGN/XOF et suivi des taux de change\n              </p>\n            </div>\n            {rates && (\n              <div className=\"text-right\">\n                <div className=\"text-sm text-gray-500 flex items-center\">\n                  <Clock className=\"h-4 w-4 mr-1\" />\n                  Dernière mise à jour: {new Intl.DateTimeFormat('fr-FR', {\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    day: '2-digit',\n                    month: '2-digit'\n                  }).format(rates.lastUpdated)}\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <AlertCircle className=\"h-5 w-5 text-red-600 mr-2\" />\n            <span className=\"text-red-800\">{error}</span>\n          </div>\n        </div>\n      )}\n\n      {/* Statistiques rapides */}\n      {rates && (\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <TrendingUp className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">NGN → XOF</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {rates.ngnToXof.toFixed(4)}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <TrendingUp className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">XOF → NGN</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {rates.xofToNgn.toFixed(4)}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <Calculator className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Exemple</p>\n                <p className=\"text-lg font-bold text-gray-900\">\n                  1000 NGN = {(1000 * rates.ngnToXof).toFixed(0)} XOF\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Contenu principal */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Convertisseur */}\n        <div className=\"lg:col-span-2\">\n          <Suspense fallback={\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <div className=\"animate-pulse space-y-4\">\n                <div className=\"h-6 bg-gray-200 rounded w-1/3\"></div>\n                <div className=\"h-10 bg-gray-200 rounded\"></div>\n                <div className=\"h-10 bg-gray-200 rounded\"></div>\n              </div>\n            </div>\n          }>\n            <CurrencyConverter />\n          </Suspense>\n        </div>\n\n        {/* Widget taux de change */}\n        <div>\n          <Suspense fallback={\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4\">\n              <div className=\"animate-pulse space-y-3\">\n                <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n                <div className=\"h-6 bg-gray-200 rounded\"></div>\n                <div className=\"h-6 bg-gray-200 rounded\"></div>\n              </div>\n            </div>\n          }>\n            <ExchangeRateWidget />\n          </Suspense>\n        </div>\n      </div>\n\n      {/* Informations utiles */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n          Informations sur les devises\n        </h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <h4 className=\"font-medium text-gray-900 mb-2\">Naira Nigérian (NGN)</h4>\n            <ul className=\"text-sm text-gray-600 space-y-1\">\n              <li>• Devise officielle du Nigeria</li>\n              <li>• Code ISO: NGN</li>\n              <li>• Symbole: ₦</li>\n              <li>• Subdivisions: 100 kobo</li>\n            </ul>\n          </div>\n          \n          <div>\n            <h4 className=\"font-medium text-gray-900 mb-2\">Franc CFA (XOF)</h4>\n            <ul className=\"text-sm text-gray-600 space-y-1\">\n              <li>• Devise de l'Union Économique et Monétaire Ouest Africaine</li>\n              <li>• Code ISO: XOF</li>\n              <li>• Utilisé au Sénégal, Mali, Burkina Faso, etc.</li>\n              <li>• Subdivisions: 100 centimes</li>\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n          <h4 className=\"font-medium text-blue-900 mb-2\">💡 Conseils d'utilisation</h4>\n          <ul className=\"text-sm text-blue-800 space-y-1\">\n            <li>• Les taux sont mis à jour automatiquement toutes les 30 minutes</li>\n            <li>• En cas d'indisponibilité de l'API, des taux de secours sont utilisés</li>\n            <li>• Utilisez le convertisseur pour calculer vos marges et prix de vente</li>\n            <li>• Les conversions sont arrondies à 2 décimales pour plus de précision</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;;AAEe,eAAe;IAC5B,IAAI,QAAQ;IACZ,IAAI,QAAQ;IAEZ,IAAI;QACF,QAAQ,MAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD;IAC9B,EAAE,OAAO,KAAK;QACZ,QAAQ;QACR,QAAQ,KAAK,CAAC,2BAA2B;IAC3C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAA+B;;;;;;;kDAGlD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;4BAInC,uBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;wCACX,IAAI,KAAK,cAAc,CAAC,SAAS;4CACtD,MAAM;4CACN,QAAQ;4CACR,KAAK;4CACL,OAAO;wCACT,GAAG,MAAM,CAAC,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQtC,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;;;;;;YAMrC,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDACV,MAAM,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;kCAMhC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDACV,MAAM,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;kCAMhC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;;gDAAkC;gDACjC,CAAC,OAAO,MAAM,QAAQ,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3D,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,qMAAA,CAAA,WAAQ;4BAAC,wBACR,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;sCAInB,cAAA,8OAAC,mJAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;kCAKtB,8OAAC;kCACC,cAAA,8OAAC,qMAAA,CAAA,WAAQ;4BAAC,wBACR,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;sCAInB,cAAA,8OAAC,oJAAA,CAAA,qBAAkB;;;;;;;;;;;;;;;;;;;;;0BAMzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAIvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAKV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}, {"offset": {"line": 945, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,MAAM,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA;YAC5D,OAAA,CAAA,CAAA,CAAA,CAAA;QAAA;IACT;AAEJ,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 981, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,cAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACX,CACE,EACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAEL,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,+MAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,uKAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA;QAC/D,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,EACA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;WACvD,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA;YAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAAA;KAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1048, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,yMACjF,gBAAA,yJAAc,UAAM,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,+KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,+KAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,EAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,EAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACJ,CAAA;IAGO,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gLAAc,eAAA,EAAa,QAAQ,CAAA;IAEtC,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1080, "column": 0}, "map": {"version": 3, "file": "trending-up.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1126, "column": 0}, "map": {"version": 3, "file": "calculator.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/calculator.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '16', height: '20', x: '4', y: '2', rx: '2', key: '1nb95v' }],\n  ['line', { x1: '8', x2: '16', y1: '6', y2: '6', key: 'x4nwl0' }],\n  ['line', { x1: '16', x2: '16', y1: '14', y2: '18', key: 'wjye3r' }],\n  ['path', { d: 'M16 10h.01', key: '1m94wz' }],\n  ['path', { d: 'M12 10h.01', key: '1nrarc' }],\n  ['path', { d: 'M8 10h.01', key: '19clt8' }],\n  ['path', { d: 'M12 14h.01', key: '1etili' }],\n  ['path', { d: 'M8 14h.01', key: '6423bh' }],\n  ['path', { d: 'M12 18h.01', key: 'mhygvu' }],\n  ['path', { d: 'M8 18h.01', key: 'lrp35t' }],\n];\n\n/**\n * @component @name Calculator\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMjAiIHg9IjQiIHk9IjIiIHJ4PSIyIiAvPgogIDxsaW5lIHgxPSI4IiB4Mj0iMTYiIHkxPSI2IiB5Mj0iNiIgLz4KICA8bGluZSB4MT0iMTYiIHgyPSIxNiIgeTE9IjE0IiB5Mj0iMTgiIC8+CiAgPHBhdGggZD0iTTE2IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTBoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTRoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDE0aC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMThoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDE4aC4wMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/calculator\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calculator = createLucideIcon('calculator', __iconNode);\n\nexport default Calculator;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1238, "column": 0}, "map": {"version": 3, "file": "globe.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/globe.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20', key: '13o1zl' }],\n  ['path', { d: 'M2 12h20', key: '9i4pu4' }],\n];\n\n/**\n * @component @name Globe\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNMTIgMmExNC41IDE0LjUgMCAwIDAgMCAyMCAxNC41IDE0LjUgMCAwIDAgMC0yMCIgLz4KICA8cGF0aCBkPSJNMiAxMmgyMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/globe\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Globe = createLucideIcon('globe', __iconNode);\n\nexport default Globe;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChF;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1293, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 6v6l4 2', key: 'mmk7yg' }],\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNnY2bDQgMiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC3D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1341, "column": 0}, "map": {"version": 3, "file": "circle-alert.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('circle-alert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,MAAA,CAAQ;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,IAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACvE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1439, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,GAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}