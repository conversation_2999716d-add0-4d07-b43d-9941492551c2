{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: 'NGN' | 'XOF' = 'XOF'): string {\n  const symbols = {\n    NGN: '₦',\n    XOF: 'CFA'\n  }\n  \n  return `${amount.toLocaleString('fr-FR')} ${symbols[currency]}`\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric'\n  }).format(date)\n}\n\nexport function formatDateTime(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  }).format(date)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAA0B,KAAK;IAC5E,MAAM,UAAU;QACd,KAAK;QACL,KAAK;IACP;IAEA,OAAO,GAAG,OAAO,cAAc,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,SAAS,EAAE;AACjE;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;IACR,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAU;IACvC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'\n  size?: 'default' | 'sm' | 'lg' | 'icon'\n}\n\nexport function Button({ \n  className, \n  variant = 'default', \n  size = 'default', \n  ...props \n}: ButtonProps) {\n  const variantClasses = {\n    default: 'bg-blue-600 text-white hover:bg-blue-700',\n    destructive: 'bg-red-600 text-white hover:bg-red-700',\n    outline: 'border border-gray-300 bg-transparent hover:bg-gray-50',\n    secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200',\n    ghost: 'hover:bg-gray-100',\n    link: 'text-blue-600 underline-offset-4 hover:underline'\n  }\n\n  const sizeClasses = {\n    default: 'h-10 px-4 py-2',\n    sm: 'h-9 rounded-md px-3',\n    lg: 'h-11 rounded-md px-8',\n    icon: 'h-10 w-10'\n  }\n\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none',\n        variantClasses[variant],\n        sizeClasses[size],\n        className\n      )}\n      {...props}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,GAAG,OACS;IACZ,MAAM,iBAAiB;QACrB,SAAS;QACT,aAAa;QACb,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iPACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/badge-component.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {\n  children: React.ReactNode\n  className?: string\n  variant?: 'default' | 'secondary' | 'destructive' | 'outline'\n}\n\nexport function Badge({ children, className, variant = 'default', ...props }: BadgeProps) {\n  const variantClasses = {\n    default: 'bg-blue-100 text-blue-800 border-blue-200',\n    secondary: 'bg-gray-100 text-gray-800 border-gray-200',\n    destructive: 'bg-red-100 text-red-800 border-red-200',\n    outline: 'bg-transparent text-gray-700 border-gray-300'\n  }\n\n  return (\n    <span \n      className={cn(\n        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border',\n        variantClasses[variant],\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAmB;IACtF,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,aAAa;QACb,SAAS;IACX;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kFACA,cAAc,CAAC,QAAQ,EACvB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/tabs.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Tabs = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx <module evaluation>\",\n    \"Tabs\",\n);\nexport const TabsContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsContent() from the server but TabsContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx <module evaluation>\",\n    \"TabsContent\",\n);\nexport const TabsList = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsList() from the server but <PERSON><PERSON>List is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx <module evaluation>\",\n    \"TabsList\",\n);\nexport const TabsTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsTrigger() from the server but TabsTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx <module evaluation>\",\n    \"TabsTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,4DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,4DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4DACA", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/tabs.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Tabs = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx\",\n    \"Tabs\",\n);\nexport const TabsContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsContent() from the server but TabsContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx\",\n    \"TabsContent\",\n);\nexport const TabsList = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsList() from the server but TabsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx\",\n    \"TabsList\",\n);\nexport const TabsTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsTrigger() from the server but TabsTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx\",\n    \"TabsTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,wCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,wCACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,wCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,wCACA", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/progress.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {\n  value?: number\n  max?: number\n  className?: string\n}\n\nexport function Progress({ value = 0, max = 100, className, ...props }: ProgressProps) {\n  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)\n\n  return (\n    <div\n      className={cn(\n        \"relative h-4 w-full overflow-hidden rounded-full bg-gray-200\",\n        className\n      )}\n      {...props}\n    >\n      <div\n        className=\"h-full bg-blue-600 transition-all duration-300 ease-in-out\"\n        style={{ width: `${percentage}%` }}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,SAAS,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,EAAE,SAAS,EAAE,GAAG,OAAsB;IACnF,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,AAAC,QAAQ,MAAO,KAAK,IAAI;IAE9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC;YACC,WAAU;YACV,OAAO;gBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;YAAC;;;;;;;;;;;AAIzC", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/commerce-client/page.tsx"], "sourcesContent": ["import { Suspense } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge-component'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Progress } from '@/components/ui/progress'\nimport { \n  ShoppingCart, \n  Package, \n  Truck, \n  TrendingUp,\n  DollarSign,\n  Star,\n  Plus,\n  Eye,\n  Calendar,\n  MapPin,\n  Weight,\n  Clock\n} from 'lucide-react'\n\n// Données de démonstration pour client commerce\nconst commerceStats = {\n  totalOrders: 18,\n  totalValue: 4250000,\n  averageOrderValue: 236111,\n  pendingOrders: 3,\n  inTransitOrders: 2,\n  deliveredOrders: 13,\n  averageDeliveryTime: 6.2,\n  customerSatisfaction: 4.7\n}\n\nconst recentOrders = [\n  {\n    id: 'CMD-2024-015',\n    products: [\n      { name: 'Tissus Wax Premium', quantity: 50, unit: 'yards' },\n      { name: 'Cosmétiques Nivea', quantity: 24, unit: 'pcs' }\n    ],\n    totalValue: 385000,\n    status: 'DELIVERED',\n    orderDate: '2024-01-08',\n    deliveryDate: '2024-01-14',\n    origin: 'Lagos',\n    weight: 45.5,\n    rating: 5\n  },\n  {\n    id: 'CMD-2024-016',\n    products: [\n      { name: 'Mèches Brésiliennes', quantity: 100, unit: 'pcs' },\n      { name: 'Produits capillaires', quantity: 36, unit: 'pcs' }\n    ],\n    totalValue: 520000,\n    status: 'IN_TRANSIT',\n    orderDate: '2024-01-10',\n    estimatedDelivery: '2024-01-16',\n    origin: 'Kano',\n    weight: 28.0,\n    trackingProgress: 75\n  },\n  {\n    id: 'CMD-2024-017',\n    products: [\n      { name: 'Tissus Ankara', quantity: 80, unit: 'yards' },\n      { name: 'Accessoires mode', quantity: 45, unit: 'pcs' }\n    ],\n    totalValue: 295000,\n    status: 'PENDING',\n    orderDate: '2024-01-12',\n    estimatedDelivery: '2024-01-19',\n    origin: 'Abuja',\n    weight: 35.2\n  }\n]\n\nconst productCategories = [\n  {\n    name: 'Tissus',\n    orders: 8,\n    value: 1850000,\n    growth: '+12%',\n    icon: '🧵'\n  },\n  {\n    name: 'Cosmétiques',\n    orders: 6,\n    value: 1420000,\n    growth: '+8%',\n    icon: '💄'\n  },\n  {\n    name: 'Mèches',\n    orders: 4,\n    value: 980000,\n    growth: '+15%',\n    icon: '💇‍♀️'\n  }\n]\n\nexport default function CommerceClientPage() {\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      PENDING: { label: 'En préparation', color: 'bg-yellow-100 text-yellow-800' },\n      IN_TRANSIT: { label: 'En transit', color: 'bg-blue-100 text-blue-800' },\n      DELIVERED: { label: 'Livré', color: 'bg-green-100 text-green-800' },\n      CANCELLED: { label: 'Annulé', color: 'bg-red-100 text-red-800' }\n    }\n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING\n    return <Badge className={config.color}>{config.label}</Badge>\n  }\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'XOF',\n      minimumFractionDigits: 0\n    }).format(amount)\n  }\n\n  const renderStars = (rating: number) => {\n    return Array.from({ length: 5 }, (_, i) => (\n      <Star\n        key={i}\n        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}\n      />\n    ))\n  }\n\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* En-tête */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            Espace Client Commerce\n          </h1>\n          <p className=\"text-gray-600\">\n            Gérez vos achats et suivez vos livraisons \"rendu Dakar\"\n          </p>\n        </div>\n        <Button className=\"flex items-center\">\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Nouvelle commande\n        </Button>\n      </div>\n\n      {/* Statistiques principales */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <ShoppingCart className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total commandes</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{commerceStats.totalOrders}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <DollarSign className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Valeur totale</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {formatCurrency(commerceStats.totalValue)}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <TrendingUp className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Panier moyen</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {formatCurrency(commerceStats.averageOrderValue)}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-yellow-100 rounded-lg\">\n                <Star className=\"h-6 w-6 text-yellow-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Satisfaction</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {commerceStats.customerSatisfaction}/5\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Répartition des commandes */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <Card>\n          <CardHeader>\n            <CardTitle>Statut des commandes</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-sm text-gray-600\">Livrées</span>\n                <span className=\"font-bold text-green-600\">{commerceStats.deliveredOrders}</span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-sm text-gray-600\">En transit</span>\n                <span className=\"font-bold text-blue-600\">{commerceStats.inTransitOrders}</span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-sm text-gray-600\">En préparation</span>\n                <span className=\"font-bold text-yellow-600\">{commerceStats.pendingOrders}</span>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Performance livraison</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-blue-600 mb-2\">\n                {commerceStats.averageDeliveryTime} jours\n              </div>\n              <p className=\"text-sm text-gray-600\">Délai moyen de livraison</p>\n              <div className=\"mt-4\">\n                <Progress value={85} className=\"h-2\" />\n                <p className=\"text-xs text-gray-500 mt-1\">85% dans les délais</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Catégories populaires</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              {productCategories.map((category) => (\n                <div key={category.name} className=\"flex justify-between items-center\">\n                  <div className=\"flex items-center\">\n                    <span className=\"text-lg mr-2\">{category.icon}</span>\n                    <span className=\"text-sm font-medium\">{category.name}</span>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-sm font-bold\">{formatCurrency(category.value)}</div>\n                    <div className=\"text-xs text-green-600\">{category.growth}</div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Contenu principal */}\n      <Tabs defaultValue=\"orders\" className=\"space-y-6\">\n        <TabsList>\n          <TabsTrigger value=\"orders\">Mes commandes</TabsTrigger>\n          <TabsTrigger value=\"catalog\">Catalogue</TabsTrigger>\n          <TabsTrigger value=\"tracking\">Suivi</TabsTrigger>\n          <TabsTrigger value=\"invoices\">Factures</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"orders\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Commandes récentes</CardTitle>\n              <CardDescription>\n                Historique de vos achats avec transport intégré\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {recentOrders.map((order) => (\n                  <div key={order.id} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex justify-between items-start mb-4\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-3 mb-2\">\n                          <h3 className=\"font-medium text-gray-900\">{order.id}</h3>\n                          {getStatusBadge(order.status)}\n                          {order.rating && (\n                            <div className=\"flex items-center\">\n                              {renderStars(order.rating)}\n                            </div>\n                          )}\n                        </div>\n                        <div className=\"grid grid-cols-2 gap-4 text-sm text-gray-600 mb-3\">\n                          <div className=\"flex items-center\">\n                            <Calendar className=\"h-4 w-4 mr-1\" />\n                            Commandé: {new Date(order.orderDate).toLocaleDateString('fr-FR')}\n                          </div>\n                          <div className=\"flex items-center\">\n                            <MapPin className=\"h-4 w-4 mr-1\" />\n                            {order.origin} → Dakar\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Weight className=\"h-4 w-4 mr-1\" />\n                            {order.weight} kg\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Clock className=\"h-4 w-4 mr-1\" />\n                            {order.status === 'DELIVERED' \n                              ? `Livré: ${new Date(order.deliveryDate!).toLocaleDateString('fr-FR')}`\n                              : `Estimé: ${new Date(order.estimatedDelivery).toLocaleDateString('fr-FR')}`\n                            }\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"font-bold text-lg text-gray-900\">\n                          {formatCurrency(order.totalValue)}\n                        </div>\n                        <Button variant=\"outline\" size=\"sm\" className=\"mt-2\">\n                          <Eye className=\"h-4 w-4 mr-1\" />\n                          Détails\n                        </Button>\n                      </div>\n                    </div>\n\n                    {/* Produits de la commande */}\n                    <div className=\"bg-gray-50 rounded-lg p-3\">\n                      <h4 className=\"font-medium text-sm text-gray-900 mb-2\">Produits commandés</h4>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2\">\n                        {order.products.map((product, index) => (\n                          <div key={index} className=\"flex justify-between text-sm\">\n                            <span className=\"text-gray-600\">{product.name}</span>\n                            <span className=\"font-medium\">{product.quantity} {product.unit}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n\n                    {/* Barre de progression pour les commandes en transit */}\n                    {order.status === 'IN_TRANSIT' && order.trackingProgress && (\n                      <div className=\"mt-3\">\n                        <div className=\"flex justify-between text-sm text-gray-600 mb-1\">\n                          <span>Progression de la livraison</span>\n                          <span>{order.trackingProgress}%</span>\n                        </div>\n                        <Progress value={order.trackingProgress} className=\"h-2\" />\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"catalog\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Catalogue produits</CardTitle>\n              <CardDescription>\n                Parcourez notre sélection de produits avec prix \"rendu Dakar\"\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-center py-8\">\n                <ShoppingCart className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" />\n                <p className=\"text-gray-500 mb-4\">\n                  Catalogue en cours de développement\n                </p>\n                <Button>\n                  Accéder au catalogue\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"tracking\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Suivi des livraisons</CardTitle>\n              <CardDescription>\n                Suivez vos commandes en temps réel\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-center py-8\">\n                <Truck className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" />\n                <p className=\"text-gray-500 mb-4\">\n                  {commerceStats.inTransitOrders} commande{commerceStats.inTransitOrders > 1 ? 's' : ''} en cours de livraison\n                </p>\n                <Button variant=\"outline\">\n                  Voir le suivi détaillé\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"invoices\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Factures et paiements</CardTitle>\n              <CardDescription>\n                Consultez vos factures et l'historique des paiements\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-center py-8\">\n                <DollarSign className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" />\n                <p className=\"text-gray-500 mb-4\">\n                  Gestion des factures en cours de développement\n                </p>\n                <Button variant=\"outline\">\n                  Voir toutes les factures\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  )\n}\n\nexport const metadata = {\n  title: 'Espace Client Commerce - Bi-Express',\n  description: 'Dashboard client pour la gestion des achats avec transport intégré'\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;AAeA,gDAAgD;AAChD,MAAM,gBAAgB;IACpB,aAAa;IACb,YAAY;IACZ,mBAAmB;IACnB,eAAe;IACf,iBAAiB;IACjB,iBAAiB;IACjB,qBAAqB;IACrB,sBAAsB;AACxB;AAEA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,UAAU;YACR;gBAAE,MAAM;gBAAsB,UAAU;gBAAI,MAAM;YAAQ;YAC1D;gBAAE,MAAM;gBAAqB,UAAU;gBAAI,MAAM;YAAM;SACxD;QACD,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,cAAc;QACd,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,UAAU;YACR;gBAAE,MAAM;gBAAuB,UAAU;gBAAK,MAAM;YAAM;YAC1D;gBAAE,MAAM;gBAAwB,UAAU;gBAAI,MAAM;YAAM;SAC3D;QACD,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,mBAAmB;QACnB,QAAQ;QACR,QAAQ;QACR,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,UAAU;YACR;gBAAE,MAAM;gBAAiB,UAAU;gBAAI,MAAM;YAAQ;YACrD;gBAAE,MAAM;gBAAoB,UAAU;gBAAI,MAAM;YAAM;SACvD;QACD,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,mBAAmB;QACnB,QAAQ;QACR,QAAQ;IACV;CACD;AAED,MAAM,oBAAoB;IACxB;QACE,MAAM;QACN,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,MAAM;IACR;IACA;QACE,MAAM;QACN,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,MAAM;IACR;IACA;QACE,MAAM;QACN,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,MAAM;IACR;CACD;AAEc,SAAS;IACtB,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,SAAS;gBAAE,OAAO;gBAAkB,OAAO;YAAgC;YAC3E,YAAY;gBAAE,OAAO;gBAAc,OAAO;YAA4B;YACtE,WAAW;gBAAE,OAAO;gBAAS,OAAO;YAA8B;YAClE,WAAW;gBAAE,OAAO;gBAAU,OAAO;YAA0B;QACjE;QACA,MAAM,SAAS,YAAY,CAAC,OAAoC,IAAI,aAAa,OAAO;QACxF,qBAAO,8OAAC,8IAAA,CAAA,QAAK;YAAC,WAAW,OAAO,KAAK;sBAAG,OAAO,KAAK;;;;;;IACtD;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,kBACnC,8OAAC,kMAAA,CAAA,OAAI;gBAEH,WAAW,CAAC,QAAQ,EAAE,IAAI,SAAS,iCAAiC,iBAAiB;eADhF;;;;;IAIX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAI/B,8OAAC,kIAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAoC,cAAc,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMlF,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,eAAe,cAAc,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlD,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,eAAe,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOzD,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;oDACV,cAAc,oBAAoB;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,8OAAC;oDAAK,WAAU;8DAA4B,cAAc,eAAe;;;;;;;;;;;;sDAE3E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,8OAAC;oDAAK,WAAU;8DAA2B,cAAc,eAAe;;;;;;;;;;;;sDAE1E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,8OAAC;oDAAK,WAAU;8DAA6B,cAAc,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMhF,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,mBAAmB;gDAAC;;;;;;;sDAErC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,OAAO;oDAAI,WAAU;;;;;;8DAC/B,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMlD,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;4CAAwB,WAAU;;8DACjC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB,SAAS,IAAI;;;;;;sEAC7C,8OAAC;4DAAK,WAAU;sEAAuB,SAAS,IAAI;;;;;;;;;;;;8DAEtD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAqB,eAAe,SAAS,KAAK;;;;;;sEACjE,8OAAC;4DAAI,WAAU;sEAA0B,SAAS,MAAM;;;;;;;;;;;;;2CAPlD,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiBjC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAS,WAAU;;kCACpC,8OAAC,gIAAA,CAAA,WAAQ;;0CACP,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAS;;;;;;0CAC5B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;0CAC7B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;0CAC9B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;;;;;;;kCAGhC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;gDAAmB,WAAU;;kEAC5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAG,WAAU;0FAA6B,MAAM,EAAE;;;;;;4EAClD,eAAe,MAAM,MAAM;4EAC3B,MAAM,MAAM,kBACX,8OAAC;gFAAI,WAAU;0FACZ,YAAY,MAAM,MAAM;;;;;;;;;;;;kFAI/B,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,0MAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;oFAAiB;oFAC1B,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB,CAAC;;;;;;;0FAE1D,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,0MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFACjB,MAAM,MAAM;oFAAC;;;;;;;0FAEhB,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,sMAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFACjB,MAAM,MAAM;oFAAC;;;;;;;0FAEhB,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,oMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFAChB,MAAM,MAAM,KAAK,cACd,CAAC,OAAO,EAAE,IAAI,KAAK,MAAM,YAAY,EAAG,kBAAkB,CAAC,UAAU,GACrE,CAAC,QAAQ,EAAE,IAAI,KAAK,MAAM,iBAAiB,EAAE,kBAAkB,CAAC,UAAU;;;;;;;;;;;;;;;;;;;0EAKpF,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,eAAe,MAAM,UAAU;;;;;;kFAElC,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAU,MAAK;wEAAK,WAAU;;0FAC5C,8OAAC,gMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;kEAOtC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAI,WAAU;0EACZ,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC5B,8OAAC;wEAAgB,WAAU;;0FACzB,8OAAC;gFAAK,WAAU;0FAAiB,QAAQ,IAAI;;;;;;0FAC7C,8OAAC;gFAAK,WAAU;;oFAAe,QAAQ,QAAQ;oFAAC;oFAAE,QAAQ,IAAI;;;;;;;;uEAFtD;;;;;;;;;;;;;;;;oDASf,MAAM,MAAM,KAAK,gBAAgB,MAAM,gBAAgB,kBACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAK;;;;;;kFACN,8OAAC;;4EAAM,MAAM,gBAAgB;4EAAC;;;;;;;;;;;;;0EAEhC,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,OAAO,MAAM,gBAAgB;gEAAE,WAAU;;;;;;;;;;;;;+CAjE/C,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCA2E5B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC,kIAAA,CAAA,SAAM;0DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAE,WAAU;;oDACV,cAAc,eAAe;oDAAC;oDAAU,cAAc,eAAe,GAAG,IAAI,MAAM;oDAAG;;;;;;;0DAExF,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQlC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1C;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf", "debugId": null}}]}