import { LoginForm } from '@/components/auth/LoginForm'

interface LoginPageProps {
  searchParams: Promise<{
    redirectTo?: string
  }>
}

export default async function LoginPage({ searchParams }: LoginPageProps) {
  const params = await searchParams
  return <LoginForm redirectTo={params.redirectTo} />
}

export const metadata = {
  title: 'Connexion - Bi-Express',
  description: 'Connectez-vous à votre compte Bi-Express'
}
