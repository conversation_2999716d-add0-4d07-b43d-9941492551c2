export default function ReportsPage() {
  return (
    <div className="p-6">
      <div className="border-b border-gray-200 pb-4 mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Rapports</h1>
        <p className="text-gray-600 mt-1">
          Analyses et statistiques de votre activité
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Rapport des ventes */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <div className="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
              <span className="text-green-600 text-lg">📊</span>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Rapport des ventes</h3>
              <p className="text-sm text-gray-600">Analyse des performances</p>
            </div>
          </div>
          <div className="text-sm text-gray-500 mb-4">
            <ul className="space-y-1">
              <li>• Chiffre d'affaires par période</li>
              <li>• Évolution des bénéfices</li>
              <li>• Top produits vendus</li>
              <li>• Performance par fournisseur</li>
            </ul>
          </div>
          <button className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
            Générer le rapport
          </button>
        </div>

        {/* Rapport des stocks */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
              <span className="text-blue-600 text-lg">📦</span>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Rapport des stocks</h3>
              <p className="text-sm text-gray-600">État des inventaires</p>
            </div>
          </div>
          <div className="text-sm text-gray-500 mb-4">
            <ul className="space-y-1">
              <li>• Valeur totale du stock</li>
              <li>• Produits en rupture</li>
              <li>• Rotation des stocks</li>
              <li>• Alertes de réapprovisionnement</li>
            </ul>
          </div>
          <button className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
            Générer le rapport
          </button>
        </div>

        {/* Rapport logistique */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <div className="h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
              <span className="text-purple-600 text-lg">🚛</span>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Rapport logistique</h3>
              <p className="text-sm text-gray-600">Performance des livraisons</p>
            </div>
          </div>
          <div className="text-sm text-gray-500 mb-4">
            <ul className="space-y-1">
              <li>• Délais de livraison moyens</li>
              <li>• Coûts de transport</li>
              <li>• Taux de livraison à temps</li>
              <li>• Comparaison routier vs aérien</li>
            </ul>
          </div>
          <button className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
            Générer le rapport
          </button>
        </div>

        {/* Rapport clients */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <div className="h-10 w-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
              <span className="text-orange-600 text-lg">👥</span>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Rapport clients</h3>
              <p className="text-sm text-gray-600">Analyse de la clientèle</p>
            </div>
          </div>
          <div className="text-sm text-gray-500 mb-4">
            <ul className="space-y-1">
              <li>• Clients les plus actifs</li>
              <li>• Panier moyen par client</li>
              <li>• Fréquence de commande</li>
              <li>• Fidélisation client</li>
            </ul>
          </div>
          <button className="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors">
            Générer le rapport
          </button>
        </div>

        {/* Rapport financier */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <div className="h-10 w-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
              <span className="text-indigo-600 text-lg">💰</span>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Rapport financier</h3>
              <p className="text-sm text-gray-600">Analyse des finances</p>
            </div>
          </div>
          <div className="text-sm text-gray-500 mb-4">
            <ul className="space-y-1">
              <li>• Compte de résultat</li>
              <li>• Marges par catégorie</li>
              <li>• Évolution des coûts</li>
              <li>• Rentabilité par ville</li>
            </ul>
          </div>
          <button className="w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
            Générer le rapport
          </button>
        </div>

        {/* Rapport personnalisé */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <div className="h-10 w-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
              <span className="text-gray-600 text-lg">⚙️</span>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Rapport personnalisé</h3>
              <p className="text-sm text-gray-600">Créer un rapport sur mesure</p>
            </div>
          </div>
          <div className="text-sm text-gray-500 mb-4">
            <ul className="space-y-1">
              <li>• Sélection des métriques</li>
              <li>• Période personnalisée</li>
              <li>• Filtres avancés</li>
              <li>• Export Excel/PDF</li>
            </ul>
          </div>
          <button className="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
            Créer un rapport
          </button>
        </div>
      </div>

      <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Rapports automatiques
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <p className="font-medium text-gray-900">Rapport hebdomadaire</p>
              <p className="text-sm text-gray-600">Envoyé chaque lundi matin</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" defaultChecked />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <p className="font-medium text-gray-900">Rapport mensuel</p>
              <p className="text-sm text-gray-600">Envoyé le 1er de chaque mois</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  )
}
