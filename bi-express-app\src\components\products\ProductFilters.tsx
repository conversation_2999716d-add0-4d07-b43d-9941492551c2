'use client'

import { useState } from 'react'
import { Search, Filter, AlertTriangle } from 'lucide-react'

export function ProductFilters() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedCity, setSelectedCity] = useState('')
  const [showLowStock, setShowLowStock] = useState(false)
  const [priceRange, setPriceRange] = useState('')

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        {/* Search */}
        <div className="lg:col-span-2 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Rechercher un produit..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Category Filter */}
        <div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Toutes catégories</option>
            <option value="TISSUS">Tissus</option>
            <option value="COSMETIQUES">Cosmétiques</option>
            <option value="MECHES">Mèches</option>
          </select>
        </div>

        {/* City Filter */}
        <div>
          <select
            value={selectedCity}
            onChange={(e) => setSelectedCity(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Toutes villes</option>
            <option value="LAGOS">Lagos</option>
            <option value="ABUJA">Abuja</option>
            <option value="KANO">Kano</option>
          </select>
        </div>

        {/* Price Range */}
        <div>
          <select
            value={priceRange}
            onChange={(e) => setPriceRange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Tous prix</option>
            <option value="0-10000">0 - 10,000 CFA</option>
            <option value="10000-50000">10,000 - 50,000 CFA</option>
            <option value="50000-100000">50,000 - 100,000 CFA</option>
            <option value="100000+">100,000+ CFA</option>
          </select>
        </div>

        {/* Actions */}
        <div className="flex space-x-2">
          <button
            onClick={() => setShowLowStock(!showLowStock)}
            className={`flex items-center px-3 py-2 rounded-md transition-colors ${
              showLowStock
                ? 'bg-red-100 text-red-700 border border-red-300'
                : 'bg-gray-100 text-gray-700 border border-gray-300'
            }`}
          >
            <AlertTriangle className="h-4 w-4 mr-1" />
            Stock faible
          </button>
        </div>
      </div>

      {/* Active Filters */}
      {(searchTerm || selectedCategory || selectedCity || showLowStock || priceRange) && (
        <div className="mt-4 flex flex-wrap items-center gap-2">
          <span className="text-sm text-gray-500">Filtres actifs:</span>
          
          {searchTerm && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Recherche: {searchTerm}
              <button
                onClick={() => setSearchTerm('')}
                className="ml-1 text-blue-600 hover:text-blue-800"
              >
                ×
              </button>
            </span>
          )}
          
          {selectedCategory && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              Catégorie: {selectedCategory}
              <button
                onClick={() => setSelectedCategory('')}
                className="ml-1 text-purple-600 hover:text-purple-800"
              >
                ×
              </button>
            </span>
          )}
          
          {selectedCity && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Ville: {selectedCity}
              <button
                onClick={() => setSelectedCity('')}
                className="ml-1 text-green-600 hover:text-green-800"
              >
                ×
              </button>
            </span>
          )}
          
          {showLowStock && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
              Stock faible
              <button
                onClick={() => setShowLowStock(false)}
                className="ml-1 text-red-600 hover:text-red-800"
              >
                ×
              </button>
            </span>
          )}
          
          {priceRange && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
              Prix: {priceRange} CFA
              <button
                onClick={() => setPriceRange('')}
                className="ml-1 text-yellow-600 hover:text-yellow-800"
              >
                ×
              </button>
            </span>
          )}
          
          <button
            onClick={() => {
              setSearchTerm('')
              setSelectedCategory('')
              setSelectedCity('')
              setShowLowStock(false)
              setPriceRange('')
            }}
            className="text-xs text-gray-500 hover:text-gray-700 underline"
          >
            Effacer tous les filtres
          </button>
        </div>
      )}
    </div>
  )
}
