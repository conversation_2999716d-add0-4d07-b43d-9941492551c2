import { Suspense } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge-component'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { 
  ShoppingCart, 
  Package, 
  Truck, 
  TrendingUp,
  DollarSign,
  Star,
  Plus,
  Eye,
  Calendar,
  MapPin,
  Weight,
  Clock
} from 'lucide-react'

// Données de démonstration pour client commerce
const commerceStats = {
  totalOrders: 18,
  totalValue: 4250000,
  averageOrderValue: 236111,
  pendingOrders: 3,
  inTransitOrders: 2,
  deliveredOrders: 13,
  averageDeliveryTime: 6.2,
  customerSatisfaction: 4.7
}

const recentOrders = [
  {
    id: 'CMD-2024-015',
    products: [
      { name: 'Tissus Wax Premium', quantity: 50, unit: 'yards' },
      { name: 'Cosmétiques Nivea', quantity: 24, unit: 'pcs' }
    ],
    totalValue: 385000,
    status: 'DELIVERED',
    orderDate: '2024-01-08',
    deliveryDate: '2024-01-14',
    origin: 'Lagos',
    weight: 45.5,
    rating: 5
  },
  {
    id: 'CMD-2024-016',
    products: [
      { name: 'Mèches Brésiliennes', quantity: 100, unit: 'pcs' },
      { name: 'Produits capillaires', quantity: 36, unit: 'pcs' }
    ],
    totalValue: 520000,
    status: 'IN_TRANSIT',
    orderDate: '2024-01-10',
    estimatedDelivery: '2024-01-16',
    origin: 'Kano',
    weight: 28.0,
    trackingProgress: 75
  },
  {
    id: 'CMD-2024-017',
    products: [
      { name: 'Tissus Ankara', quantity: 80, unit: 'yards' },
      { name: 'Accessoires mode', quantity: 45, unit: 'pcs' }
    ],
    totalValue: 295000,
    status: 'PENDING',
    orderDate: '2024-01-12',
    estimatedDelivery: '2024-01-19',
    origin: 'Abuja',
    weight: 35.2
  }
]

const productCategories = [
  {
    name: 'Tissus',
    orders: 8,
    value: 1850000,
    growth: '+12%',
    icon: '🧵'
  },
  {
    name: 'Cosmétiques',
    orders: 6,
    value: 1420000,
    growth: '+8%',
    icon: '💄'
  },
  {
    name: 'Mèches',
    orders: 4,
    value: 980000,
    growth: '+15%',
    icon: '💇‍♀️'
  }
]

export default function CommerceClientPage() {
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { label: 'En préparation', color: 'bg-yellow-100 text-yellow-800' },
      IN_TRANSIT: { label: 'En transit', color: 'bg-blue-100 text-blue-800' },
      DELIVERED: { label: 'Livré', color: 'bg-green-100 text-green-800' },
      CANCELLED: { label: 'Annulé', color: 'bg-red-100 text-red-800' }
    }
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING
    return <Badge className={config.color}>{config.label}</Badge>
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ))
  }

  return (
    <div className="p-6 space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Espace Client Commerce
          </h1>
          <p className="text-gray-600">
            Gérez vos achats et suivez vos livraisons "rendu Dakar"
          </p>
        </div>
        <Button className="flex items-center">
          <Plus className="h-4 w-4 mr-2" />
          Nouvelle commande
        </Button>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <ShoppingCart className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total commandes</p>
                <p className="text-2xl font-bold text-gray-900">{commerceStats.totalOrders}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Valeur totale</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(commerceStats.totalValue)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Panier moyen</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(commerceStats.averageOrderValue)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Satisfaction</p>
                <p className="text-2xl font-bold text-gray-900">
                  {commerceStats.customerSatisfaction}/5
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Répartition des commandes */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Statut des commandes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Livrées</span>
                <span className="font-bold text-green-600">{commerceStats.deliveredOrders}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">En transit</span>
                <span className="font-bold text-blue-600">{commerceStats.inTransitOrders}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">En préparation</span>
                <span className="font-bold text-yellow-600">{commerceStats.pendingOrders}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance livraison</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {commerceStats.averageDeliveryTime} jours
              </div>
              <p className="text-sm text-gray-600">Délai moyen de livraison</p>
              <div className="mt-4">
                <Progress value={85} className="h-2" />
                <p className="text-xs text-gray-500 mt-1">85% dans les délais</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Catégories populaires</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {productCategories.map((category) => (
                <div key={category.name} className="flex justify-between items-center">
                  <div className="flex items-center">
                    <span className="text-lg mr-2">{category.icon}</span>
                    <span className="text-sm font-medium">{category.name}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold">{formatCurrency(category.value)}</div>
                    <div className="text-xs text-green-600">{category.growth}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Contenu principal */}
      <Tabs defaultValue="orders" className="space-y-6">
        <TabsList>
          <TabsTrigger value="orders">Mes commandes</TabsTrigger>
          <TabsTrigger value="catalog">Catalogue</TabsTrigger>
          <TabsTrigger value="tracking">Suivi</TabsTrigger>
          <TabsTrigger value="invoices">Factures</TabsTrigger>
        </TabsList>

        <TabsContent value="orders">
          <Card>
            <CardHeader>
              <CardTitle>Commandes récentes</CardTitle>
              <CardDescription>
                Historique de vos achats avec transport intégré
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentOrders.map((order) => (
                  <div key={order.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="font-medium text-gray-900">{order.id}</h3>
                          {getStatusBadge(order.status)}
                          {order.rating && (
                            <div className="flex items-center">
                              {renderStars(order.rating)}
                            </div>
                          )}
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            Commandé: {new Date(order.orderDate).toLocaleDateString('fr-FR')}
                          </div>
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-1" />
                            {order.origin} → Dakar
                          </div>
                          <div className="flex items-center">
                            <Weight className="h-4 w-4 mr-1" />
                            {order.weight} kg
                          </div>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {order.status === 'DELIVERED' 
                              ? `Livré: ${new Date(order.deliveryDate!).toLocaleDateString('fr-FR')}`
                              : `Estimé: ${new Date(order.estimatedDelivery).toLocaleDateString('fr-FR')}`
                            }
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-lg text-gray-900">
                          {formatCurrency(order.totalValue)}
                        </div>
                        <Button variant="outline" size="sm" className="mt-2">
                          <Eye className="h-4 w-4 mr-1" />
                          Détails
                        </Button>
                      </div>
                    </div>

                    {/* Produits de la commande */}
                    <div className="bg-gray-50 rounded-lg p-3">
                      <h4 className="font-medium text-sm text-gray-900 mb-2">Produits commandés</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {order.products.map((product, index) => (
                          <div key={index} className="flex justify-between text-sm">
                            <span className="text-gray-600">{product.name}</span>
                            <span className="font-medium">{product.quantity} {product.unit}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Barre de progression pour les commandes en transit */}
                    {order.status === 'IN_TRANSIT' && order.trackingProgress && (
                      <div className="mt-3">
                        <div className="flex justify-between text-sm text-gray-600 mb-1">
                          <span>Progression de la livraison</span>
                          <span>{order.trackingProgress}%</span>
                        </div>
                        <Progress value={order.trackingProgress} className="h-2" />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="catalog">
          <Card>
            <CardHeader>
              <CardTitle>Catalogue produits</CardTitle>
              <CardDescription>
                Parcourez notre sélection de produits avec prix "rendu Dakar"
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <ShoppingCart className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                <p className="text-gray-500 mb-4">
                  Catalogue en cours de développement
                </p>
                <Button>
                  Accéder au catalogue
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tracking">
          <Card>
            <CardHeader>
              <CardTitle>Suivi des livraisons</CardTitle>
              <CardDescription>
                Suivez vos commandes en temps réel
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Truck className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                <p className="text-gray-500 mb-4">
                  {commerceStats.inTransitOrders} commande{commerceStats.inTransitOrders > 1 ? 's' : ''} en cours de livraison
                </p>
                <Button variant="outline">
                  Voir le suivi détaillé
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="invoices">
          <Card>
            <CardHeader>
              <CardTitle>Factures et paiements</CardTitle>
              <CardDescription>
                Consultez vos factures et l'historique des paiements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <DollarSign className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                <p className="text-gray-500 mb-4">
                  Gestion des factures en cours de développement
                </p>
                <Button variant="outline">
                  Voir toutes les factures
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export const metadata = {
  title: 'Espace Client Commerce - Bi-Express',
  description: 'Dashboard client pour la gestion des achats avec transport intégré'
}
