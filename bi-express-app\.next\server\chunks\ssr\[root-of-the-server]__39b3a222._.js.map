{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: 'NGN' | 'XOF' = 'XOF'): string {\n  const symbols = {\n    NGN: '₦',\n    XOF: 'CFA'\n  }\n  \n  return `${amount.toLocaleString('fr-FR')} ${symbols[currency]}`\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric'\n  }).format(date)\n}\n\nexport function formatDateTime(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  }).format(date)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAA0B,KAAK;IAC5E,MAAM,UAAU;QACd,KAAK;QACL,KAAK;IACP;IAEA,OAAO,GAAG,OAAO,cAAc,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,SAAS,EAAE;AACjE;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;IACR,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAU;IACvC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/dashboard/DashboardStats.tsx"], "sourcesContent": ["import { prisma } from '@/lib/prisma'\nimport { formatCurrency } from '@/lib/utils'\nimport { \n  TrendingUp, \n  ShoppingCart, \n  DollarSign, \n  Percent \n} from 'lucide-react'\n\nasync function getStats() {\n  // Récupérer les statistiques depuis la base de données\n  const [\n    totalProducts,\n    totalSuppliers,\n    totalCustomers,\n    recentOrders\n  ] = await Promise.all([\n    prisma.product.count({ where: { isActive: true } }),\n    prisma.supplier.count({ where: { isActive: true } }),\n    prisma.customer.count({ where: { isActive: true } }),\n    prisma.order.findMany({\n      take: 10,\n      orderBy: { createdAt: 'desc' },\n      include: {\n        customer: true,\n        supplier: true,\n        orderItems: {\n          include: {\n            product: true\n          }\n        }\n      }\n    })\n  ])\n\n  // Calculer les statistiques financières\n  const totalRevenue = recentOrders.reduce((sum, order) => sum + order.totalAmount, 0)\n  const totalProfit = recentOrders.reduce((sum, order) => sum + order.totalProfit, 0)\n  const averageMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0\n\n  return {\n    totalRevenue,\n    totalOrders: recentOrders.length,\n    totalProfit,\n    averageMargin,\n    totalProducts,\n    totalSuppliers,\n    totalCustomers\n  }\n}\n\nexport async function DashboardStats() {\n  const stats = await getStats()\n\n  const statCards = [\n    {\n      title: 'Chiffre d\\'affaires',\n      value: formatCurrency(stats.totalRevenue),\n      icon: DollarSign,\n      color: 'text-green-600',\n      bgColor: 'bg-green-50',\n      change: '+12.5%',\n      changeType: 'positive' as const\n    },\n    {\n      title: 'Commandes',\n      value: stats.totalOrders.toString(),\n      icon: ShoppingCart,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-50',\n      change: '+8.2%',\n      changeType: 'positive' as const\n    },\n    {\n      title: 'Bénéfices',\n      value: formatCurrency(stats.totalProfit),\n      icon: TrendingUp,\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-50',\n      change: '+15.3%',\n      changeType: 'positive' as const\n    },\n    {\n      title: 'Marge moyenne',\n      value: `${stats.averageMargin.toFixed(1)}%`,\n      icon: Percent,\n      color: 'text-orange-600',\n      bgColor: 'bg-orange-50',\n      change: '+2.1%',\n      changeType: 'positive' as const\n    }\n  ]\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n      {statCards.map((stat, index) => (\n        <div key={index} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">{stat.title}</p>\n              <p className=\"text-2xl font-bold text-gray-900 mt-1\">{stat.value}</p>\n            </div>\n            <div className={`p-3 rounded-lg ${stat.bgColor}`}>\n              <stat.icon className={`h-6 w-6 ${stat.color}`} />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <span className={`text-sm font-medium ${\n              stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'\n            }`}>\n              {stat.change}\n            </span>\n            <span className=\"text-sm text-gray-500 ml-1\">vs mois dernier</span>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;;;;;AAOA,eAAe;IACb,uDAAuD;IACvD,MAAM,CACJ,eACA,gBACA,gBACA,aACD,GAAG,MAAM,QAAQ,GAAG,CAAC;QACpB,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YAAE,OAAO;gBAAE,UAAU;YAAK;QAAE;QACjD,oHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO;gBAAE,UAAU;YAAK;QAAE;QAClD,oHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO;gBAAE,UAAU;YAAK;QAAE;QAClD,oHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YACpB,MAAM;YACN,SAAS;gBAAE,WAAW;YAAO;YAC7B,SAAS;gBACP,UAAU;gBACV,UAAU;gBACV,YAAY;oBACV,SAAS;wBACP,SAAS;oBACX;gBACF;YACF;QACF;KACD;IAED,wCAAwC;IACxC,MAAM,eAAe,aAAa,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,WAAW,EAAE;IAClF,MAAM,cAAc,aAAa,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,WAAW,EAAE;IACjF,MAAM,gBAAgB,eAAe,IAAI,AAAC,cAAc,eAAgB,MAAM;IAE9E,OAAO;QACL;QACA,aAAa,aAAa,MAAM;QAChC;QACA;QACA;QACA;QACA;IACF;AACF;AAEO,eAAe;IACpB,MAAM,QAAQ,MAAM;IAEpB,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY;YACxC,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;YACT,QAAQ;YACR,YAAY;QACd;QACA;YACE,OAAO;YACP,OAAO,MAAM,WAAW,CAAC,QAAQ;YACjC,MAAM,sNAAA,CAAA,eAAY;YAClB,OAAO;YACP,SAAS;YACT,QAAQ;YACR,YAAY;QACd;QACA;YACE,OAAO;YACP,OAAO,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,WAAW;YACvC,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;YACT,QAAQ;YACR,YAAY;QACd;QACA;YACE,OAAO;YACP,OAAO,GAAG,MAAM,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC3C,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,SAAS;YACT,QAAQ;YACR,YAAY;QACd;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;gBAAgB,WAAU;;kCACzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAqC,KAAK,KAAK;;;;;;kDAC5D,8OAAC;wCAAE,WAAU;kDAAyC,KAAK,KAAK;;;;;;;;;;;;0CAElE,8OAAC;gCAAI,WAAW,CAAC,eAAe,EAAE,KAAK,OAAO,EAAE;0CAC9C,cAAA,8OAAC,KAAK,IAAI;oCAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;;;;;;;kCAGjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAW,CAAC,oBAAoB,EACpC,KAAK,UAAU,KAAK,aAAa,mBAAmB,gBACpD;0CACC,KAAK,MAAM;;;;;;0CAEd,8OAAC;gCAAK,WAAU;0CAA6B;;;;;;;;;;;;;eAhBvC;;;;;;;;;;AAsBlB", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/badge-component.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {\n  children: React.ReactNode\n  className?: string\n  variant?: 'default' | 'secondary' | 'destructive' | 'outline'\n}\n\nexport function Badge({ children, className, variant = 'default', ...props }: BadgeProps) {\n  const variantClasses = {\n    default: 'bg-blue-100 text-blue-800 border-blue-200',\n    secondary: 'bg-gray-100 text-gray-800 border-gray-200',\n    destructive: 'bg-red-100 text-red-800 border-red-200',\n    outline: 'bg-transparent text-gray-700 border-gray-300'\n  }\n\n  return (\n    <span \n      className={cn(\n        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border',\n        variantClasses[variant],\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAmB;IACtF,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,aAAa;QACb,SAAS;IACX;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kFACA,cAAc,CAAC,QAAQ,EACvB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/dashboard/RecentOrders.tsx"], "sourcesContent": ["import { prisma } from '@/lib/prisma'\nimport { formatCurrency, formatDateTime } from '@/lib/utils'\nimport { Badge } from '@/components/ui/badge-component'\n\nasync function getRecentOrders() {\n  return await prisma.order.findMany({\n    take: 5,\n    orderBy: { createdAt: 'desc' },\n    include: {\n      customer: true,\n      supplier: true,\n      orderItems: {\n        include: {\n          product: true\n        }\n      }\n    }\n  })\n}\n\nconst statusColors = {\n  PENDING: 'bg-yellow-100 text-yellow-800',\n  CONFIRMED: 'bg-blue-100 text-blue-800',\n  SHIPPED: 'bg-purple-100 text-purple-800',\n  DELIVERED: 'bg-green-100 text-green-800',\n  CANCELLED: 'bg-red-100 text-red-800'\n}\n\nconst statusLabels = {\n  PENDING: 'En attente',\n  CONFIRMED: 'Confirmée',\n  SHIPPED: 'Expédiée',\n  DELIVERED: 'Livrée',\n  CANCELLED: 'Annulée'\n}\n\nexport async function RecentOrders() {\n  const orders = await getRecentOrders()\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n      <div className=\"px-6 py-4 border-b border-gray-200\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Commandes récentes</h3>\n        <p className=\"text-sm text-gray-600 mt-1\">\n          Dernières commandes passées par vos clients\n        </p>\n      </div>\n      \n      <div className=\"overflow-x-auto\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Commande\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Client\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Fournisseur\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Montant\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Statut\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Date\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {orders.map((order) => (\n              <tr key={order.id} className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <div className=\"text-sm font-medium text-gray-900\">\n                    {order.orderNumber}\n                  </div>\n                  <div className=\"text-sm text-gray-500\">\n                    {order.orderItems.length} article{order.orderItems.length > 1 ? 's' : ''}\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <div className=\"text-sm font-medium text-gray-900\">\n                    {order.customer.name}\n                  </div>\n                  <div className=\"text-sm text-gray-500\">\n                    {order.customer.phone}\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <div className=\"text-sm text-gray-900\">\n                    {order.supplier.name}\n                  </div>\n                  <div className=\"text-sm text-gray-500\">\n                    {order.supplier.city}\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <div className=\"text-sm font-medium text-gray-900\">\n                    {formatCurrency(order.totalAmount)}\n                  </div>\n                  <div className=\"text-sm text-green-600\">\n                    +{formatCurrency(order.totalProfit)} bénéfice\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <Badge className={statusColors[order.status]}>\n                    {statusLabels[order.status]}\n                  </Badge>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                  {formatDateTime(order.createdAt)}\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n      \n      {orders.length === 0 && (\n        <div className=\"text-center py-12\">\n          <p className=\"text-gray-500\">Aucune commande récente</p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,eAAe;IACb,OAAO,MAAM,oHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;QACjC,MAAM;QACN,SAAS;YAAE,WAAW;QAAO;QAC7B,SAAS;YACP,UAAU;YACV,UAAU;YACV,YAAY;gBACV,SAAS;oBACP,SAAS;gBACX;YACF;QACF;IACF;AACF;AAEA,MAAM,eAAe;IACnB,SAAS;IACT,WAAW;IACX,SAAS;IACT,WAAW;IACX,WAAW;AACb;AAEA,MAAM,eAAe;IACnB,SAAS;IACT,WAAW;IACX,SAAS;IACT,WAAW;IACX,WAAW;AACb;AAEO,eAAe;IACpB,MAAM,SAAS,MAAM;IAErB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAK5C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,WAAU;sCACf,cAAA,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,8OAAC;wCAAG,WAAU;kDAAiF;;;;;;;;;;;;;;;;;sCAKnG,8OAAC;4BAAM,WAAU;sCACd,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;oCAAkB,WAAU;;sDAC3B,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;8DACZ,MAAM,WAAW;;;;;;8DAEpB,8OAAC;oDAAI,WAAU;;wDACZ,MAAM,UAAU,CAAC,MAAM;wDAAC;wDAAS,MAAM,UAAU,CAAC,MAAM,GAAG,IAAI,MAAM;;;;;;;;;;;;;sDAG1E,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;8DACZ,MAAM,QAAQ,CAAC,IAAI;;;;;;8DAEtB,8OAAC;oDAAI,WAAU;8DACZ,MAAM,QAAQ,CAAC,KAAK;;;;;;;;;;;;sDAGzB,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;8DACZ,MAAM,QAAQ,CAAC,IAAI;;;;;;8DAEtB,8OAAC;oDAAI,WAAU;8DACZ,MAAM,QAAQ,CAAC,IAAI;;;;;;;;;;;;sDAGxB,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,WAAW;;;;;;8DAEnC,8OAAC;oDAAI,WAAU;;wDAAyB;wDACpC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,WAAW;wDAAE;;;;;;;;;;;;;sDAGxC,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,8IAAA,CAAA,QAAK;gDAAC,WAAW,YAAY,CAAC,MAAM,MAAM,CAAC;0DACzC,YAAY,CAAC,MAAM,MAAM,CAAC;;;;;;;;;;;sDAG/B,8OAAC;4CAAG,WAAU;sDACX,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,SAAS;;;;;;;mCAvC1B,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;YA+CxB,OAAO,MAAM,KAAK,mBACjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAKvC", "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/dashboard/SalesChart.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SalesChart = registerClientReference(\n    function() { throw new Error(\"Attempted to call SalesChart() from the server but SalesChart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/SalesChart.tsx <module evaluation>\",\n    \"SalesChart\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,yEACA", "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/dashboard/SalesChart.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SalesChart = registerClientReference(\n    function() { throw new Error(\"Attempted to call SalesChart() from the server but SalesChart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/SalesChart.tsx\",\n    \"SalesChart\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,qDACA", "debugId": null}}, {"offset": {"line": 669, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/dashboard/TopProducts.tsx"], "sourcesContent": ["import { prisma } from '@/lib/prisma'\nimport { formatCurrency } from '@/lib/utils'\nimport { Badge } from '@/components/ui/badge-component'\n\nasync function getTopProducts() {\n  const products = await prisma.product.findMany({\n    take: 5,\n    where: { isActive: true },\n    include: {\n      supplier: true,\n      orderItems: true\n    },\n    orderBy: {\n      stockQuantity: 'desc'\n    }\n  })\n\n  return products.map(product => ({\n    ...product,\n    totalSold: product.orderItems.reduce((sum, item) => sum + item.quantity, 0),\n    revenue: product.orderItems.reduce((sum, item) => sum + item.totalPrice, 0)\n  }))\n}\n\nconst categoryColors = {\n  TISSUS: 'bg-blue-100 text-blue-800',\n  COSMETIQUES: 'bg-pink-100 text-pink-800',\n  MECHES: 'bg-purple-100 text-purple-800'\n}\n\nconst categoryLabels = {\n  TISSUS: 'Tissus',\n  COSMETIQUES: 'Cosmétiques',\n  MECHES: 'Mèches'\n}\n\nexport async function TopProducts() {\n  const products = await getTopProducts()\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n      <div className=\"mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Produits populaires</h3>\n        <p className=\"text-sm text-gray-600 mt-1\">\n          Produits les plus en stock et performants\n        </p>\n      </div>\n      \n      <div className=\"space-y-4\">\n        {products.map((product, index) => (\n          <div key={product.id} className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold\">\n                  {index + 1}\n                </div>\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900 truncate\">\n                  {product.name}\n                </p>\n                <div className=\"flex items-center space-x-2 mt-1\">\n                  <Badge className={categoryColors[product.category]}>\n                    {categoryLabels[product.category]}\n                  </Badge>\n                  <span className=\"text-xs text-gray-500\">\n                    {product.supplier.name}\n                  </span>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"text-right\">\n              <p className=\"text-sm font-medium text-gray-900\">\n                {formatCurrency(product.supplierPrice)}\n              </p>\n              <p className=\"text-xs text-gray-500\">\n                Stock: {product.stockQuantity}\n              </p>\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      {products.length === 0 && (\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-500\">Aucun produit disponible</p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,eAAe;IACb,MAAM,WAAW,MAAM,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;QAC7C,MAAM;QACN,OAAO;YAAE,UAAU;QAAK;QACxB,SAAS;YACP,UAAU;YACV,YAAY;QACd;QACA,SAAS;YACP,eAAe;QACjB;IACF;IAEA,OAAO,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;YAC9B,GAAG,OAAO;YACV,WAAW,QAAQ,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;YACzE,SAAS,QAAQ,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE;QAC3E,CAAC;AACH;AAEA,MAAM,iBAAiB;IACrB,QAAQ;IACR,aAAa;IACb,QAAQ;AACV;AAEA,MAAM,iBAAiB;IACrB,QAAQ;IACR,aAAa;IACb,QAAQ;AACV;AAEO,eAAe;IACpB,MAAM,WAAW,MAAM;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAK5C,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;wBAAqB,WAAU;;0CAC9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,QAAQ;;;;;;;;;;;kDAGb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,QAAQ,IAAI;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8IAAA,CAAA,QAAK;wDAAC,WAAW,cAAc,CAAC,QAAQ,QAAQ,CAAC;kEAC/C,cAAc,CAAC,QAAQ,QAAQ,CAAC;;;;;;kEAEnC,8OAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,aAAa;;;;;;kDAEvC,8OAAC;wCAAE,WAAU;;4CAAwB;4CAC3B,QAAQ,aAAa;;;;;;;;;;;;;;uBA3BzB,QAAQ,EAAE;;;;;;;;;;YAkCvB,SAAS,MAAM,KAAK,mBACnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAKvC", "debugId": null}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/currency/ExchangeRateWidget.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ExchangeRateWidget = registerClientReference(\n    function() { throw new Error(\"Attempted to call ExchangeRateWidget() from the server but ExchangeRateWidget is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/currency/ExchangeRateWidget.tsx <module evaluation>\",\n    \"ExchangeRateWidget\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,gFACA", "debugId": null}}, {"offset": {"line": 902, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/currency/ExchangeRateWidget.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ExchangeRateWidget = registerClientReference(\n    function() { throw new Error(\"Attempted to call ExchangeRateWidget() from the server but ExchangeRateWidget is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/currency/ExchangeRateWidget.tsx\",\n    \"ExchangeRateWidget\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,4DACA", "debugId": null}}, {"offset": {"line": 916, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 926, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/page.tsx"], "sourcesContent": ["import { Suspense } from 'react'\nimport { DashboardStats } from '@/components/dashboard/DashboardStats'\nimport { RecentOrders } from '@/components/dashboard/RecentOrders'\nimport { SalesChart } from '@/components/dashboard/SalesChart'\nimport { TopProducts } from '@/components/dashboard/TopProducts'\nimport { ExchangeRateWidget } from '@/components/currency/ExchangeRateWidget'\nimport { QuickActions } from '@/components/dashboard/QuickActions'\n\nexport default function Dashboard() {\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* Header */}\n      <div className=\"border-b border-gray-200 pb-4\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">Dashboard</h1>\n        <p className=\"text-gray-600 mt-1\">\n          Vue d'ensemble de votre activité de vente en gros Nigeria-Dakar\n        </p>\n      </div>\n\n      {/* Quick Actions */}\n      <QuickActions />\n\n      {/* Stats Cards */}\n      <Suspense fallback={<div className=\"animate-pulse h-32 bg-gray-200 rounded-lg\" />}>\n        <DashboardStats />\n      </Suspense>\n\n      {/* Charts and Tables */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <div className=\"lg:col-span-2 space-y-6\">\n          <Suspense fallback={<div className=\"animate-pulse h-80 bg-gray-200 rounded-lg\" />}>\n            <SalesChart />\n          </Suspense>\n\n          <Suspense fallback={<div className=\"animate-pulse h-80 bg-gray-200 rounded-lg\" />}>\n            <TopProducts />\n          </Suspense>\n        </div>\n\n        <div className=\"space-y-6\">\n          <Suspense fallback={<div className=\"animate-pulse h-32 bg-gray-200 rounded-lg\" />}>\n            <ExchangeRateWidget />\n          </Suspense>\n        </div>\n      </div>\n\n      {/* Recent Orders */}\n      <Suspense fallback={<div className=\"animate-pulse h-64 bg-gray-200 rounded-lg\" />}>\n        <RecentOrders />\n      </Suspense>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAGe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAMpC,8OAAC;;;;;0BAGD,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;oBAAI,WAAU;;;;;;0BACjC,cAAA,8OAAC,iJAAA,CAAA,iBAAc;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,qMAAA,CAAA,WAAQ;gCAAC,wBAAU,8OAAC;oCAAI,WAAU;;;;;;0CACjC,cAAA,8OAAC,6IAAA,CAAA,aAAU;;;;;;;;;;0CAGb,8OAAC,qMAAA,CAAA,WAAQ;gCAAC,wBAAU,8OAAC;oCAAI,WAAU;;;;;;0CACjC,cAAA,8OAAC,8IAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kCAIhB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,qMAAA,CAAA,WAAQ;4BAAC,wBAAU,8OAAC;gCAAI,WAAU;;;;;;sCACjC,cAAA,8OAAC,oJAAA,CAAA,qBAAkB;;;;;;;;;;;;;;;;;;;;;0BAMzB,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;oBAAI,WAAU;;;;;;0BACjC,cAAA,8OAAC,+IAAA,CAAA,eAAY;;;;;;;;;;;;;;;;AAIrB", "debugId": null}}]}