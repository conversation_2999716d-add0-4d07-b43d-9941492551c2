import { prisma } from '@/lib/prisma'
import { formatCurrency } from '@/lib/utils'
import { 
  TrendingUp, 
  ShoppingCart, 
  DollarSign, 
  Percent 
} from 'lucide-react'

async function getStats() {
  // Récupérer les statistiques depuis la base de données
  const [
    totalProducts,
    totalSuppliers,
    totalCustomers,
    recentOrders
  ] = await Promise.all([
    prisma.product.count({ where: { isActive: true } }),
    prisma.supplier.count({ where: { isActive: true } }),
    prisma.customer.count({ where: { isActive: true } }),
    prisma.order.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: true,
        supplier: true,
        orderItems: {
          include: {
            product: true
          }
        }
      }
    })
  ])

  // Calculer les statistiques financières
  const totalRevenue = recentOrders.reduce((sum, order) => sum + order.totalAmount, 0)
  const totalProfit = recentOrders.reduce((sum, order) => sum + order.totalProfit, 0)
  const averageMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0

  return {
    totalRevenue,
    totalOrders: recentOrders.length,
    totalProfit,
    averageMargin,
    totalProducts,
    totalSuppliers,
    totalCustomers
  }
}

export async function DashboardStats() {
  const stats = await getStats()

  const statCards = [
    {
      title: 'Chiffre d\'affaires',
      value: formatCurrency(stats.totalRevenue),
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+12.5%',
      changeType: 'positive' as const
    },
    {
      title: 'Commandes',
      value: stats.totalOrders.toString(),
      icon: ShoppingCart,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+8.2%',
      changeType: 'positive' as const
    },
    {
      title: 'Bénéfices',
      value: formatCurrency(stats.totalProfit),
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+15.3%',
      changeType: 'positive' as const
    },
    {
      title: 'Marge moyenne',
      value: `${stats.averageMargin.toFixed(1)}%`,
      icon: Percent,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '+2.1%',
      changeType: 'positive' as const
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statCards.map((stat, index) => (
        <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">{stat.title}</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
            </div>
            <div className={`p-3 rounded-lg ${stat.bgColor}`}>
              <stat.icon className={`h-6 w-6 ${stat.color}`} />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <span className={`text-sm font-medium ${
              stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
            }`}>
              {stat.change}
            </span>
            <span className="text-sm text-gray-500 ml-1">vs mois dernier</span>
          </div>
        </div>
      ))}
    </div>
  )
}
