'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { calculateLogisticsPricing, type PricingRequest, type PricingResult } from '@/lib/pricing'
import { TransportMode, UrgencyLevel } from '@prisma/client'
import { Truck, Plane, Clock, Shield, Calculator } from 'lucide-react'

interface ShipmentRequestFormProps {
  customerId: string
  onSubmit: (data: any) => void
}

export function ShipmentRequestForm({ customerId, onSubmit }: ShipmentRequestFormProps) {
  const [formData, setFormData] = useState({
    originCity: '',
    destinationCity: 'DAKAR',
    description: '',
    weight: '',
    volume: '',
    declaredValue: '',
    transportMode: 'ROAD' as TransportMode,
    urgency: 'STANDARD' as UrgencyLevel,
    insuranceRequired: false,
    specialInstructions: '',
    requestedPickupDate: ''
  })

  const [pricing, setPricing] = useState<PricingResult | null>(null)
  const [isCalculating, setIsCalculating] = useState(false)

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Recalculer automatiquement si les champs critiques changent
    if (['originCity', 'weight', 'transportMode', 'urgency', 'insuranceRequired', 'declaredValue'].includes(field)) {
      calculatePricing({ ...formData, [field]: value })
    }
  }

  const calculatePricing = async (data = formData) => {
    if (!data.originCity || !data.weight || parseFloat(data.weight) <= 0) {
      setPricing(null)
      return
    }

    setIsCalculating(true)
    try {
      const request: PricingRequest = {
        customerType: 'LOGISTICS',
        transportMode: data.transportMode,
        originCity: data.originCity,
        destinationCity: data.destinationCity,
        weight: parseFloat(data.weight),
        volume: data.volume ? parseFloat(data.volume) : undefined,
        declaredValue: data.declaredValue ? parseFloat(data.declaredValue) : undefined,
        urgency: data.urgency,
        insuranceRequired: data.insuranceRequired
      }

      const result = calculateLogisticsPricing(request)
      setPricing(result)
    } catch (error) {
      console.error('Erreur calcul tarif:', error)
      setPricing(null)
    } finally {
      setIsCalculating(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!pricing) {
      calculatePricing()
      return
    }

    const requestData = {
      ...formData,
      customerId,
      weight: parseFloat(formData.weight),
      volume: formData.volume ? parseFloat(formData.volume) : null,
      declaredValue: formData.declaredValue ? parseFloat(formData.declaredValue) : null,
      estimatedCost: pricing.totalCost,
      estimatedDelivery: new Date(Date.now() + pricing.estimatedDays * 24 * 60 * 60 * 1000)
    }

    onSubmit(requestData)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0
    }).format(amount)
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Formulaire */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Truck className="h-5 w-5 mr-2 text-blue-600" />
            Nouvelle demande de transport
          </CardTitle>
          <CardDescription>
            Remplissez les détails de votre expédition pour obtenir un devis instantané
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Origine et destination */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="originCity">Ville d'origine *</Label>
                <Select value={formData.originCity} onValueChange={(value) => handleInputChange('originCity', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="LAGOS">Lagos</SelectItem>
                    <SelectItem value="ABUJA">Abuja</SelectItem>
                    <SelectItem value="KANO">Kano</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="destinationCity">Destination</Label>
                <Input value="Dakar" disabled className="bg-gray-50" />
              </div>
            </div>

            {/* Description */}
            <div>
              <Label htmlFor="description">Description des marchandises *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Décrivez le contenu de votre expédition..."
                required
              />
            </div>

            {/* Poids et volume */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="weight">Poids (kg) *</Label>
                <Input
                  id="weight"
                  type="number"
                  step="0.1"
                  min="0.1"
                  value={formData.weight}
                  onChange={(e) => handleInputChange('weight', e.target.value)}
                  placeholder="0.0"
                  required
                />
              </div>
              <div>
                <Label htmlFor="volume">Volume (m³)</Label>
                <Input
                  id="volume"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.volume}
                  onChange={(e) => handleInputChange('volume', e.target.value)}
                  placeholder="0.00"
                />
              </div>
            </div>

            {/* Mode de transport */}
            <div>
              <Label>Mode de transport *</Label>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    formData.transportMode === 'ROAD'
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleInputChange('transportMode', 'ROAD')}
                >
                  <div className="flex items-center space-x-2">
                    <Truck className="h-5 w-5 text-blue-600" />
                    <div>
                      <div className="font-medium">Routier</div>
                      <div className="text-sm text-gray-500">5-7 jours</div>
                    </div>
                  </div>
                </div>
                <div
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    formData.transportMode === 'AIR_EXPRESS'
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleInputChange('transportMode', 'AIR_EXPRESS')}
                >
                  <div className="flex items-center space-x-2">
                    <Plane className="h-5 w-5 text-blue-600" />
                    <div>
                      <div className="font-medium">Aérien Express</div>
                      <div className="text-sm text-gray-500">24-48h</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Urgence */}
            <div>
              <Label htmlFor="urgency">Niveau d'urgence</Label>
              <Select value={formData.urgency} onValueChange={(value) => handleInputChange('urgency', value as UrgencyLevel)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="STANDARD">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-2" />
                      Standard (délai normal)
                    </div>
                  </SelectItem>
                  <SelectItem value="EXPRESS">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-2 text-orange-500" />
                      Express (+30%)
                    </div>
                  </SelectItem>
                  <SelectItem value="URGENT">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-2 text-red-500" />
                      Urgent (+80%)
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Valeur déclarée et assurance */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="declaredValue">Valeur déclarée (CFA)</Label>
                <Input
                  id="declaredValue"
                  type="number"
                  min="0"
                  value={formData.declaredValue}
                  onChange={(e) => handleInputChange('declaredValue', e.target.value)}
                  placeholder="0"
                />
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="insurance"
                  checked={formData.insuranceRequired}
                  onCheckedChange={(checked) => handleInputChange('insuranceRequired', checked)}
                />
                <Label htmlFor="insurance" className="flex items-center">
                  <Shield className="h-4 w-4 mr-1 text-green-600" />
                  Assurance transport (0.5% de la valeur déclarée)
                </Label>
              </div>
            </div>

            {/* Date d'enlèvement souhaitée */}
            <div>
              <Label htmlFor="pickupDate">Date d'enlèvement souhaitée</Label>
              <Input
                id="pickupDate"
                type="date"
                value={formData.requestedPickupDate}
                onChange={(e) => handleInputChange('requestedPickupDate', e.target.value)}
                min={new Date().toISOString().split('T')[0]}
              />
            </div>

            {/* Instructions spéciales */}
            <div>
              <Label htmlFor="specialInstructions">Instructions spéciales</Label>
              <Textarea
                id="specialInstructions"
                value={formData.specialInstructions}
                onChange={(e) => handleInputChange('specialInstructions', e.target.value)}
                placeholder="Instructions particulières pour l'enlèvement ou la livraison..."
              />
            </div>

            <Separator />

            <Button type="submit" className="w-full" disabled={!pricing || isCalculating}>
              {isCalculating ? 'Calcul en cours...' : 'Confirmer la demande'}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Devis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calculator className="h-5 w-5 mr-2 text-green-600" />
            Devis instantané
          </CardTitle>
          <CardDescription>
            Tarification automatique basée sur vos paramètres
          </CardDescription>
        </CardHeader>
        <CardContent>
          {pricing ? (
            <div className="space-y-4">
              {/* Prix total */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="text-center">
                  <div className="text-sm text-green-600 font-medium">Prix total</div>
                  <div className="text-3xl font-bold text-green-700">
                    {formatCurrency(pricing.totalCost)}
                  </div>
                  <div className="text-sm text-green-600">
                    Délai estimé: {pricing.estimatedDays} jour{pricing.estimatedDays > 1 ? 's' : ''}
                  </div>
                </div>
              </div>

              {/* Détail des coûts */}
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">Détail des coûts</h4>
                {pricing.breakdown.map((item, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100">
                    <div>
                      <div className="font-medium text-sm">{item.item}</div>
                      <div className="text-xs text-gray-500">{item.description}</div>
                    </div>
                    <div className="font-medium text-sm">
                      {formatCurrency(item.amount)}
                    </div>
                  </div>
                ))}
              </div>

              {/* Informations importantes */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <h5 className="font-medium text-blue-900 mb-2">Informations importantes</h5>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Prix valable 48h</li>
                  <li>• Enlèvement gratuit dans la ville d'origine</li>
                  <li>• Livraison incluse à Dakar</li>
                  <li>• Suivi temps réel inclus</li>
                </ul>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Calculator className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Remplissez les informations pour obtenir un devis</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
