import { Suspense } from 'react'
import { ProductsList } from '@/components/products/ProductsList'
import { ProductFilters } from '@/components/products/ProductFilters'
import { AddProductButton } from '@/components/products/AddProductButton'
import { ProductStats } from '@/components/products/ProductStats'

export default function ProductsPage() {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 pb-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Catalogue Produits</h1>
          <p className="text-gray-600 mt-1">
            Gérez vos produits avec calcul automatique des prix et marges
          </p>
        </div>
        <AddProductButton />
      </div>

      {/* Stats */}
      <Suspense fallback={<div className="animate-pulse h-24 bg-gray-200 rounded-lg" />}>
        <ProductStats />
      </Suspense>

      {/* Filters */}
      <Suspense fallback={<div className="animate-pulse h-16 bg-gray-200 rounded-lg" />}>
        <ProductFilters />
      </Suspense>

      {/* Products List */}
      <Suspense fallback={<div className="animate-pulse h-96 bg-gray-200 rounded-lg" />}>
        <ProductsList />
      </Suspense>
    </div>
  )
}
