// Schéma Prisma pour l'application Bi-Express
// Gestion de vente en gros Nigeria-Dakar

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// Modèle Fournisseur
model Supplier {
  id          String   @id @default(cuid())
  name        String
  phone       String
  email       String?
  address     String
  city        SupplierCity
  specialties String   // Spécialités du fournisseur (séparées par des virgules)
  rating      Float    @default(0) // Note sur 5
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  products    Product[]
  orders      Order[]
  evaluations SupplierEvaluation[]

  @@map("suppliers")
}

// Évaluations des fournisseurs
model SupplierEvaluation {
  id         String   @id @default(cuid())
  supplierId String
  rating     Int      // Note de 1 à 5
  comment    String?
  orderId    String?  // Évaluation liée à une commande
  createdAt  DateTime @default(now())

  // Relations
  supplier Supplier @relation(fields: [supplierId], references: [id], onDelete: Cascade)
  order    Order?   @relation(fields: [orderId], references: [id])

  @@map("supplier_evaluations")
}

// Modèle Produit
model Product {
  id          String      @id @default(cuid())
  name        String
  description String?
  category    ProductCategory

  // Spécificités selon la catégorie
  // Pour les tissus
  fabricType  String?     // Type de tissu (wax, bazin, soie, coton)
  width       Float?      // Largeur en mètres
  color       String?
  pattern     String?     // Motif

  // Pour les cosmétiques
  brand       String?     // Marque
  volume      Float?      // Contenance
  origin      String?     // Origine

  // Pour les mèches
  length      Float?      // Longueur
  texture     String?     // Texture
  hairType    String?     // Type de cheveux (naturel, synthétique)

  // Informations tarifaires
  supplierPrice    Float   // Prix fournisseur HT
  logisticRate     Float   @default(0.30) // Taux de frais logistique (30% par défaut)
  margin           Float   @default(0.20) // Marge souhaitée (20% par défaut)

  // Stock et disponibilité
  stockQuantity    Int     @default(0)
  minStockAlert    Int     @default(10)
  isActive         Boolean @default(true)

  // Métadonnées
  imageUrl         String?
  weight           Float?  // Poids en kg
  dimensions       String? // Dimensions (LxlxH)

  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  supplierId       String
  supplier         Supplier @relation(fields: [supplierId], references: [id])
  orderItems       OrderItem[]
  priceHistory     PriceHistory[]

  @@map("products")
}

// Historique des prix
model PriceHistory {
  id            String   @id @default(cuid())
  productId     String
  supplierPrice Float
  margin        Float
  createdAt     DateTime @default(now())

  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("price_history")
}

// Modèle Client
model Customer {
  id              String      @id @default(cuid())
  name            String
  phone           String
  email           String?
  address         String
  city            String      @default("Dakar")

  // Type de client : logistique (transport seul) ou commerce (achat+transport)
  type            CustomerType @default(COMMERCE)

  // Informations commerciales
  companyName     String?
  taxNumber       String?
  creditLimit     Float       @default(0)
  paymentTerms    Int         @default(30) // Jours

  // Préférences logistiques
  preferredMode   TransportMode? // Routier ou Aérien par défaut
  insuranceOpt    Boolean     @default(false)

  // Statut et évaluation
  isActive        Boolean     @default(true)
  status          CustomerStatus @default(ACTIVE)
  rating          Float       @default(0)
  totalOrders     Int         @default(0)
  totalSpent      Float       @default(0)

  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  orders          Order[]
  shipmentRequests ShipmentRequest[]
  evaluations     CustomerEvaluation[]
  notifications   CustomerNotification[]

  @@map("customers")
}

enum CustomerType {
  LOGISTICS    // Transport seul
  COMMERCE     // Achat + Transport
}

enum CustomerStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING_APPROVAL
}

// Modèle Commande
model Order {
  id              String      @id @default(cuid())
  orderNumber     String      @unique
  customerId      String
  supplierId      String

  // Statut et logistique
  status          OrderStatus @default(PENDING)
  transportMode   TransportMode

  // Calculs financiers
  subtotal        Float       // Sous-total HT
  logisticCosts   Float       // Frais logistiques
  totalAmount     Float       // Montant total TTC
  totalProfit     Float       // Bénéfice total

  // Dates importantes
  orderDate       DateTime    @default(now())
  expectedDelivery DateTime?
  deliveredAt     DateTime?

  // Informations de livraison
  deliveryAddress String
  trackingNumber  String?

  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  customer        Customer    @relation(fields: [customerId], references: [id])
  supplier        Supplier    @relation(fields: [supplierId], references: [id])
  orderItems      OrderItem[]
  evaluations     SupplierEvaluation[]
  customerEvaluations CustomerEvaluation[]
  shipment        Shipment?   @relation(fields: [shipmentId], references: [id])
  shipmentId      String?

  @@map("orders")
}

// Articles de commande
model OrderItem {
  id        String @id @default(cuid())
  orderId   String
  productId String
  quantity  Int
  unitPrice Float  // Prix unitaire au moment de la commande
  totalPrice Float // Prix total pour cet article
  profit    Float  // Bénéfice pour cet article

  // Relations
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// Transporteurs/Partenaires logistiques
model Carrier {
  id          String      @id @default(cuid())
  name        String
  phone       String
  email       String?
  address     String
  city        SupplierCity

  // Spécialités transport
  transportModes String    // ROAD,AIR_EXPRESS (séparés par virgules)
  capacity       Float?    // Capacité en kg

  // Performance
  rating         Float     @default(0) // Note sur 5
  onTimeRate     Float     @default(0) // Taux de livraison à temps (%)
  isActive       Boolean   @default(true)

  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  // Relations
  shipments      Shipment[]
  evaluations    CarrierEvaluation[]

  @@map("carriers")
}

// Expéditions
model Shipment {
  id              String        @id @default(cuid())
  trackingNumber  String        @unique
  carrierId       String

  // Type d'expédition
  shipmentType    ShipmentType  @default(MIXED)

  // Statut et progression
  status          ShipmentStatus @default(PENDING)
  transportMode   TransportMode

  // Localisation et suivi
  originCity      SupplierCity
  destinationCity String        @default("DAKAR")
  currentLocation String?
  gpsCoordinates  String?       // Coordonnées GPS actuelles

  // Dates et délais
  pickupDate      DateTime?
  departureDate   DateTime?
  arrivalDate     DateTime?
  deliveryDate    DateTime?
  estimatedDelivery DateTime

  // Capacité et chargement
  maxWeight       Float         // Capacité maximale en kg
  maxVolume       Float?        // Capacité maximale en m3
  currentWeight   Float         @default(0)
  currentVolume   Float?        @default(0)
  loadingRate     Float         @default(0) // Taux de chargement %

  // Coûts consolidés
  totalTransportCost Float       @default(0)
  fuelSurcharge   Float?        @default(0)
  handlingFees    Float?        @default(0)

  // Notes et observations
  notes           String?
  driverName      String?
  driverPhone     String?
  vehicleInfo     String?       // Plaque, modèle

  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  orders          Order[]       // Commandes commerce
  shipmentRequests ShipmentRequest[] // Demandes logistique
  carrier         Carrier       @relation(fields: [carrierId], references: [id])
  trackingEvents  TrackingEvent[]
  shipmentTracking ShipmentTracking[] // Suivi temps réel

  @@map("shipments")
}

// Événements de suivi
model TrackingEvent {
  id          String    @id @default(cuid())
  shipmentId  String

  // Détails de l'événement
  eventType   TrackingEventType
  location    String
  description String
  timestamp   DateTime  @default(now())

  // Relations
  shipment    Shipment  @relation(fields: [shipmentId], references: [id])

  @@map("tracking_events")
}

// Évaluations des transporteurs
model CarrierEvaluation {
  id          String   @id @default(cuid())
  carrierId   String
  shipmentId  String?

  // Critères d'évaluation
  punctuality Int      // Note sur 5
  condition   Int      // État des marchandises sur 5
  communication Int    // Communication sur 5
  overall     Int      // Note globale sur 5

  // Commentaires
  comment     String?

  createdAt   DateTime @default(now())

  // Relations
  carrier     Carrier  @relation(fields: [carrierId], references: [id])

  @@map("carrier_evaluations")
}

// Modèle Demande de Transport (pour clients logistique)
model ShipmentRequest {
  id                String              @id @default(cuid())
  requestNumber     String              @unique
  customerId        String

  // Informations expédition
  originCity        String              // Lagos, Abuja, Kano
  destinationCity   String              @default("Dakar")
  description       String              // Description des marchandises
  weight            Float               // Poids en kg
  volume            Float?              // Volume en m3
  declaredValue     Float               // Valeur déclarée

  // Préférences transport
  transportMode     TransportMode
  urgency           UrgencyLevel        @default(STANDARD)
  insuranceRequired Boolean             @default(false)
  specialInstructions String?

  // Tarification
  estimatedCost     Float?
  finalCost         Float?
  insuranceCost     Float?              @default(0)
  handlingFees      Float?              @default(0)

  // Statut et suivi
  status            ShipmentRequestStatus @default(PENDING)
  assignedShipmentId String?

  // Dates
  requestedPickupDate DateTime?
  estimatedDelivery   DateTime?
  actualPickupDate    DateTime?
  actualDeliveryDate  DateTime?

  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relations
  customer          Customer            @relation(fields: [customerId], references: [id])
  assignedShipment  Shipment?           @relation(fields: [assignedShipmentId], references: [id])
  trackingEvents    ShipmentTracking[]

  @@map("shipment_requests")
}

enum UrgencyLevel {
  STANDARD    // 5-7 jours
  EXPRESS     // 2-3 jours
  URGENT      // 24-48h
}

enum ShipmentRequestStatus {
  PENDING         // En attente de validation
  QUOTED          // Devis envoyé
  CONFIRMED       // Confirmé par client
  ASSIGNED        // Assigné à une expédition
  IN_TRANSIT      // En transit
  DELIVERED       // Livré
  CANCELLED       // Annulé
}

// Modèle Évaluation Client
model CustomerEvaluation {
  id          String   @id @default(cuid())
  customerId  String
  orderId     String?
  shipmentRequestId String?

  // Évaluation
  serviceRating     Int      // 1-5
  deliveryRating    Int      // 1-5
  communicationRating Int    // 1-5
  overallRating     Int      // 1-5

  // Commentaires
  comment       String?
  improvements  String?

  createdAt     DateTime @default(now())

  // Relations
  customer      Customer @relation(fields: [customerId], references: [id])
  order         Order?   @relation(fields: [orderId], references: [id])

  @@map("customer_evaluations")
}

// Modèle Notifications Client
model CustomerNotification {
  id          String              @id @default(cuid())
  customerId  String

  // Contenu notification
  type        NotificationType
  title       String
  message     String

  // Statut
  isRead      Boolean             @default(false)
  sentAt      DateTime            @default(now())
  readAt      DateTime?

  // Métadonnées
  relatedOrderId String?
  relatedShipmentId String?

  // Relations
  customer    Customer            @relation(fields: [customerId], references: [id])

  @@map("customer_notifications")
}

enum NotificationType {
  ORDER_CONFIRMATION
  SHIPMENT_UPDATE
  DELIVERY_NOTIFICATION
  PAYMENT_REMINDER
  PROMOTION
  SYSTEM_ALERT
}

// Modèle Tarification Transport
model TransportPricing {
  id              String        @id @default(cuid())

  // Configuration tarifaire
  name            String        // Nom du tarif (ex: "Standard Route", "Express Air")
  transportMode   TransportMode
  originCity      SupplierCity
  destinationCity String        @default("DAKAR")

  // Tarifs par poids/volume
  pricePerKg      Float         // Prix par kg
  pricePerM3      Float?        // Prix par m3 (optionnel)
  minimumCharge   Float         // Frais minimum

  // Frais additionnels
  handlingFee     Float         @default(0)
  fuelSurcharge   Float         @default(0) // %
  insuranceRate   Float         @default(0.5) // % de la valeur déclarée

  // Conditions
  maxWeight       Float?        // Poids maximum accepté
  maxVolume       Float?        // Volume maximum accepté

  // Délais
  estimatedDays   Int           // Délai estimé en jours

  // Tarifs dégressifs
  bulkDiscounts   BulkDiscount[]

  // Statut
  isActive        Boolean       @default(true)
  validFrom       DateTime      @default(now())
  validUntil      DateTime?

  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  @@map("transport_pricing")
}

// Modèle Remises Volume
model BulkDiscount {
  id              String          @id @default(cuid())
  pricingId       String

  // Seuils
  minWeight       Float           // Poids minimum pour la remise
  maxWeight       Float?          // Poids maximum (optionnel)

  // Remise
  discountType    DiscountType    // Pourcentage ou montant fixe
  discountValue   Float           // Valeur de la remise

  // Relations
  pricing         TransportPricing @relation(fields: [pricingId], references: [id])

  @@map("bulk_discounts")
}

enum DiscountType {
  PERCENTAGE  // Remise en pourcentage
  FIXED       // Montant fixe de remise
}

// Taux de change
model ExchangeRate {
  id        String   @id @default(cuid())
  fromCurrency String // NGN
  toCurrency   String // XOF
  rate         Float
  createdAt    DateTime @default(now())

  @@map("exchange_rates")
}

// Enums
enum SupplierCity {
  LAGOS
  ABUJA
  KANO
}

enum ProductCategory {
  TISSUS
  COSMETIQUES
  MECHES
}

enum OrderStatus {
  PENDING     // En attente
  CONFIRMED   // Confirmée
  SHIPPED     // Expédiée
  DELIVERED   // Livrée
  CANCELLED   // Annulée
}

enum TransportMode {
  ROAD        // Transport routier (5-7 jours)
  AIR_EXPRESS // Fret aérien express (24-48h)
}

enum ShipmentType {
  COMMERCE_ONLY   // Uniquement commandes commerce
  LOGISTICS_ONLY  // Uniquement demandes logistique
  MIXED          // Mix des deux types
}

enum ShipmentStatus {
  PENDING     // En attente d'enlèvement
  PICKED_UP   // Marchandise enlevée
  IN_TRANSIT  // En transit
  CUSTOMS     // En douane
  OUT_FOR_DELIVERY // En cours de livraison
  DELIVERED   // Livrée
  DELAYED     // Retardée
  CANCELLED   // Annulée
}

enum TrackingEventType {
  PICKUP_SCHEDULED  // Enlèvement programmé
  PICKED_UP        // Marchandise enlevée
  DEPARTED         // Départ du centre
  IN_TRANSIT       // En transit
  ARRIVED_HUB      // Arrivée au hub
  CUSTOMS_CLEARANCE // Dédouanement
  OUT_FOR_DELIVERY // En cours de livraison
  DELIVERED        // Livrée
  DELIVERY_FAILED  // Échec de livraison
  DELAYED          // Retard signalé
}

// Modèle Suivi Temps Réel Avancé
model ShipmentTracking {
  id              String              @id @default(cuid())
  shipmentId      String?
  shipmentRequestId String?

  // Informations de localisation
  eventType       TrackingEventType
  location        String
  gpsLatitude     Float?
  gpsLongitude    Float?
  address         String?

  // Détails de l'événement
  description     String
  notes           String?

  // Métadonnées
  timestamp       DateTime            @default(now())
  estimatedNext   DateTime?           // Prochaine étape estimée

  // Photos et documents (JSON pour SQLite)
  photoUrls       String?             // JSON array des URLs des photos
  documentUrls    String?             // JSON array des URLs des documents

  // Signature électronique (pour livraison)
  recipientName   String?
  recipientPhone  String?
  signatureUrl    String?             // URL de la signature

  // Informations du responsable
  handlerName     String?             // Nom du responsable
  handlerPhone    String?

  // Relations
  shipment        Shipment?           @relation(fields: [shipmentId], references: [id])
  shipmentRequest ShipmentRequest?    @relation(fields: [shipmentRequestId], references: [id])

  @@map("shipment_tracking")
}
