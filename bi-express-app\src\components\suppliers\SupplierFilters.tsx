'use client'

import { useState } from 'react'
import { Search, Filter } from 'lucide-react'

export function SupplierFilters() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCity, setSelectedCity] = useState('')
  const [minRating, setMinRating] = useState('')

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-4">
        {/* Search */}
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Rechercher un fournisseur..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* City Filter */}
        <div className="min-w-0 flex-shrink-0">
          <select
            value={selectedCity}
            onChange={(e) => setSelectedCity(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Toutes les villes</option>
            <option value="LAGOS">Lagos</option>
            <option value="ABUJA">Abuja</option>
            <option value="KANO">Kano</option>
          </select>
        </div>

        {/* Rating Filter */}
        <div className="min-w-0 flex-shrink-0">
          <select
            value={minRating}
            onChange={(e) => setMinRating(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Toutes les notes</option>
            <option value="4">4+ étoiles</option>
            <option value="3">3+ étoiles</option>
            <option value="2">2+ étoiles</option>
          </select>
        </div>

        {/* Filter Button */}
        <button className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
          <Filter className="h-4 w-4 mr-2" />
          Filtrer
        </button>
      </div>

      {/* Active Filters */}
      {(searchTerm || selectedCity || minRating) && (
        <div className="mt-4 flex items-center space-x-2">
          <span className="text-sm text-gray-500">Filtres actifs:</span>
          {searchTerm && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Recherche: {searchTerm}
              <button
                onClick={() => setSearchTerm('')}
                className="ml-1 text-blue-600 hover:text-blue-800"
              >
                ×
              </button>
            </span>
          )}
          {selectedCity && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Ville: {selectedCity}
              <button
                onClick={() => setSelectedCity('')}
                className="ml-1 text-green-600 hover:text-green-800"
              >
                ×
              </button>
            </span>
          )}
          {minRating && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
              Note: {minRating}+ étoiles
              <button
                onClick={() => setMinRating('')}
                className="ml-1 text-yellow-600 hover:text-yellow-800"
              >
                ×
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  )
}
