{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: 'NGN' | 'XOF' = 'XOF'): string {\n  const symbols = {\n    NGN: '₦',\n    XOF: 'CFA'\n  }\n  \n  return `${amount.toLocaleString('fr-FR')} ${symbols[currency]}`\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric'\n  }).format(date)\n}\n\nexport function formatDateTime(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  }).format(date)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAA0B,KAAK;IAC5E,MAAM,UAAU;QACd,KAAK;QACL,KAAK;IACP;IAEA,OAAO,GAAG,OAAO,cAAc,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,SAAS,EAAE;AACjE;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;IACR,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAU;IACvC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'\n  size?: 'default' | 'sm' | 'lg' | 'icon'\n}\n\nexport function Button({ \n  className, \n  variant = 'default', \n  size = 'default', \n  ...props \n}: ButtonProps) {\n  const variantClasses = {\n    default: 'bg-blue-600 text-white hover:bg-blue-700',\n    destructive: 'bg-red-600 text-white hover:bg-red-700',\n    outline: 'border border-gray-300 bg-transparent hover:bg-gray-50',\n    secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200',\n    ghost: 'hover:bg-gray-100',\n    link: 'text-blue-600 underline-offset-4 hover:underline'\n  }\n\n  const sizeClasses = {\n    default: 'h-10 px-4 py-2',\n    sm: 'h-9 rounded-md px-3',\n    lg: 'h-11 rounded-md px-8',\n    icon: 'h-10 w-10'\n  }\n\n  return (\n    <button\n      className={cn(\n        'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none',\n        variantClasses[variant],\n        sizeClasses[size],\n        className\n      )}\n      {...props}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOO,SAAS,OAAO,EACrB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,GAAG,OACS;IACZ,MAAM,iBAAiB;QACrB,SAAS;QACT,aAAa;QACb,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iPACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/badge-component.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {\n  children: React.ReactNode\n  className?: string\n  variant?: 'default' | 'secondary' | 'destructive' | 'outline'\n}\n\nexport function Badge({ children, className, variant = 'default', ...props }: BadgeProps) {\n  const variantClasses = {\n    default: 'bg-blue-100 text-blue-800 border-blue-200',\n    secondary: 'bg-gray-100 text-gray-800 border-gray-200',\n    destructive: 'bg-red-100 text-red-800 border-red-200',\n    outline: 'bg-transparent text-gray-700 border-gray-300'\n  }\n\n  return (\n    <span \n      className={cn(\n        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border',\n        variantClasses[variant],\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAQO,SAAS,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAmB;IACtF,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,aAAa;QACb,SAAS;IACX;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kFACA,cAAc,CAAC,QAAQ,EACvB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/tabs.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Tabs = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx <module evaluation>\",\n    \"Tabs\",\n);\nexport const TabsContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsContent() from the server but TabsContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx <module evaluation>\",\n    \"TabsContent\",\n);\nexport const TabsList = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsList() from the server but <PERSON><PERSON>List is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx <module evaluation>\",\n    \"TabsList\",\n);\nexport const TabsTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsTrigger() from the server but TabsTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx <module evaluation>\",\n    \"TabsTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,4DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,4DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4DACA", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/tabs.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Tabs = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx\",\n    \"Tabs\",\n);\nexport const TabsContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsContent() from the server but TabsContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx\",\n    \"TabsContent\",\n);\nexport const TabsList = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsList() from the server but TabsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx\",\n    \"TabsList\",\n);\nexport const TabsTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsTrigger() from the server but TabsTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/tabs.tsx\",\n    \"TabsTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,wCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,wCACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,wCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,wCACA", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/logistics-client/page.tsx"], "sourcesContent": ["import { Suspense } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge-component'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { \n  Truck, \n  Package, \n  Clock, \n  MapPin, \n  Calculator,\n  FileText,\n  Star,\n  Plus,\n  TrendingUp\n} from 'lucide-react'\n\n// Données de démonstration\nconst recentShipments = [\n  {\n    id: 'SH-2024-001',\n    description: 'Équipements électroniques',\n    weight: 45.5,\n    origin: 'Lagos',\n    status: 'IN_TRANSIT',\n    estimatedDelivery: '2024-01-15',\n    cost: 85000\n  },\n  {\n    id: 'SH-2024-002', \n    description: 'Pièces automobiles',\n    weight: 120.0,\n    origin: 'Kano',\n    status: 'DELIVERED',\n    estimatedDelivery: '2024-01-10',\n    cost: 156000\n  },\n  {\n    id: 'SH-2024-003',\n    description: 'Produits pharmaceutiques',\n    weight: 25.0,\n    origin: 'Abuja',\n    status: 'PENDING',\n    estimatedDelivery: '2024-01-20',\n    cost: 95000\n  }\n]\n\nconst stats = {\n  totalShipments: 24,\n  totalWeight: 2450,\n  totalSpent: 2850000,\n  averageRating: 4.8\n}\n\nexport default function LogisticsClientPage() {\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      PENDING: { label: 'En attente', color: 'bg-yellow-100 text-yellow-800' },\n      IN_TRANSIT: { label: 'En transit', color: 'bg-blue-100 text-blue-800' },\n      DELIVERED: { label: 'Livré', color: 'bg-green-100 text-green-800' },\n      CANCELLED: { label: 'Annulé', color: 'bg-red-100 text-red-800' }\n    }\n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING\n    return <Badge className={config.color}>{config.label}</Badge>\n  }\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'XOF',\n      minimumFractionDigits: 0\n    }).format(amount)\n  }\n\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* En-tête */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            Espace Client Logistique\n          </h1>\n          <p className=\"text-gray-600\">\n            Gérez vos expéditions et suivez vos transports en temps réel\n          </p>\n        </div>\n        <Button className=\"flex items-center\">\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Nouvelle demande\n        </Button>\n      </div>\n\n      {/* Statistiques */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <Package className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total expéditions</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.totalShipments}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <TrendingUp className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Poids total</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.totalWeight.toLocaleString()} kg</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <FileText className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total dépensé</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {formatCurrency(stats.totalSpent)}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-yellow-100 rounded-lg\">\n                <Star className=\"h-6 w-6 text-yellow-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Note moyenne</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.averageRating}/5</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Contenu principal */}\n      <Tabs defaultValue=\"shipments\" className=\"space-y-6\">\n        <TabsList>\n          <TabsTrigger value=\"shipments\">Mes expéditions</TabsTrigger>\n          <TabsTrigger value=\"calculator\">Calculateur</TabsTrigger>\n          <TabsTrigger value=\"invoices\">Factures</TabsTrigger>\n          <TabsTrigger value=\"tracking\">Suivi</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"shipments\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Expéditions récentes</CardTitle>\n              <CardDescription>\n                Historique de vos dernières demandes de transport\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {recentShipments.map((shipment) => (\n                  <div key={shipment.id} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex justify-between items-start\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-3 mb-2\">\n                          <h3 className=\"font-medium text-gray-900\">{shipment.id}</h3>\n                          {getStatusBadge(shipment.status)}\n                        </div>\n                        <p className=\"text-gray-600 mb-2\">{shipment.description}</p>\n                        <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                          <div className=\"flex items-center\">\n                            <MapPin className=\"h-4 w-4 mr-1\" />\n                            {shipment.origin} → Dakar\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Package className=\"h-4 w-4 mr-1\" />\n                            {shipment.weight} kg\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Clock className=\"h-4 w-4 mr-1\" />\n                            {new Date(shipment.estimatedDelivery).toLocaleDateString('fr-FR')}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"font-bold text-lg text-gray-900\">\n                          {formatCurrency(shipment.cost)}\n                        </div>\n                        <Button variant=\"outline\" size=\"sm\" className=\"mt-2\">\n                          Détails\n                        </Button>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"calculator\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Calculator className=\"h-5 w-5 mr-2\" />\n                Calculateur de tarifs\n              </CardTitle>\n              <CardDescription>\n                Obtenez un devis instantané pour vos expéditions\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                {/* Formulaire de calcul */}\n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Route\n                    </label>\n                    <select className=\"w-full p-2 border border-gray-300 rounded-md\" aria-label=\"Sélectionner une route\">\n                      <option>Lagos → Dakar</option>\n                      <option>Abuja → Dakar</option>\n                      <option>Kano → Dakar</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Poids (kg)\n                    </label>\n                    <input\n                      type=\"number\"\n                      placeholder=\"Ex: 100\"\n                      className=\"w-full p-2 border border-gray-300 rounded-md\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Mode de transport\n                    </label>\n                    <div className=\"grid grid-cols-2 gap-2\">\n                      <button type=\"button\" className=\"p-3 border border-blue-500 bg-blue-50 rounded-md text-sm\">\n                        🚛 Routier\n                      </button>\n                      <button type=\"button\" className=\"p-3 border border-gray-300 rounded-md text-sm\">\n                        ✈️ Aérien\n                      </button>\n                    </div>\n                  </div>\n                  <Button className=\"w-full\">\n                    Calculer le tarif\n                  </Button>\n                </div>\n\n                {/* Résultat */}\n                <div className=\"bg-green-50 border border-green-200 rounded-lg p-6\">\n                  <h3 className=\"font-bold text-lg text-green-800 mb-4\">Estimation</h3>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between\">\n                      <span>Transport de base:</span>\n                      <span className=\"font-medium\">85,000 CFA</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Frais de manutention:</span>\n                      <span className=\"font-medium\">4,250 CFA</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Surcharge carburant:</span>\n                      <span className=\"font-medium\">10,200 CFA</span>\n                    </div>\n                    <hr className=\"my-2\" />\n                    <div className=\"flex justify-between font-bold text-lg\">\n                      <span>Total:</span>\n                      <span className=\"text-green-700\">99,450 CFA</span>\n                    </div>\n                    <p className=\"text-sm text-green-600 mt-2\">\n                      Délai estimé: 6 jours\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"invoices\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <FileText className=\"h-5 w-5 mr-2\" />\n                Factures et paiements\n              </CardTitle>\n              <CardDescription>\n                Consultez vos factures et l'historique des paiements\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-center py-8\">\n                <FileText className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" />\n                <p className=\"text-gray-500 mb-4\">\n                  Aucune facture disponible pour le moment\n                </p>\n                <Button variant=\"outline\">\n                  Voir toutes les factures\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"tracking\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Truck className=\"h-5 w-5 mr-2\" />\n                Suivi en temps réel\n              </CardTitle>\n              <CardDescription>\n                Suivez vos expéditions en cours en temps réel\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-center py-8\">\n                <Truck className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" />\n                <p className=\"text-gray-500 mb-4\">\n                  Aucune expédition en cours de suivi\n                </p>\n                <Button variant=\"outline\">\n                  Rechercher par numéro\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n\n      {/* Actions rapides */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Actions rapides</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <Button variant=\"outline\" className=\"h-20 flex-col\">\n              <Plus className=\"h-6 w-6 mb-2\" />\n              Nouvelle demande\n            </Button>\n            <Button variant=\"outline\" className=\"h-20 flex-col\">\n              <Calculator className=\"h-6 w-6 mb-2\" />\n              Calculer un tarif\n            </Button>\n            <Button variant=\"outline\" className=\"h-20 flex-col\">\n              <Truck className=\"h-6 w-6 mb-2\" />\n              Suivre une expédition\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n\nexport const metadata = {\n  title: 'Espace Client Logistique - Bi-Express',\n  description: 'Dashboard client pour la gestion des expéditions et transports'\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;AAYA,2BAA2B;AAC3B,MAAM,kBAAkB;IACtB;QACE,IAAI;QACJ,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,mBAAmB;QACnB,MAAM;IACR;IACA;QACE,IAAI;QACJ,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,mBAAmB;QACnB,MAAM;IACR;IACA;QACE,IAAI;QACJ,aAAa;QACb,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,mBAAmB;QACnB,MAAM;IACR;CACD;AAED,MAAM,QAAQ;IACZ,gBAAgB;IAChB,aAAa;IACb,YAAY;IACZ,eAAe;AACjB;AAEe,SAAS;IACtB,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,SAAS;gBAAE,OAAO;gBAAc,OAAO;YAAgC;YACvE,YAAY;gBAAE,OAAO;gBAAc,OAAO;YAA4B;YACtE,WAAW;gBAAE,OAAO;gBAAS,OAAO;YAA8B;YAClE,WAAW;gBAAE,OAAO;gBAAU,OAAO;YAA0B;QACjE;QACA,MAAM,SAAS,YAAY,CAAC,OAAoC,IAAI,aAAa,OAAO;QACxF,qBAAO,8OAAC,8IAAA,CAAA,QAAK;YAAC,WAAW,OAAO,KAAK;sBAAG,OAAO,KAAK;;;;;;IACtD;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAI/B,8OAAC,kIAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAoC,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7E,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;oDAAoC,MAAM,WAAW,CAAC,cAAc;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM5F,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,eAAe,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO1C,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;oDAAoC,MAAM,aAAa;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/E,8OAAC,gIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAY,WAAU;;kCACvC,8OAAC,gIAAA,CAAA,WAAQ;;0CACP,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAY;;;;;;0CAC/B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAa;;;;;;0CAChC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;0CAC9B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;;;;;;;kCAGhC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACZ,gBAAgB,GAAG,CAAC,CAAC,yBACpB,8OAAC;gDAAsB,WAAU;0DAC/B,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAA6B,SAAS,EAAE;;;;;;wEACrD,eAAe,SAAS,MAAM;;;;;;;8EAEjC,8OAAC;oEAAE,WAAU;8EAAsB,SAAS,WAAW;;;;;;8EACvD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFACjB,SAAS,MAAM;gFAAC;;;;;;;sFAEnB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,wMAAA,CAAA,UAAO;oFAAC,WAAU;;;;;;gFAClB,SAAS,MAAM;gFAAC;;;;;;;sFAEnB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,oMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;gFAChB,IAAI,KAAK,SAAS,iBAAiB,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;sEAI/D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACZ,eAAe,SAAS,IAAI;;;;;;8EAE/B,8OAAC,kIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAU,MAAK;oEAAK,WAAU;8EAAO;;;;;;;;;;;;;;;;;;+CA3BjD,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCAuC/B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,8MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGzC,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEAAO,WAAU;gEAA+C,cAAW;;kFAC1E,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;;;;;;;;;;;;;kEAGZ,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,aAAY;gEACZ,WAAU;;;;;;;;;;;;kEAGd,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAO,MAAK;wEAAS,WAAU;kFAA2D;;;;;;kFAG3F,8OAAC;wEAAO,MAAK;wEAAS,WAAU;kFAAgD;;;;;;;;;;;;;;;;;;kEAKpF,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;kEAAS;;;;;;;;;;;;0DAM7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAK;;;;;;kFACN,8OAAC;wEAAK,WAAU;kFAAc;;;;;;;;;;;;0EAEhC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAK;;;;;;kFACN,8OAAC;wEAAK,WAAU;kFAAc;;;;;;;;;;;;0EAEhC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAK;;;;;;kFACN,8OAAC;wEAAK,WAAU;kFAAc;;;;;;;;;;;;0EAEhC,8OAAC;gEAAG,WAAU;;;;;;0EACd,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAK;;;;;;kFACN,8OAAC;wEAAK,WAAU;kFAAiB;;;;;;;;;;;;0EAEnC,8OAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvD,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQlC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,8OAAC,8MAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGzC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf", "debugId": null}}]}