import { TransportMode, SupplierCity, ShipmentStatus } from '@/types'

/**
 * Calcule les coûts de transport selon le mode et le poids
 */
export function calculateTransportCosts(
  weight: number,
  mode: TransportMode,
  originCity: SupplierCity,
  fuelSurcharge: number = 0
): number {
  // Tarifs de base par kg selon le mode de transport
  const baseRates = {
    ROAD: {
      LAGOS: 500,   // 500 XOF par kg depuis Lagos
      ABUJA: 600,   // 600 XOF par kg depuis Abuja
      KANO: 700     // 700 XOF par kg depuis Kano
    },
    AIR_EXPRESS: {
      LAGOS: 2000,  // 2000 XOF par kg depuis Lagos
      ABUJA: 2200,  // 2200 XOF par kg depuis Abuja
      KANO: 2500    // 2500 XOF par kg depuis Kano
    }
  }

  const baseRate = baseRates[mode][originCity]
  const baseCost = weight * baseRate
  const fuelCost = baseCost * (fuelSurcharge / 100)
  
  return baseCost + fuelCost
}

/**
 * Calcule la durée estimée de livraison
 */
export function calculateDeliveryTime(
  mode: TransportMode,
  originCity: SupplierCity,
  orderDate: Date = new Date()
): Date {
  // Durées en heures selon le mode et la ville d'origine
  const deliveryTimes = {
    ROAD: {
      LAGOS: 120,   // 5 jours
      ABUJA: 144,   // 6 jours
      KANO: 168     // 7 jours
    },
    AIR_EXPRESS: {
      LAGOS: 24,    // 1 jour
      ABUJA: 36,    // 1.5 jours
      KANO: 48      // 2 jours
    }
  }

  const hoursToAdd = deliveryTimes[mode][originCity]
  const estimatedDelivery = new Date(orderDate)
  estimatedDelivery.setHours(estimatedDelivery.getHours() + hoursToAdd)
  
  return estimatedDelivery
}

/**
 * Génère un numéro de suivi unique
 */
export function generateTrackingNumber(
  mode: TransportMode,
  originCity: SupplierCity
): string {
  const prefix = mode === 'ROAD' ? 'RD' : 'AE'
  const cityCode = originCity.substring(0, 2)
  const timestamp = Date.now().toString().slice(-6)
  const random = Math.random().toString(36).substring(2, 5).toUpperCase()
  
  return `${prefix}${cityCode}${timestamp}${random}`
}

/**
 * Détermine le statut de livraison basé sur les dates
 */
export function getDeliveryStatus(
  estimatedDelivery: Date,
  actualDelivery?: Date
): 'ON_TIME' | 'DELAYED' | 'EARLY' | 'PENDING' {
  if (!actualDelivery) return 'PENDING'
  
  const timeDiff = actualDelivery.getTime() - estimatedDelivery.getTime()
  const hoursDiff = timeDiff / (1000 * 60 * 60)
  
  if (hoursDiff <= 0) return 'EARLY'
  if (hoursDiff <= 24) return 'ON_TIME' // Tolérance de 24h
  return 'DELAYED'
}

/**
 * Calcule le taux de ponctualité d'un transporteur
 */
export function calculateOnTimeRate(
  totalDeliveries: number,
  onTimeDeliveries: number
): number {
  if (totalDeliveries === 0) return 0
  return Math.round((onTimeDeliveries / totalDeliveries) * 100)
}

/**
 * Obtient la couleur du statut d'expédition
 */
export function getShipmentStatusColor(status: ShipmentStatus): string {
  const colors = {
    PENDING: 'bg-gray-100 text-gray-800',
    PICKED_UP: 'bg-blue-100 text-blue-800',
    IN_TRANSIT: 'bg-yellow-100 text-yellow-800',
    CUSTOMS: 'bg-orange-100 text-orange-800',
    OUT_FOR_DELIVERY: 'bg-purple-100 text-purple-800',
    DELIVERED: 'bg-green-100 text-green-800',
    DELAYED: 'bg-red-100 text-red-800',
    CANCELLED: 'bg-gray-100 text-gray-800'
  }
  
  return colors[status] || 'bg-gray-100 text-gray-800'
}

/**
 * Obtient le libellé français du statut d'expédition
 */
export function getShipmentStatusLabel(status: ShipmentStatus): string {
  const labels = {
    PENDING: 'En attente',
    PICKED_UP: 'Enlevée',
    IN_TRANSIT: 'En transit',
    CUSTOMS: 'En douane',
    OUT_FOR_DELIVERY: 'En livraison',
    DELIVERED: 'Livrée',
    DELAYED: 'Retardée',
    CANCELLED: 'Annulée'
  }
  
  return labels[status] || status
}

/**
 * Calcule les statistiques logistiques
 */
export function calculateLogisticsKPIs(shipments: any[]) {
  const total = shipments.length
  const delivered = shipments.filter(s => s.status === 'DELIVERED').length
  const inTransit = shipments.filter(s => 
    ['PICKED_UP', 'IN_TRANSIT', 'CUSTOMS', 'OUT_FOR_DELIVERY'].includes(s.status)
  ).length
  
  // Calcul du temps moyen de livraison (en heures)
  const deliveredShipments = shipments.filter(s => s.deliveryDate && s.pickupDate)
  const averageDeliveryTime = deliveredShipments.length > 0
    ? deliveredShipments.reduce((sum, s) => {
        const diff = new Date(s.deliveryDate).getTime() - new Date(s.pickupDate).getTime()
        return sum + (diff / (1000 * 60 * 60))
      }, 0) / deliveredShipments.length
    : 0
  
  // Calcul du taux de ponctualité
  const onTimeDeliveries = deliveredShipments.filter(s => {
    const status = getDeliveryStatus(new Date(s.estimatedDelivery), new Date(s.deliveryDate))
    return status === 'ON_TIME' || status === 'EARLY'
  }).length
  
  const onTimeRate = deliveredShipments.length > 0
    ? (onTimeDeliveries / deliveredShipments.length) * 100
    : 0
  
  // Coût total de transport
  const totalTransportCosts = shipments.reduce((sum, s) => sum + (s.transportCost || 0), 0)
  
  return {
    totalShipments: total,
    inTransitShipments: inTransit,
    deliveredShipments: delivered,
    averageDeliveryTime: Math.round(averageDeliveryTime),
    onTimeDeliveryRate: Math.round(onTimeRate),
    totalTransportCosts
  }
}

/**
 * Valide les données d'une expédition
 */
export function validateShipmentData(data: any): string[] {
  const errors: string[] = []
  
  if (!data.orderId) errors.push('ID de commande requis')
  if (!data.carrierId) errors.push('Transporteur requis')
  if (!data.weight || data.weight <= 0) errors.push('Poids valide requis')
  if (!data.transportMode) errors.push('Mode de transport requis')
  if (!data.originCity) errors.push('Ville d\'origine requise')
  
  return errors
}

/**
 * Formate une durée en heures vers un texte lisible
 */
export function formatDuration(hours: number): string {
  if (hours < 24) {
    return `${Math.round(hours)}h`
  } else {
    const days = Math.floor(hours / 24)
    const remainingHours = Math.round(hours % 24)
    return remainingHours > 0 ? `${days}j ${remainingHours}h` : `${days}j`
  }
}
