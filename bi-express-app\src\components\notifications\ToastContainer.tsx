'use client'

import { useEffect, useState } from 'react'
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react'
import { useNotifications } from '@/contexts/NotificationContext'

export function ToastContainer() {
  const { notifications, removeNotification } = useNotifications()
  const [toasts, setToasts] = useState<typeof notifications>([])

  useEffect(() => {
    // Only show recent notifications as toasts (last 3 unread)
    const recentToasts = notifications
      .filter(n => !n.read)
      .slice(0, 3)
    
    setToasts(recentToasts)
  }, [notifications])

  const getIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'error': return <AlertCircle className="h-5 w-5 text-red-600" />
      case 'warning': return <AlertTriangle className="h-5 w-5 text-orange-600" />
      case 'info': return <Info className="h-5 w-5 text-blue-600" />
      default: return <Info className="h-5 w-5 text-gray-600" />
    }
  }

  const getToastClasses = (type: string) => {
    switch (type) {
      case 'success': return 'bg-green-50 border-green-200 text-green-800'
      case 'error': return 'bg-red-50 border-red-200 text-red-800'
      case 'warning': return 'bg-orange-50 border-orange-200 text-orange-800'
      case 'info': return 'bg-blue-50 border-blue-200 text-blue-800'
      default: return 'bg-gray-50 border-gray-200 text-gray-800'
    }
  }

  if (toasts.length === 0) return null

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map((toast, index) => (
        <div
          key={toast.id}
          className={`
            max-w-sm w-full rounded-lg border p-4 shadow-lg transform transition-all duration-300 ease-in-out
            ${getToastClasses(toast.type)}
            ${index === 0 ? 'translate-x-0 opacity-100' : 'translate-x-2 opacity-90'}
          `}
          style={{
            animationDelay: `${index * 100}ms`,
            marginTop: index > 0 ? '8px' : '0'
          }}
        >
          <div className="flex items-start gap-3">
            {getIcon(toast.type)}
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-medium">
                {toast.title}
              </h4>
              {toast.message && (
                <p className="text-sm mt-1 opacity-90">
                  {toast.message}
                </p>
              )}
              {toast.action && (
                <button
                  onClick={toast.action.onClick}
                  className="text-sm font-medium underline mt-2 hover:no-underline"
                >
                  {toast.action.label}
                </button>
              )}
            </div>
            <button
              onClick={() => removeNotification(toast.id)}
              className="flex-shrink-0 text-current opacity-60 hover:opacity-100 transition-opacity"
              title="Fermer"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      ))}
    </div>
  )
}
