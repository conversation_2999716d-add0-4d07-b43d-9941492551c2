import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')

    if (!query || query.length < 2) {
      return NextResponse.json({ results: [] })
    }

    const searchTerm = query.toLowerCase()

    // Pour SQLite, nous devons utiliser une approche différente
    // Récupérons toutes les données et filtrons côté application
    const [allSuppliers, allCustomers, allProducts, allOrders, allShipments] = await Promise.all([
      // Récupération fournisseurs
      prisma.supplier.findMany({
        take: 100,
        select: {
          id: true,
          name: true,
          city: true,
          specialties: true,
          rating: true,
          email: true
        }
      }),

      // Récupération clients
      prisma.customer.findMany({
        take: 100,
        select: {
          id: true,
          name: true,
          city: true,
          type: true,
          phone: true,
          email: true
        }
      }),

      // Récupération produits
      prisma.product.findMany({
        take: 100,
        include: {
          supplier: {
            select: {
              name: true
            }
          }
        }
      }),

      // Récupération commandes
      prisma.order.findMany({
        take: 100,
        include: {
          customer: {
            select: {
              name: true
            }
          }
        }
      }),

      // Récupération expéditions
      prisma.shipment.findMany({
        take: 100,
        select: {
          id: true,
          trackingNumber: true,
          status: true,
          transportMode: true,
          originCity: true,
          destinationCity: true
        }
      })
    ])

    // Filtrage côté application pour SQLite
    const suppliers = allSuppliers.filter(supplier =>
      supplier.name.toLowerCase().includes(searchTerm) ||
      supplier.city.toLowerCase().includes(searchTerm) ||
      supplier.specialties?.toLowerCase().includes(searchTerm) ||
      supplier.email?.toLowerCase().includes(searchTerm)
    ).slice(0, 5)

    const customers = allCustomers.filter(customer =>
      customer.name.toLowerCase().includes(searchTerm) ||
      customer.city.toLowerCase().includes(searchTerm) ||
      customer.email?.toLowerCase().includes(searchTerm) ||
      customer.phone.toLowerCase().includes(searchTerm)
    ).slice(0, 5)

    const products = allProducts.filter(product =>
      product.name.toLowerCase().includes(searchTerm) ||
      product.category.toLowerCase().includes(searchTerm) ||
      product.description?.toLowerCase().includes(searchTerm)
    ).slice(0, 5)

    const orders = allOrders.filter(order =>
      order.orderNumber.toLowerCase().includes(searchTerm) ||
      order.status.toLowerCase().includes(searchTerm) ||
      order.customer.name.toLowerCase().includes(searchTerm)
    ).slice(0, 5)

    const shipments = allShipments.filter(shipment =>
      shipment.trackingNumber.toLowerCase().includes(searchTerm) ||
      shipment.status.toLowerCase().includes(searchTerm) ||
      shipment.transportMode.toLowerCase().includes(searchTerm) ||
      shipment.originCity.toLowerCase().includes(searchTerm) ||
      shipment.destinationCity.toLowerCase().includes(searchTerm)
    ).slice(0, 5)

    // Formater les résultats
    const results = [
      // Fournisseurs
      ...suppliers.map(supplier => ({
        id: supplier.id,
        type: 'supplier' as const,
        title: supplier.name,
        subtitle: `${supplier.city} • ${supplier.specialties || 'Fournisseur'}`,
        description: `Note: ${supplier.rating}/5`,
        url: `/suppliers?highlight=${supplier.id}`
      })),

      // Clients
      ...customers.map(customer => ({
        id: customer.id,
        type: 'customer' as const,
        title: customer.name,
        subtitle: `${customer.city} • ${customer.type}`,
        description: customer.phone,
        url: `/customers?highlight=${customer.id}`
      })),

      // Produits
      ...products.map(product => ({
        id: product.id,
        type: 'product' as const,
        title: product.name,
        subtitle: `${product.category} • ${product.supplier?.name || 'Sans fournisseur'}`,
        description: `${product.supplierPrice.toLocaleString()} NGN`,
        url: `/products?highlight=${product.id}`
      })),

      // Commandes
      ...orders.map(order => ({
        id: order.id,
        type: 'order' as const,
        title: order.orderNumber,
        subtitle: `${order.customer?.name || 'Client inconnu'} • ${order.status}`,
        description: `${order.totalAmount.toLocaleString()} NGN`,
        url: `/orders?highlight=${order.id}`
      })),

      // Expéditions
      ...shipments.map(shipment => ({
        id: shipment.id,
        type: 'shipment' as const,
        title: shipment.trackingNumber,
        subtitle: `${shipment.originCity} → ${shipment.destinationCity}`,
        description: `${shipment.transportMode} • ${shipment.status}`,
        url: `/shipments?highlight=${shipment.id}`
      }))
    ]

    // Trier par pertinence (exact match en premier)
    const sortedResults = results.sort((a, b) => {
      const aExact = a.title.toLowerCase().includes(searchTerm)
      const bExact = b.title.toLowerCase().includes(searchTerm)
      
      if (aExact && !bExact) return -1
      if (!aExact && bExact) return 1
      return 0
    })

    return NextResponse.json({ 
      results: sortedResults.slice(0, 20), // Limiter à 20 résultats
      total: sortedResults.length 
    })

  } catch (error) {
    console.error('Erreur recherche globale:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la recherche' },
      { status: 500 }
    )
  }
}
