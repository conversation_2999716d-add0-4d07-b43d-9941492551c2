import { Suspense } from 'react'
import Link from 'next/link'
import { DashboardStats } from '@/components/dashboard/DashboardStats'
import { RecentOrders } from '@/components/dashboard/RecentOrders'
import { SalesChart } from '@/components/dashboard/SalesChart'
import { TopProducts } from '@/components/dashboard/TopProducts'
import { ExchangeRateWidget } from '@/components/currency/ExchangeRateWidget'
import { Button } from '@/components/ui/button'
import {
  ShoppingCart,
  Users,
  Package,
  Truck,
  FileText,
  Calculator
} from 'lucide-react'

export default function Dashboard() {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-1">
          Vue d'ensemble de votre activité de vente en gros Nigeria-Dakar
        </p>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="mb-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Actions Rapides</h2>
          <p className="text-sm text-gray-600">
            Accès direct aux fonctionnalités principales de l'application
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          <Link href="/orders/new">
            <Button className="w-full h-auto p-4 flex flex-col items-center space-y-2 bg-blue-500 hover:bg-blue-600 text-white border-0">
              <ShoppingCart className="h-6 w-6" />
              <div className="text-center">
                <div className="font-medium text-sm">Nouvelle Commande</div>
                <div className="text-xs opacity-90 mt-1">Créer une nouvelle commande</div>
              </div>
            </Button>
          </Link>

          <Link href="/suppliers/new">
            <Button className="w-full h-auto p-4 flex flex-col items-center space-y-2 bg-green-500 hover:bg-green-600 text-white border-0">
              <Users className="h-6 w-6" />
              <div className="text-center">
                <div className="font-medium text-sm">Ajouter Fournisseur</div>
                <div className="text-xs opacity-90 mt-1">Nouveau fournisseur</div>
              </div>
            </Button>
          </Link>

          <Link href="/products/new">
            <Button className="w-full h-auto p-4 flex flex-col items-center space-y-2 bg-purple-500 hover:bg-purple-600 text-white border-0">
              <Package className="h-6 w-6" />
              <div className="text-center">
                <div className="font-medium text-sm">Ajouter Produit</div>
                <div className="text-xs opacity-90 mt-1">Nouveau produit</div>
              </div>
            </Button>
          </Link>

          <Link href="/shipments/new">
            <Button className="w-full h-auto p-4 flex flex-col items-center space-y-2 bg-orange-500 hover:bg-orange-600 text-white border-0">
              <Truck className="h-6 w-6" />
              <div className="text-center">
                <div className="font-medium text-sm">Nouvelle Expédition</div>
                <div className="text-xs opacity-90 mt-1">Organiser expédition</div>
              </div>
            </Button>
          </Link>

          <Link href="/reports">
            <Button className="w-full h-auto p-4 flex flex-col items-center space-y-2 bg-indigo-500 hover:bg-indigo-600 text-white border-0">
              <FileText className="h-6 w-6" />
              <div className="text-center">
                <div className="font-medium text-sm">Rapports</div>
                <div className="text-xs opacity-90 mt-1">Générer rapport</div>
              </div>
            </Button>
          </Link>

          <Link href="/tools/calculator">
            <Button className="w-full h-auto p-4 flex flex-col items-center space-y-2 bg-teal-500 hover:bg-teal-600 text-white border-0">
              <Calculator className="h-6 w-6" />
              <div className="text-center">
                <div className="font-medium text-sm">Calculateur</div>
                <div className="text-xs opacity-90 mt-1">Calculer prix</div>
              </div>
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <Suspense fallback={<div className="animate-pulse h-32 bg-gray-200 rounded-lg" />}>
        <DashboardStats />
      </Suspense>

      {/* Charts and Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Suspense fallback={<div className="animate-pulse h-80 bg-gray-200 rounded-lg" />}>
            <SalesChart />
          </Suspense>

          <Suspense fallback={<div className="animate-pulse h-80 bg-gray-200 rounded-lg" />}>
            <TopProducts />
          </Suspense>
        </div>

        <div className="space-y-6">
          <Suspense fallback={<div className="animate-pulse h-32 bg-gray-200 rounded-lg" />}>
            <ExchangeRateWidget />
          </Suspense>
        </div>
      </div>

      {/* Recent Orders */}
      <Suspense fallback={<div className="animate-pulse h-64 bg-gray-200 rounded-lg" />}>
        <RecentOrders />
      </Suspense>
    </div>
  )
}
