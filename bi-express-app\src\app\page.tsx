import { Suspense } from 'react'
import { DashboardStats } from '@/components/dashboard/DashboardStats'
import { RecentOrders } from '@/components/dashboard/RecentOrders'
import { SalesChart } from '@/components/dashboard/SalesChart'
import { TopProducts } from '@/components/dashboard/TopProducts'
import { ExchangeRateWidget } from '@/components/currency/ExchangeRateWidget'

export default function Dashboard() {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-1">
          Vue d'ensemble de votre activité de vente en gros Nigeria-Dakar
        </p>
      </div>

      {/* Stats Cards */}
      <Suspense fallback={<div className="animate-pulse h-32 bg-gray-200 rounded-lg" />}>
        <DashboardStats />
      </Suspense>

      {/* Charts and Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Suspense fallback={<div className="animate-pulse h-80 bg-gray-200 rounded-lg" />}>
            <SalesChart />
          </Suspense>

          <Suspense fallback={<div className="animate-pulse h-80 bg-gray-200 rounded-lg" />}>
            <TopProducts />
          </Suspense>
        </div>

        <div className="space-y-6">
          <Suspense fallback={<div className="animate-pulse h-32 bg-gray-200 rounded-lg" />}>
            <ExchangeRateWidget />
          </Suspense>
        </div>
      </div>

      {/* Recent Orders */}
      <Suspense fallback={<div className="animate-pulse h-64 bg-gray-200 rounded-lg" />}>
        <RecentOrders />
      </Suspense>
    </div>
  )
}
