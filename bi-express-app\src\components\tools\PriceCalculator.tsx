'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Calculator, DollarSign, TrendingUp, Package } from 'lucide-react'

interface CalculationData {
  supplierPrice: number
  logisticsCost: number
  margin: number
  quantity: number
  transportMode: 'TRUCK' | 'AIR'
  weight: number
  exchangeRate: number
}

interface CalculationResult {
  totalCost: number
  sellingPrice: number
  totalProfit: number
  profitMargin: number
  pricePerUnit: number
  totalRevenue: number
}

export function PriceCalculator() {
  const [data, setData] = useState<CalculationData>({
    supplierPrice: 0,
    logisticsCost: 0,
    margin: 30,
    quantity: 1,
    transportMode: 'TRUCK',
    weight: 0,
    exchangeRate: 650 // NGN vers XOF
  })

  const [result, setResult] = useState<CalculationResult>({
    totalCost: 0,
    sellingPrice: 0,
    totalProfit: 0,
    profitMargin: 0,
    pricePerUnit: 0,
    totalRevenue: 0
  })

  const [savedCalculations, setSavedCalculations] = useState<Array<{
    id: string
    name: string
    data: CalculationData
    result: CalculationResult
    createdAt: string
  }>>([])

  // Calculer automatiquement quand les données changent
  useEffect(() => {
    calculatePrices()
  }, [data])

  // Charger les calculs sauvegardés
  useEffect(() => {
    const saved = localStorage.getItem('priceCalculations')
    if (saved) {
      setSavedCalculations(JSON.parse(saved))
    }
  }, [])

  const calculatePrices = () => {
    const { supplierPrice, logisticsCost, margin, quantity, transportMode, weight } = data

    // Coût de transport basé sur le mode et le poids
    const transportCost = transportMode === 'AIR' 
      ? weight * 150 // 150 FCFA par kg pour l'aérien
      : weight * 75   // 75 FCFA par kg pour le routier

    // Coût total par unité
    const totalCostPerUnit = supplierPrice + logisticsCost + (transportCost / quantity)
    
    // Prix de vente avec marge
    const sellingPricePerUnit = totalCostPerUnit * (1 + margin / 100)
    
    // Calculs totaux
    const totalCost = totalCostPerUnit * quantity
    const totalRevenue = sellingPricePerUnit * quantity
    const totalProfit = totalRevenue - totalCost
    const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0

    setResult({
      totalCost,
      sellingPrice: sellingPricePerUnit,
      totalProfit,
      profitMargin,
      pricePerUnit: sellingPricePerUnit,
      totalRevenue
    })
  }

  const handleInputChange = (field: keyof CalculationData, value: number | string) => {
    setData(prev => ({ ...prev, [field]: value }))
  }

  const saveCalculation = () => {
    const name = prompt('Nom du calcul:')
    if (!name) return

    const newCalculation = {
      id: Date.now().toString(),
      name,
      data: { ...data },
      result: { ...result },
      createdAt: new Date().toISOString()
    }

    const updated = [...savedCalculations, newCalculation]
    setSavedCalculations(updated)
    localStorage.setItem('priceCalculations', JSON.stringify(updated))
  }

  const loadCalculation = (calculation: typeof savedCalculations[0]) => {
    setData(calculation.data)
  }

  const deleteCalculation = (id: string) => {
    const updated = savedCalculations.filter(calc => calc.id !== id)
    setSavedCalculations(updated)
    localStorage.setItem('priceCalculations', JSON.stringify(updated))
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Formulaire de calcul */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Paramètres de calcul
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Prix fournisseur (FCFA)
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={data.supplierPrice}
                  onChange={(e) => handleInputChange('supplierPrice', parseFloat(e.target.value) || 0)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Coûts logistiques (FCFA)
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={data.logisticsCost}
                  onChange={(e) => handleInputChange('logisticsCost', parseFloat(e.target.value) || 0)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Marge souhaitée (%)
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={data.margin}
                  onChange={(e) => handleInputChange('margin', parseFloat(e.target.value) || 0)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Quantité
                </label>
                <input
                  type="number"
                  min="1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={data.quantity}
                  onChange={(e) => handleInputChange('quantity', parseInt(e.target.value) || 1)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Poids total (kg)
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={data.weight}
                  onChange={(e) => handleInputChange('weight', parseFloat(e.target.value) || 0)}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mode de transport
              </label>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant={data.transportMode === 'TRUCK' ? 'default' : 'outline'}
                  onClick={() => handleInputChange('transportMode', 'TRUCK')}
                  className="flex-1"
                >
                  Routier (75 FCFA/kg)
                </Button>
                <Button
                  type="button"
                  variant={data.transportMode === 'AIR' ? 'default' : 'outline'}
                  onClick={() => handleInputChange('transportMode', 'AIR')}
                  className="flex-1"
                >
                  Aérien (150 FCFA/kg)
                </Button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Taux de change NGN → XOF
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={data.exchangeRate}
                onChange={(e) => handleInputChange('exchangeRate', parseFloat(e.target.value) || 650)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Résultats */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Résultats
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="text-sm text-blue-700 mb-1">Prix de vente unitaire</div>
              <div className="text-lg font-bold text-blue-900">
                {result.pricePerUnit.toLocaleString()} FCFA
              </div>
            </div>

            <div className="bg-green-50 p-3 rounded-lg">
              <div className="text-sm text-green-700 mb-1">Chiffre d'affaires total</div>
              <div className="text-lg font-bold text-green-900">
                {result.totalRevenue.toLocaleString()} FCFA
              </div>
            </div>

            <div className="bg-purple-50 p-3 rounded-lg">
              <div className="text-sm text-purple-700 mb-1">Bénéfice total</div>
              <div className="text-lg font-bold text-purple-900">
                {result.totalProfit.toLocaleString()} FCFA
              </div>
            </div>

            <div className="bg-orange-50 p-3 rounded-lg">
              <div className="text-sm text-orange-700 mb-1">Marge réelle</div>
              <div className="text-lg font-bold text-orange-900">
                {result.profitMargin.toFixed(1)}%
              </div>
            </div>

            <Button
              onClick={saveCalculation}
              className="w-full"
              disabled={result.totalRevenue === 0}
            >
              Sauvegarder ce calcul
            </Button>
          </CardContent>
        </Card>

        {/* Calculs sauvegardés */}
        {savedCalculations.length > 0 && (
          <Card className="lg:col-span-3">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Calculs sauvegardés ({savedCalculations.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {savedCalculations.map(calculation => (
                  <div
                    key={calculation.id}
                    className="p-4 border border-gray-200 rounded-lg hover:border-gray-300"
                  >
                    <div className="font-medium mb-2">{calculation.name}</div>
                    <div className="text-sm text-gray-600 mb-2">
                      Prix: {calculation.result.pricePerUnit.toLocaleString()} FCFA
                    </div>
                    <div className="text-sm text-gray-600 mb-3">
                      Marge: {calculation.result.profitMargin.toFixed(1)}%
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => loadCalculation(calculation)}
                        className="flex-1"
                      >
                        Charger
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => deleteCalculation(calculation.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        Supprimer
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
