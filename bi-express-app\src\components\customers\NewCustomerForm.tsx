'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { User, MapPin, Phone, Mail, Building, Truck, ShoppingCart } from 'lucide-react'
import { apiCall } from '@/hooks/useApi'

interface CustomerFormData {
  name: string
  email: string
  phone: string
  address: string
  city: string
  type: 'LOGISTICS' | 'COMMERCE'
  contactPerson: string
  businessType: string
  notes: string
}

const senegalCities = [
  'Dakar', 'Thiès', 'Saint-Louis', 'Kaolack', 'Ziguinchor', 
  'Diourbel', 'Tambacounda', 'Kolda', 'Fatick', 'Louga'
]

const businessTypes = [
  'Boutique', 'Grossiste', 'Détaillant', 'Salon de beauté', 'Marché',
  'Entreprise de transport', 'Société de logistique', 'Transitaire'
]

interface NewCustomerFormProps {
  onSuccess: () => void
  onCancel: () => void
}

export function NewCustomerForm({ onSuccess, onCancel }: NewCustomerFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState<CustomerFormData>({
    name: '',
    email: '',
    phone: '',
    address: '',
    city: 'Dakar',
    type: 'COMMERCE',
    contactPerson: '',
    businessType: '',
    notes: ''
  })

  const handleInputChange = (field: keyof CustomerFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name || !formData.phone || !formData.type) {
      alert('Veuillez remplir tous les champs obligatoires')
      return
    }

    setIsLoading(true)
    try {
      // Adapter les données au format API
      const apiData = {
        name: formData.name,
        phone: formData.phone,
        email: formData.email || undefined,
        address: formData.address || undefined,
        city: formData.city,
        type: formData.type,
        contactPerson: formData.contactPerson || undefined,
        businessType: formData.businessType || undefined,
        notes: formData.notes || undefined
      }

      await apiCall('/api/customers', {
        method: 'POST',
        body: apiData
      })

      onSuccess()
    } catch (error) {
      console.error('Erreur:', error)
      alert(`Erreur: ${error instanceof Error ? error.message : 'Erreur lors de la création du client'}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Informations générales */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Informations générales
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nom du client *
              </label>
              <input
                type="text"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Ex: Boutique Elegance"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Type de client *
              </label>
              <div className="grid grid-cols-2 gap-3">
                <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="type"
                    value="COMMERCE"
                    checked={formData.type === 'COMMERCE'}
                    onChange={(e) => handleInputChange('type', e.target.value as 'LOGISTICS' | 'COMMERCE')}
                    className="mr-3"
                  />
                  <ShoppingCart className="h-5 w-5 text-green-600 mr-2" />
                  <div>
                    <div className="font-medium text-gray-900">Commerce</div>
                    <div className="text-xs text-gray-500">Achat + Transport</div>
                  </div>
                </label>
                <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="type"
                    value="LOGISTICS"
                    checked={formData.type === 'LOGISTICS'}
                    onChange={(e) => handleInputChange('type', e.target.value as 'LOGISTICS' | 'COMMERCE')}
                    className="mr-3"
                  />
                  <Truck className="h-5 w-5 text-orange-600 mr-2" />
                  <div>
                    <div className="font-medium text-gray-900">Logistique</div>
                    <div className="text-xs text-gray-500">Transport uniquement</div>
                  </div>
                </label>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Type d'entreprise
              </label>
              <select
                title="Type d'entreprise"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.businessType}
                onChange={(e) => handleInputChange('businessType', e.target.value)}
              >
                <option value="">Sélectionner un type</option>
                {businessTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Ville
              </label>
              <select
                title="Ville"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
              >
                {senegalCities.map(city => (
                  <option key={city} value={city}>{city}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Adresse complète
              </label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder="Adresse complète du client"
              />
            </div>
          </CardContent>
        </Card>

        {/* Contact */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Informations de contact
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Personne de contact
              </label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.contactPerson}
                onChange={(e) => handleInputChange('contactPerson', e.target.value)}
                placeholder="Nom du contact principal"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                type="email"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Téléphone *
              </label>
              <input
                type="tel"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="+221 xx xxx xx xx"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notes additionnelles
              </label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={4}
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Informations supplémentaires sur le client..."
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Boutons d'action */}
      <div className="flex justify-end gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
        >
          Annuler
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
        >
          {isLoading ? 'Création...' : 'Créer le client'}
        </Button>
      </div>
    </form>
  )
}
