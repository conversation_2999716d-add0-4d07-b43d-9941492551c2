import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Début du seeding...')

  // Créer des fournisseurs
  const suppliers = await Promise.all([
    prisma.supplier.create({
      data: {
        name: 'Textile Lagos Premium',
        phone: '+234-************',
        email: '<EMAIL>',
        address: '123 Balogun Market, Lagos Island',
        city: 'LAGOS',
        specialties: 'Wax, Bazin, Soie',
        rating: 4.5,
      },
    }),
    prisma.supplier.create({
      data: {
        name: 'Abuja Cosmetics Hub',
        phone: '+234-************',
        email: '<EMAIL>',
        address: '45 Wuse Market, Abuja',
        city: 'ABUJA',
        specialties: 'Cosmétiques, Parfums, Soins',
        rating: 4.2,
      },
    }),
    prisma.supplier.create({
      data: {
        name: 'Kano Hair Extensions',
        phone: '+234-************',
        email: '<EMAIL>',
        address: '78 Kurmi Market, Kano',
        city: 'KANO',
        specialties: 'Mèches, Extensions, Cheveux naturels',
        rating: 4.7,
      },
    }),
  ])

  console.log('✅ Fournisseurs créés')

  // Créer des produits
  const products = await Promise.all([
    // Tissus
    prisma.product.create({
      data: {
        name: 'Wax Hollandais Premium',
        description: 'Tissu wax de haute qualité, motifs traditionnels',
        category: 'TISSUS',
        fabricType: 'Wax',
        width: 1.2,
        color: 'Multicolore',
        pattern: 'Géométrique',
        supplierPrice: 15000,
        margin: 0.25,
        stockQuantity: 50,
        minStockAlert: 10,
        weight: 0.5,
        supplierId: suppliers[0].id,
      },
    }),
    prisma.product.create({
      data: {
        name: 'Bazin Riche Brodé',
        description: 'Bazin de qualité supérieure avec broderies',
        category: 'TISSUS',
        fabricType: 'Bazin',
        width: 1.5,
        color: 'Blanc',
        pattern: 'Brodé',
        supplierPrice: 25000,
        margin: 0.30,
        stockQuantity: 30,
        minStockAlert: 5,
        weight: 0.8,
        supplierId: suppliers[0].id,
      },
    }),
    // Cosmétiques
    prisma.product.create({
      data: {
        name: 'Crème Éclaircissante Fair & White',
        description: 'Crème éclaircissante pour le visage',
        category: 'COSMETIQUES',
        brand: 'Fair & White',
        volume: 50,
        origin: 'Nigeria',
        supplierPrice: 8000,
        margin: 0.40,
        stockQuantity: 100,
        minStockAlert: 20,
        weight: 0.1,
        supplierId: suppliers[1].id,
      },
    }),
    prisma.product.create({
      data: {
        name: 'Parfum Oud Royal',
        description: 'Parfum oriental de luxe',
        category: 'COSMETIQUES',
        brand: 'Oud Royal',
        volume: 100,
        origin: 'Nigeria',
        supplierPrice: 12000,
        margin: 0.35,
        stockQuantity: 75,
        minStockAlert: 15,
        weight: 0.2,
        supplierId: suppliers[1].id,
      },
    }),
    // Mèches
    prisma.product.create({
      data: {
        name: 'Mèches Brésiliennes 20 pouces',
        description: 'Cheveux naturels brésiliens, qualité premium',
        category: 'MECHES',
        length: 20,
        texture: 'Lisse',
        hairType: 'Naturel',
        color: 'Noir naturel',
        supplierPrice: 45000,
        margin: 0.50,
        stockQuantity: 25,
        minStockAlert: 5,
        weight: 0.3,
        supplierId: suppliers[2].id,
      },
    }),
    prisma.product.create({
      data: {
        name: 'Extensions Synthétiques Bouclées',
        description: 'Extensions synthétiques de qualité, bouclées',
        category: 'MECHES',
        length: 16,
        texture: 'Bouclée',
        hairType: 'Synthétique',
        color: 'Brun',
        supplierPrice: 8000,
        margin: 0.45,
        stockQuantity: 60,
        minStockAlert: 12,
        weight: 0.2,
        supplierId: suppliers[2].id,
      },
    }),
  ])

  console.log('✅ Produits créés')

  // Créer des clients
  const customers = await Promise.all([
    prisma.customer.create({
      data: {
        name: 'Fatou Diop',
        phone: '+221-77-123-4567',
        email: '<EMAIL>',
        address: 'Médina, Dakar',
        creditLimit: 500000,
      },
    }),
    prisma.customer.create({
      data: {
        name: 'Aminata Sow',
        phone: '+221-78-234-5678',
        email: '<EMAIL>',
        address: 'Plateau, Dakar',
        creditLimit: 750000,
      },
    }),
    prisma.customer.create({
      data: {
        name: 'Boutique Elegance',
        phone: '+221-77-345-6789',
        email: '<EMAIL>',
        address: 'Sandaga, Dakar',
        creditLimit: 1000000,
      },
    }),
  ])

  console.log('✅ Clients créés')

  // Créer des taux de change
  await prisma.exchangeRate.create({
    data: {
      fromCurrency: 'NGN',
      toCurrency: 'XOF',
      rate: 0.85,
    },
  })

  console.log('✅ Taux de change créé')

  console.log('🎉 Seeding terminé avec succès!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
