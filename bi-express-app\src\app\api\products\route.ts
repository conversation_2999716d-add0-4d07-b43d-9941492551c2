import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const category = searchParams.get('category')
    const supplierId = searchParams.get('supplierId')

    const where: any = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { sku: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (category) {
      where.category = category
    }

    if (supplierId) {
      where.supplierId = supplierId
    }

    const products = await prisma.product.findMany({
      where,
      include: {
        supplier: {
          select: {
            id: true,
            name: true,
            city: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(products)
  } catch (error) {
    console.error('Erreur lors de la récupération des produits:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la récupération des produits' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const {
      name,
      description,
      category,
      supplierId,
      supplierPrice,
      logisticsCost,
      margin,
      sellingPrice,
      minQuantity,
      weight,
      dimensions,
      sku,
      tags,
      isActive
    } = body

    // Validation des champs obligatoires
    if (!name || !category || !supplierId || !supplierPrice) {
      return NextResponse.json(
        { error: 'Les champs nom, catégorie, fournisseur et prix fournisseur sont obligatoires' },
        { status: 400 }
      )
    }

    // Vérifier que le fournisseur existe
    const supplier = await prisma.supplier.findUnique({
      where: { id: supplierId }
    })

    if (!supplier) {
      return NextResponse.json(
        { error: 'Fournisseur non trouvé' },
        { status: 404 }
      )
    }

    // Générer un SKU si non fourni
    const finalSku = sku || `${category.substring(0, 3).toUpperCase()}-${supplier.name.substring(0, 3).toUpperCase()}-${Math.random().toString(36).substring(2, 6).toUpperCase()}`

    // Vérifier l'unicité du SKU
    const existingSku = await prisma.product.findUnique({
      where: { sku: finalSku }
    })

    if (existingSku) {
      return NextResponse.json(
        { error: 'Ce SKU existe déjà' },
        { status: 400 }
      )
    }

    const product = await prisma.product.create({
      data: {
        name,
        description: description || '',
        category,
        supplierId,
        supplierPrice: parseFloat(supplierPrice),
        logisticsCost: parseFloat(logisticsCost) || 0,
        margin: parseFloat(margin) || 30,
        sellingPrice: parseFloat(sellingPrice),
        minQuantity: parseInt(minQuantity) || 1,
        weight: parseFloat(weight) || 0,
        dimensions: dimensions || '',
        sku: finalSku,
        tags: tags || [],
        isActive: isActive !== false
      },
      include: {
        supplier: {
          select: {
            id: true,
            name: true,
            city: true
          }
        }
      }
    })

    return NextResponse.json(product, { status: 201 })
  } catch (error) {
    console.error('Erreur lors de la création du produit:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la création du produit' },
      { status: 500 }
    )
  }
}
