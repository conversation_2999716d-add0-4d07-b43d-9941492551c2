'use client'

import { useNotifications, useToast } from '@/contexts/NotificationContext'
import { useRouter } from 'next/navigation'

export function useBusinessNotifications() {
  const { addNotification } = useNotifications()
  const toast = useToast()
  const router = useRouter()

  // Notifications pour les fournisseurs
  const notifySupplierCreated = (supplierName: string) => {
    toast.success('Fournisseur créé', `${supplierName} a été ajouté avec succès`)
    addNotification({
      type: 'success',
      title: 'Nouveau fournisseur',
      message: `${supplierName} a été ajouté à votre liste de fournisseurs`,
      action: {
        label: 'Voir le fournisseur',
        onClick: () => router.push('/suppliers')
      }
    })
  }

  const notifySupplierUpdated = (supplierName: string) => {
    toast.success('Fournisseur modifié', `${supplierName} a été mis à jour`)
  }

  const notifySupplierDeleted = (supplierName: string) => {
    toast.success('Fournisseur supprimé', `${supplierName} a été retiré de votre liste`)
  }

  // Notifications pour les produits
  const notifyProductCreated = (productName: string) => {
    toast.success('Produit créé', `${productName} a été ajouté au catalogue`)
    addNotification({
      type: 'success',
      title: 'Nouveau produit',
      message: `${productName} est maintenant disponible dans votre catalogue`,
      action: {
        label: 'Voir le catalogue',
        onClick: () => router.push('/products')
      }
    })
  }

  const notifyProductUpdated = (productName: string) => {
    toast.success('Produit modifié', `${productName} a été mis à jour`)
  }

  const notifyProductDeleted = (productName: string) => {
    toast.success('Produit supprimé', `${productName} a été retiré du catalogue`)
  }

  const notifyLowStock = (productName: string, stock: number) => {
    addNotification({
      type: 'warning',
      title: 'Stock faible',
      message: `${productName} - Il ne reste que ${stock} unités en stock`,
      action: {
        label: 'Réapprovisionner',
        onClick: () => router.push('/products')
      }
    })
  }

  // Notifications pour les commandes
  const notifyOrderCreated = (orderNumber: string, customerName: string) => {
    toast.success('Commande créée', `Commande ${orderNumber} pour ${customerName}`)
    addNotification({
      type: 'success',
      title: 'Nouvelle commande',
      message: `Commande ${orderNumber} créée pour ${customerName}`,
      action: {
        label: 'Voir la commande',
        onClick: () => router.push('/orders')
      }
    })
  }

  const notifyOrderStatusChanged = (orderNumber: string, status: string) => {
    const statusLabels: Record<string, string> = {
      'PENDING': 'En attente',
      'CONFIRMED': 'Confirmée',
      'SHIPPED': 'Expédiée',
      'DELIVERED': 'Livrée',
      'CANCELLED': 'Annulée'
    }

    addNotification({
      type: status === 'DELIVERED' ? 'success' : 'info',
      title: 'Statut de commande mis à jour',
      message: `Commande ${orderNumber} - ${statusLabels[status] || status}`,
      action: {
        label: 'Voir la commande',
        onClick: () => router.push('/orders')
      }
    })
  }

  // Notifications pour les clients
  const notifyCustomerCreated = (customerName: string) => {
    toast.success('Client créé', `${customerName} a été ajouté`)
    addNotification({
      type: 'success',
      title: 'Nouveau client',
      message: `${customerName} a été ajouté à votre base clients`,
      action: {
        label: 'Voir les clients',
        onClick: () => router.push('/customers')
      }
    })
  }

  // Notifications pour les expéditions
  const notifyShipmentCreated = (trackingNumber: string) => {
    toast.success('Expédition créée', `Numéro de suivi: ${trackingNumber}`)
    addNotification({
      type: 'success',
      title: 'Nouvelle expédition',
      message: `Expédition créée avec le numéro de suivi ${trackingNumber}`,
      action: {
        label: 'Suivre l\'expédition',
        onClick: () => router.push('/shipments')
      }
    })
  }

  const notifyShipmentStatusChanged = (trackingNumber: string, status: string) => {
    const statusLabels: Record<string, string> = {
      'PENDING': 'En attente',
      'IN_TRANSIT': 'En transit',
      'DELIVERED': 'Livrée',
      'CANCELLED': 'Annulée'
    }

    addNotification({
      type: status === 'DELIVERED' ? 'success' : 'info',
      title: 'Statut d\'expédition mis à jour',
      message: `Expédition ${trackingNumber} - ${statusLabels[status] || status}`,
      action: {
        label: 'Voir l\'expédition',
        onClick: () => router.push('/shipments')
      }
    })
  }

  // Notifications d'erreur
  const notifyError = (title: string, message: string) => {
    toast.error(title, message)
    addNotification({
      type: 'error',
      title,
      message
    })
  }

  // Notifications système
  const notifySystemUpdate = (message: string) => {
    addNotification({
      type: 'info',
      title: 'Mise à jour système',
      message
    })
  }

  const notifyPaymentReceived = (amount: number, currency: string, customerName: string) => {
    addNotification({
      type: 'success',
      title: 'Paiement reçu',
      message: `${amount.toLocaleString()} ${currency} reçu de ${customerName}`,
      action: {
        label: 'Voir les paiements',
        onClick: () => router.push('/accounting')
      }
    })
  }

  return {
    // Fournisseurs
    notifySupplierCreated,
    notifySupplierUpdated,
    notifySupplierDeleted,
    
    // Produits
    notifyProductCreated,
    notifyProductUpdated,
    notifyProductDeleted,
    notifyLowStock,
    
    // Commandes
    notifyOrderCreated,
    notifyOrderStatusChanged,
    
    // Clients
    notifyCustomerCreated,
    
    // Expéditions
    notifyShipmentCreated,
    notifyShipmentStatusChanged,
    
    // Système
    notifyError,
    notifySystemUpdate,
    notifyPaymentReceived,
    
    // Accès direct aux toasts
    toast
  }
}
