
import { TransportMode, ShipmentType, CustomerType } from '@prisma/client'

// Types pour la consolidation
export interface ConsolidationRequest {
  id: string
  customerId: string
  customerType: CustomerType
  weight: number
  volume: number
  originCity: string
  destinationCity: string
  transportMode: TransportMode
  urgency: 'STANDARD' | 'EXPRESS' | 'URGENT'
  requestedPickupDate: Date
  declaredValue?: number
  description: string
}

export interface ConsolidationGroup {
  id: string
  transportMode: TransportMode
  route: string
  departureDate: Date
  maxWeight: number
  maxVolume: number
  currentWeight: number
  currentVolume: number
  loadingRate: number
  requests: ConsolidationRequest[]
  estimatedCost: number
  costPerClient: Array<{
    requestId: string
    allocatedCost: number
    savings: number
  }>
}

export interface ConsolidationResult {
  groups: ConsolidationGroup[]
  totalSavings: number
  optimizationScore: number
  recommendations: string[]
}

// Configuration des capacités par mode de transport
const TRANSPORT_CAPACITIES = {
  ROAD: {
    maxWeight: 25000, // 25 tonnes
    maxVolume: 100,   // 100 m³
    costPerKm: 450    // CFA par km
  },
  AIR_EXPRESS: {
    maxWeight: 5000,  // 5 tonnes
    maxVolume: 25,    // 25 m³
    costPerKm: 1200   // CFA par km
  }
}

// Distances entre villes (en km)
const ROUTE_DISTANCES = {
  'LAGOS_DAKAR': 3200,
  'ABUJA_DAKAR': 3800,
  'KANO_DAKAR': 2800
}

/**
 * Optimise la consolidation des demandes de transport
 */
export function optimizeConsolidation(
  requests: ConsolidationRequest[],
  maxDaysWindow: number = 3
): ConsolidationResult {
  // Grouper par mode de transport et route
  const groupedRequests = groupRequestsByRoute(requests)
  
  // Créer les groupes de consolidation optimaux
  const groups: ConsolidationGroup[] = []
  let totalSavings = 0
  const recommendations: string[] = []

  for (const [routeKey, routeRequests] of Object.entries(groupedRequests)) {
    const [origin, destination] = routeKey.split('_')
    const transportMode = routeRequests[0].transportMode
    
    // Optimiser les groupes pour cette route
    const routeGroups = optimizeRouteGroups(
      routeRequests,
      transportMode,
      `${origin}_${destination}`,
      maxDaysWindow
    )
    
    groups.push(...routeGroups)
    
    // Calculer les économies pour cette route
    const routeSavings = calculateRouteSavings(routeRequests, routeGroups)
    totalSavings += routeSavings
    
    // Générer des recommandations
    if (routeGroups.length > 1) {
      recommendations.push(
        `Route ${origin}-${destination}: ${routeGroups.length} groupes créés, économies de ${routeSavings.toLocaleString()} CFA`
      )
    }
  }

  // Calculer le score d'optimisation
  const optimizationScore = calculateOptimizationScore(groups)

  // Recommandations générales
  if (totalSavings > 100000) {
    recommendations.push(`Économies totales importantes: ${totalSavings.toLocaleString()} CFA`)
  }
  
  if (optimizationScore < 0.7) {
    recommendations.push('Considérer un délai de consolidation plus long pour améliorer l\'optimisation')
  }

  return {
    groups,
    totalSavings,
    optimizationScore,
    recommendations
  }
}

/**
 * Groupe les demandes par route et mode de transport
 */
function groupRequestsByRoute(
  requests: ConsolidationRequest[]
): Record<string, ConsolidationRequest[]> {
  const groups: Record<string, ConsolidationRequest[]> = {}

  for (const request of requests) {
    const routeKey = `${request.originCity}_${request.destinationCity}_${request.transportMode}`
    
    if (!groups[routeKey]) {
      groups[routeKey] = []
    }
    
    groups[routeKey].push(request)
  }

  return groups
}

/**
 * Optimise les groupes pour une route donnée
 */
function optimizeRouteGroups(
  requests: ConsolidationRequest[],
  transportMode: TransportMode,
  route: string,
  maxDaysWindow: number
): ConsolidationGroup[] {
  const capacity = TRANSPORT_CAPACITIES[transportMode]
  const groups: ConsolidationGroup[] = []
  
  // Trier par date de départ souhaitée
  const sortedRequests = [...requests].sort(
    (a, b) => a.requestedPickupDate.getTime() - b.requestedPickupDate.getTime()
  )

  let currentGroup: ConsolidationRequest[] = []
  let currentWeight = 0
  let currentVolume = 0
  let groupStartDate = sortedRequests[0]?.requestedPickupDate

  for (const request of sortedRequests) {
    const daysDiff = groupStartDate 
      ? Math.abs(request.requestedPickupDate.getTime() - groupStartDate.getTime()) / (1000 * 60 * 60 * 24)
      : 0

    // Vérifier si on peut ajouter cette demande au groupe actuel
    const canAddToGroup = 
      daysDiff <= maxDaysWindow &&
      currentWeight + request.weight <= capacity.maxWeight &&
      currentVolume + request.volume <= capacity.maxVolume

    if (canAddToGroup && currentGroup.length > 0) {
      // Ajouter au groupe actuel
      currentGroup.push(request)
      currentWeight += request.weight
      currentVolume += request.volume
    } else {
      // Finaliser le groupe actuel s'il existe
      if (currentGroup.length > 0) {
        groups.push(createConsolidationGroup(currentGroup, transportMode, route))
      }
      
      // Commencer un nouveau groupe
      currentGroup = [request]
      currentWeight = request.weight
      currentVolume = request.volume
      groupStartDate = request.requestedPickupDate
    }
  }

  // Finaliser le dernier groupe
  if (currentGroup.length > 0) {
    groups.push(createConsolidationGroup(currentGroup, transportMode, route))
  }

  return groups
}

/**
 * Crée un groupe de consolidation
 */
function createConsolidationGroup(
  requests: ConsolidationRequest[],
  transportMode: TransportMode,
  route: string
): ConsolidationGroup {
  const capacity = TRANSPORT_CAPACITIES[transportMode]
  const totalWeight = requests.reduce((sum, r) => sum + r.weight, 0)
  const totalVolume = requests.reduce((sum, r) => sum + r.volume, 0)
  
  // Date de départ = date la plus tardive du groupe
  const departureDate = new Date(
    Math.max(...requests.map(r => r.requestedPickupDate.getTime()))
  )

  // Calculer le coût total du groupe
  const distance = ROUTE_DISTANCES[route as keyof typeof ROUTE_DISTANCES] || 3000
  const estimatedCost = distance * capacity.costPerKm

  // Répartir les coûts proportionnellement au poids
  const costPerClient = requests.map(request => {
    const weightRatio = request.weight / totalWeight
    const allocatedCost = estimatedCost * weightRatio
    
    // Calculer les économies (estimation basée sur le coût individuel)
    const individualCost = estimatedCost * 0.8 // Estimation d'économie de 20%
    const savings = individualCost - allocatedCost

    return {
      requestId: request.id,
      allocatedCost: Math.round(allocatedCost),
      savings: Math.round(savings)
    }
  })

  return {
    id: `CG-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    transportMode,
    route,
    departureDate,
    maxWeight: capacity.maxWeight,
    maxVolume: capacity.maxVolume,
    currentWeight: totalWeight,
    currentVolume: totalVolume,
    loadingRate: Math.max(totalWeight / capacity.maxWeight, totalVolume / capacity.maxVolume),
    requests,
    estimatedCost,
    costPerClient
  }
}

/**
 * Calcule les économies pour une route
 */
function calculateRouteSavings(
  originalRequests: ConsolidationRequest[],
  optimizedGroups: ConsolidationGroup[]
): number {
  // Coût individuel estimé
  const individualCosts = originalRequests.reduce((sum, request) => {
    const route = `${request.originCity}_${request.destinationCity}`
    const distance = ROUTE_DISTANCES[route as keyof typeof ROUTE_DISTANCES] || 3000
    const capacity = TRANSPORT_CAPACITIES[request.transportMode]
    return sum + (distance * capacity.costPerKm * 0.8) // Coût individuel estimé
  }, 0)

  // Coût consolidé
  const consolidatedCosts = optimizedGroups.reduce((sum, group) => sum + group.estimatedCost, 0)

  return Math.max(0, individualCosts - consolidatedCosts)
}

/**
 * Calcule le score d'optimisation (0-1)
 */
function calculateOptimizationScore(groups: ConsolidationGroup[]): number {
  if (groups.length === 0) return 0

  const totalScore = groups.reduce((sum, group) => {
    // Score basé sur le taux de chargement et le nombre de clients
    const loadingScore = group.loadingRate
    const clientScore = Math.min(group.requests.length / 5, 1) // Optimal à 5 clients
    return sum + (loadingScore * 0.6 + clientScore * 0.4)
  }, 0)

  return totalScore / groups.length
}

/**
 * Trouve les opportunités de consolidation pour une nouvelle demande
 */
export function findConsolidationOpportunities(
  newRequest: ConsolidationRequest,
  existingGroups: ConsolidationGroup[]
): Array<{
  group: ConsolidationGroup
  compatibility: number
  estimatedSavings: number
}> {
  const opportunities = []

  for (const group of existingGroups) {
    // Vérifier la compatibilité
    const isCompatibleRoute = group.route === `${newRequest.originCity}_${newRequest.destinationCity}`
    const isCompatibleMode = group.transportMode === newRequest.transportMode
    
    if (!isCompatibleRoute || !isCompatibleMode) continue

    // Vérifier la capacité
    const hasWeightCapacity = group.currentWeight + newRequest.weight <= group.maxWeight
    const hasVolumeCapacity = group.currentVolume + newRequest.volume <= group.maxVolume
    
    if (!hasWeightCapacity || !hasVolumeCapacity) continue

    // Calculer la compatibilité temporelle
    const daysDiff = Math.abs(
      newRequest.requestedPickupDate.getTime() - group.departureDate.getTime()
    ) / (1000 * 60 * 60 * 24)
    
    const timeCompatibility = Math.max(0, 1 - daysDiff / 7) // Décroît sur 7 jours

    // Score de compatibilité global
    const compatibility = timeCompatibility * 0.5 + 
                         (hasWeightCapacity ? 0.25 : 0) + 
                         (hasVolumeCapacity ? 0.25 : 0)

    // Estimation des économies
    const estimatedSavings = group.estimatedCost * 0.15 // 15% d'économie estimée

    if (compatibility > 0.3) { // Seuil minimum de compatibilité
      opportunities.push({
        group,
        compatibility,
        estimatedSavings
      })
    }
  }

  // Trier par compatibilité décroissante
  return opportunities.sort((a, b) => b.compatibility - a.compatibility)
}

