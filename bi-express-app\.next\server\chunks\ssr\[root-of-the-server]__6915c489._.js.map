{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/ProductsList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProductsList = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductsList() from the server but ProductsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/products/ProductsList.tsx <module evaluation>\",\n    \"ProductsList\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0EACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/ProductsList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProductsList = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductsList() from the server but ProductsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/products/ProductsList.tsx\",\n    \"ProductsList\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,sDACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/ProductFilters.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProductFilters = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductFilters() from the server but ProductFilters is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/products/ProductFilters.tsx <module evaluation>\",\n    \"ProductFilters\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,4EACA", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/ProductFilters.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProductFilters = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductFilters() from the server but ProductFilters is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/products/ProductFilters.tsx\",\n    \"ProductFilters\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,wDACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/AddProductButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AddProductButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call AddProductButton() from the server but AddProductButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/products/AddProductButton.tsx <module evaluation>\",\n    \"AddProductButton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8EACA", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/AddProductButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AddProductButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call AddProductButton() from the server but AddProductButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/products/AddProductButton.tsx\",\n    \"AddProductButton\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0DACA", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: 'NGN' | 'XOF' = 'XOF'): string {\n  const symbols = {\n    NGN: '₦',\n    XOF: 'CFA'\n  }\n  \n  return `${amount.toLocaleString('fr-FR')} ${symbols[currency]}`\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric'\n  }).format(date)\n}\n\nexport function formatDateTime(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  }).format(date)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAA0B,KAAK;IAC5E,MAAM,UAAU;QACd,KAAK;QACL,KAAK;IACP;IAEA,OAAO,GAAG,OAAO,cAAc,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,SAAS,EAAE;AACjE;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;IACR,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAU;IACvC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/products/ProductStats.tsx"], "sourcesContent": ["import { prisma } from '@/lib/prisma'\nimport { formatCurrency } from '@/lib/utils'\nimport { Package, AlertTriangle, TrendingUp, DollarSign } from 'lucide-react'\n\nasync function getProductStats() {\n  const [\n    totalProducts,\n    lowStockProducts,\n    products\n  ] = await Promise.all([\n    prisma.product.count({ where: { isActive: true } }),\n    prisma.product.count({ \n      where: { \n        isActive: true,\n        stockQuantity: { lte: prisma.product.fields.minStockAlert }\n      }\n    }),\n    prisma.product.findMany({\n      where: { isActive: true },\n      select: {\n        supplierPrice: true,\n        margin: true,\n        logisticRate: true,\n        stockQuantity: true\n      }\n    })\n  ])\n\n  // Calculs des statistiques\n  const totalValue = products.reduce((sum, product) => {\n    const costPrice = product.supplierPrice * (1 + product.logisticRate)\n    return sum + (costPrice * product.stockQuantity)\n  }, 0)\n\n  const averageMargin = products.length > 0 \n    ? products.reduce((sum, product) => sum + product.margin, 0) / products.length * 100\n    : 0\n\n  const totalStock = products.reduce((sum, product) => sum + product.stockQuantity, 0)\n\n  return {\n    totalProducts,\n    lowStockProducts,\n    totalValue,\n    averageMargin,\n    totalStock\n  }\n}\n\nexport async function ProductStats() {\n  const stats = await getProductStats()\n\n  const statCards = [\n    {\n      title: 'Total Produits',\n      value: stats.totalProducts.toString(),\n      icon: Package,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-50',\n      description: 'Produits actifs'\n    },\n    {\n      title: 'Stock Total',\n      value: stats.totalStock.toString(),\n      icon: TrendingUp,\n      color: 'text-green-600',\n      bgColor: 'bg-green-50',\n      description: 'Unités en stock'\n    },\n    {\n      title: 'Valeur Stock',\n      value: formatCurrency(stats.totalValue),\n      icon: DollarSign,\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-50',\n      description: 'Valeur totale'\n    },\n    {\n      title: 'Stock Faible',\n      value: stats.lowStockProducts.toString(),\n      icon: AlertTriangle,\n      color: 'text-red-600',\n      bgColor: 'bg-red-50',\n      description: 'Produits à réapprovisionner'\n    }\n  ]\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n      {statCards.map((stat, index) => (\n        <div key={index} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">{stat.title}</p>\n              <p className=\"text-2xl font-bold text-gray-900 mt-1\">{stat.value}</p>\n              <p className=\"text-xs text-gray-500 mt-1\">{stat.description}</p>\n            </div>\n            <div className={`p-3 rounded-lg ${stat.bgColor}`}>\n              <stat.icon className={`h-6 w-6 ${stat.color}`} />\n            </div>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;;;;;AAEA,eAAe;IACb,MAAM,CACJ,eACA,kBACA,SACD,GAAG,MAAM,QAAQ,GAAG,CAAC;QACpB,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YAAE,OAAO;gBAAE,UAAU;YAAK;QAAE;QACjD,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YACnB,OAAO;gBACL,UAAU;gBACV,eAAe;oBAAE,KAAK,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa;gBAAC;YAC5D;QACF;QACA,oHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YACtB,OAAO;gBAAE,UAAU;YAAK;YACxB,QAAQ;gBACN,eAAe;gBACf,QAAQ;gBACR,cAAc;gBACd,eAAe;YACjB;QACF;KACD;IAED,2BAA2B;IAC3B,MAAM,aAAa,SAAS,MAAM,CAAC,CAAC,KAAK;QACvC,MAAM,YAAY,QAAQ,aAAa,GAAG,CAAC,IAAI,QAAQ,YAAY;QACnE,OAAO,MAAO,YAAY,QAAQ,aAAa;IACjD,GAAG;IAEH,MAAM,gBAAgB,SAAS,MAAM,GAAG,IACpC,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,MAAM,EAAE,KAAK,SAAS,MAAM,GAAG,MAC/E;IAEJ,MAAM,aAAa,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,aAAa,EAAE;IAElF,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAEO,eAAe;IACpB,MAAM,QAAQ,MAAM;IAEpB,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,MAAM,aAAa,CAAC,QAAQ;YACnC,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,MAAM,UAAU,CAAC,QAAQ;YAChC,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,UAAU;YACtC,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,MAAM,gBAAgB,CAAC,QAAQ;YACtC,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,SAAS;YACT,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;gBAAgB,WAAU;0BACzB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAAqC,KAAK,KAAK;;;;;;8CAC5D,8OAAC;oCAAE,WAAU;8CAAyC,KAAK,KAAK;;;;;;8CAChE,8OAAC;oCAAE,WAAU;8CAA8B,KAAK,WAAW;;;;;;;;;;;;sCAE7D,8OAAC;4BAAI,WAAW,CAAC,eAAe,EAAE,KAAK,OAAO,EAAE;sCAC9C,cAAA,8OAAC,KAAK,IAAI;gCAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;;;;;;;eARzC;;;;;;;;;;AAelB", "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/products/page.tsx"], "sourcesContent": ["import { Suspense } from 'react'\nimport { ProductsList } from '@/components/products/ProductsList'\nimport { ProductFilters } from '@/components/products/ProductFilters'\nimport { AddProductButton } from '@/components/products/AddProductButton'\nimport { ProductStats } from '@/components/products/ProductStats'\n\nexport default function ProductsPage() {\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between border-b border-gray-200 pb-4\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Catalogue Produits</h1>\n          <p className=\"text-gray-600 mt-1\">\n            Gérez vos produits avec calcul automatique des prix et marges\n          </p>\n        </div>\n        <AddProductButton />\n      </div>\n\n      {/* Stats */}\n      <Suspense fallback={<div className=\"animate-pulse h-24 bg-gray-200 rounded-lg\" />}>\n        <ProductStats />\n      </Suspense>\n\n      {/* Filters */}\n      <Suspense fallback={<div className=\"animate-pulse h-16 bg-gray-200 rounded-lg\" />}>\n        <ProductFilters />\n      </Suspense>\n\n      {/* Products List */}\n      <Suspense fallback={<div className=\"animate-pulse h-96 bg-gray-200 rounded-lg\" />}>\n        <ProductsList />\n      </Suspense>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,8OAAC,kJAAA,CAAA,mBAAgB;;;;;;;;;;;0BAInB,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;oBAAI,WAAU;;;;;;0BACjC,cAAA,8OAAC,8IAAA,CAAA,eAAY;;;;;;;;;;0BAIf,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;oBAAI,WAAU;;;;;;0BACjC,cAAA,8OAAC,gJAAA,CAAA,iBAAc;;;;;;;;;;0BAIjB,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;oBAAI,WAAU;;;;;;0BACjC,cAAA,8OAAC,8IAAA,CAAA,eAAY;;;;;;;;;;;;;;;;AAIrB", "debugId": null}}]}