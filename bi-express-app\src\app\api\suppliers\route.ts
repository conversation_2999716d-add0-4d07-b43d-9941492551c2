import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const city = searchParams.get('city')
    const businessType = searchParams.get('businessType')

    const where: any = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } },
        { businessName: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (city) {
      where.city = city
    }

    if (businessType) {
      where.businessType = businessType
    }

    const suppliers = await prisma.supplier.findMany({
      where,
      include: {
        products: {
          select: {
            id: true,
            name: true,
            category: true,
            supplierPrice: true,
            logisticRate: true,
            margin: true,
            isActive: true
          },
          where: {
            isActive: true
          },
          take: 10
        },
        _count: {
          select: {
            products: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(suppliers)
  } catch (error) {
    console.error('Erreur lors de la récupération des fournisseurs:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la récupération des fournisseurs' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('Données reçues:', body)

    const {
      name,
      businessName,
      email,
      phone,
      address,
      city,
      country,
      businessType,
      taxId,
      bankAccount,
      paymentTerms,
      specialties,
      rating,
      notes
    } = body

    // Validation des champs obligatoires
    if (!name || !phone || !city) {
      return NextResponse.json(
        { error: 'Les champs nom, téléphone et ville sont obligatoires' },
        { status: 400 }
      )
    }

    // Validation de la ville (doit être une valeur de l'enum)
    const validCities = ['LAGOS', 'ABUJA', 'KANO']
    if (!validCities.includes(city)) {
      return NextResponse.json(
        { error: 'Ville invalide. Valeurs acceptées: LAGOS, ABUJA, KANO' },
        { status: 400 }
      )
    }

    // Vérifier l'unicité du téléphone
    const existingPhone = await prisma.supplier.findFirst({
      where: { phone }
    })

    if (existingPhone) {
      return NextResponse.json(
        { error: 'Ce numéro de téléphone est déjà utilisé' },
        { status: 400 }
      )
    }

    const supplier = await prisma.supplier.create({
      data: {
        name,
        email: email || null,
        phone,
        address: address || '',
        city,
        specialties: (specialties || []).join(','),
        rating: rating ? parseFloat(rating) : 5.0
      }
    })

    return NextResponse.json(supplier, { status: 201 })
  } catch (error) {
    console.error('Erreur lors de la création du fournisseur:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la création du fournisseur' },
      { status: 500 }
    )
  }
}
