'use client'

import { useCustomers } from '@/hooks/useApi'
import { Badge } from '@/components/ui/badge-component'
import { Star, Phone, Mail, MapPin, Package, AlertCircle, Loader2, Truck, ShoppingCart } from 'lucide-react'

const typeColors = {
  LOGISTICS: 'bg-orange-100 text-orange-800',
  COMMERCE: 'bg-green-100 text-green-800'
}

const typeLabels = {
  LOGISTICS: 'Logistique',
  COMMERCE: 'Commerce'
}

const typeIcons = {
  LOGISTICS: Truck,
  COMMERCE: ShoppingCart
}

function StarRating({ rating }: { rating: number }) {
  return (
    <div className="flex items-center space-x-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`h-4 w-4 ${
            star <= rating
              ? 'text-yellow-400 fill-current'
              : 'text-gray-300'
          }`}
        />
      ))}
      <span className="text-sm text-gray-600 ml-1">({rating.toFixed(1)})</span>
    </div>
  )
}

export function CustomersList() {
  const { data: customers, loading, error, refetch } = useCustomers()

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Chargement des clients...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-center py-12">
          <AlertCircle className="h-8 w-8 text-red-500" />
          <div className="ml-3">
            <p className="text-red-600 font-medium">Erreur de chargement</p>
            <p className="text-red-500 text-sm">{error}</p>
            <button 
              type="button"
              onClick={refetch}
              className="mt-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Réessayer
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
        {customers?.map((customer) => {
          const TypeIcon = typeIcons[customer.type]
          return (
            <div key={customer.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {customer.name}
                  </h3>
                  <div className="flex items-center gap-2">
                    <Badge className={typeColors[customer.type]}>
                      <TypeIcon className="h-3 w-3 mr-1" />
                      {typeLabels[customer.type]}
                    </Badge>
                  </div>
                </div>
                <div className="text-right">
                  <StarRating rating={customer.rating || 4.0} />
                </div>
              </div>

              {/* Contact Info */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-600">
                  <Phone className="h-4 w-4 mr-2" />
                  {customer.phone}
                </div>
                {customer.email && (
                  <div className="flex items-center text-sm text-gray-600">
                    <Mail className="h-4 w-4 mr-2" />
                    {customer.email}
                  </div>
                )}
                <div className="flex items-center text-sm text-gray-600">
                  <MapPin className="h-4 w-4 mr-2" />
                  {customer.address || customer.city}
                </div>
              </div>

              {/* Business Info */}
              {customer.businessType && (
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-700 mb-1">Type d'entreprise:</p>
                  <p className="text-sm text-gray-600">{customer.businessType}</p>
                </div>
              )}

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <Package className="h-4 w-4 text-gray-400 mr-1" />
                  </div>
                  <p className="text-lg font-semibold text-gray-900">
                    {customer.orders?.length || 0}
                  </p>
                  <p className="text-xs text-gray-500">Commandes</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <Package className="h-4 w-4 text-gray-400 mr-1" />
                  </div>
                  <p className="text-lg font-semibold text-gray-900">
                    {customer.shipments?.length || 0}
                  </p>
                  <p className="text-xs text-gray-500">Expéditions</p>
                </div>
              </div>

              {/* Actions */}
              <div className="mt-4 flex space-x-2">
                <button 
                  type="button"
                  className="flex-1 bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                >
                  Voir détails
                </button>
                <button 
                  type="button"
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Modifier
                </button>
              </div>
            </div>
          )
        })}
      </div>

      {(!customers || customers.length === 0) && (
        <div className="text-center py-12">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 text-lg">Aucun client trouvé</p>
          <p className="text-gray-400 text-sm mt-1">
            Commencez par ajouter votre premier client
          </p>
        </div>
      )}
    </div>
  )
}
