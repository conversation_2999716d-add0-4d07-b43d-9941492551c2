{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/api/orders/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const status = searchParams.get('status')\n    const customerId = searchParams.get('customerId')\n    const search = searchParams.get('search')\n\n    const where: any = {}\n\n    if (status) {\n      where.status = status\n    }\n\n    if (customerId) {\n      where.customerId = customerId\n    }\n\n    if (search) {\n      where.OR = [\n        { orderNumber: { contains: search, mode: 'insensitive' } },\n        { customer: { name: { contains: search, mode: 'insensitive' } } }\n      ]\n    }\n\n    const orders = await prisma.order.findMany({\n      where,\n      include: {\n        customer: {\n          select: {\n            id: true,\n            name: true,\n            city: true,\n            type: true,\n            phone: true\n          }\n        },\n        orderItems: {\n          include: {\n            product: {\n              select: {\n                id: true,\n                name: true,\n                category: true,\n                supplierPrice: true,\n                logisticRate: true,\n                margin: true\n              }\n            }\n          }\n        },\n        shipment: {\n          select: {\n            id: true,\n            status: true,\n            trackingNumber: true,\n            estimatedDelivery: true\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'desc'\n      }\n    })\n\n    return NextResponse.json(orders)\n  } catch (error) {\n    console.error('Erreur lors de la récupération des commandes:', error)\n    return NextResponse.json(\n      { error: 'Erreur lors de la récupération des commandes' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    \n    const {\n      customerId,\n      items,\n      notes,\n      deliveryAddress,\n      priority\n    } = body\n\n    // Validation des champs obligatoires\n    if (!customerId || !items || items.length === 0) {\n      return NextResponse.json(\n        { error: 'Client et articles sont obligatoires' },\n        { status: 400 }\n      )\n    }\n\n    // Vérifier que le client existe\n    const customer = await prisma.customer.findUnique({\n      where: { id: customerId }\n    })\n\n    if (!customer) {\n      return NextResponse.json(\n        { error: 'Client non trouvé' },\n        { status: 404 }\n      )\n    }\n\n    // Vérifier que tous les produits existent et calculer le total\n    let totalAmount = 0\n    const validatedItems = []\n\n    for (const item of items) {\n      const product = await prisma.product.findUnique({\n        where: { id: item.productId }\n      })\n\n      if (!product) {\n        return NextResponse.json(\n          { error: `Produit ${item.productId} non trouvé` },\n          { status: 404 }\n        )\n      }\n\n      if (!product.isActive) {\n        return NextResponse.json(\n          { error: `Le produit ${product.name} n'est plus disponible` },\n          { status: 400 }\n        )\n      }\n\n      const quantity = parseInt(item.quantity)\n      if (quantity < product.minQuantity) {\n        return NextResponse.json(\n          { error: `Quantité minimale pour ${product.name}: ${product.minQuantity}` },\n          { status: 400 }\n        )\n      }\n\n      // Calculer le prix de vente à partir des données du produit\n      const calculatedPrice = product.supplierPrice * (1 + product.logisticRate) * (1 + product.margin)\n      const unitPrice = parseFloat(item.unitPrice) || calculatedPrice\n      const itemTotal = quantity * unitPrice\n\n      validatedItems.push({\n        productId: item.productId,\n        quantity,\n        unitPrice,\n        totalPrice: itemTotal\n      })\n\n      totalAmount += itemTotal\n    }\n\n    // Générer un numéro de commande unique\n    const orderCount = await prisma.order.count()\n    const orderNumber = `ORD-${new Date().getFullYear()}-${String(orderCount + 1).padStart(6, '0')}`\n\n    // Créer la commande avec les articles\n    const order = await prisma.order.create({\n      data: {\n        orderNumber,\n        customerId,\n        totalAmount,\n        status: 'PENDING',\n        deliveryAddress: deliveryAddress || customer.address,\n        priority: priority || 'MEDIUM',\n        notes: notes || '',\n        orderItems: {\n          create: validatedItems\n        }\n      },\n      include: {\n        customer: {\n          select: {\n            id: true,\n            name: true,\n            city: true,\n            type: true,\n            phone: true\n          }\n        },\n        orderItems: {\n          include: {\n            product: {\n              select: {\n                id: true,\n                name: true,\n                category: true,\n                supplierPrice: true,\n                logisticRate: true,\n                margin: true\n              }\n            }\n          }\n        }\n      }\n    })\n\n    return NextResponse.json(order, { status: 201 })\n  } catch (error) {\n    console.error('Erreur lors de la création de la commande:', error)\n    return NextResponse.json(\n      { error: 'Erreur lors de la création de la commande' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,aAAa,aAAa,GAAG,CAAC;QACpC,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,MAAM,QAAa,CAAC;QAEpB,IAAI,QAAQ;YACV,MAAM,MAAM,GAAG;QACjB;QAEA,IAAI,YAAY;YACd,MAAM,UAAU,GAAG;QACrB;QAEA,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBAAE,aAAa;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACzD;oBAAE,UAAU;wBAAE,MAAM;4BAAE,UAAU;4BAAQ,MAAM;wBAAc;oBAAE;gBAAE;aACjE;QACH;QAEA,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YACzC;YACA,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,OAAO;oBACT;gBACF;gBACA,YAAY;oBACV,SAAS;wBACP,SAAS;4BACP,QAAQ;gCACN,IAAI;gCACJ,MAAM;gCACN,UAAU;gCACV,eAAe;gCACf,cAAc;gCACd,QAAQ;4BACV;wBACF;oBACF;gBACF;gBACA,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,QAAQ;wBACR,gBAAgB;wBAChB,mBAAmB;oBACrB;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA+C,GACxD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,MAAM,EACJ,UAAU,EACV,KAAK,EACL,KAAK,EACL,eAAe,EACf,QAAQ,EACT,GAAG;QAEJ,qCAAqC;QACrC,IAAI,CAAC,cAAc,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuC,GAChD;gBAAE,QAAQ;YAAI;QAElB;QAEA,gCAAgC;QAChC,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE,IAAI;YAAW;QAC1B;QAEA,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,+DAA+D;QAC/D,IAAI,cAAc;QAClB,MAAM,iBAAiB,EAAE;QAEzB,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC9C,OAAO;oBAAE,IAAI,KAAK,SAAS;gBAAC;YAC9B;YAEA,IAAI,CAAC,SAAS;gBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,WAAW,CAAC;gBAAC,GAChD;oBAAE,QAAQ;gBAAI;YAElB;YAEA,IAAI,CAAC,QAAQ,QAAQ,EAAE;gBACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO,CAAC,WAAW,EAAE,QAAQ,IAAI,CAAC,sBAAsB,CAAC;gBAAC,GAC5D;oBAAE,QAAQ;gBAAI;YAElB;YAEA,MAAM,WAAW,SAAS,KAAK,QAAQ;YACvC,IAAI,WAAW,QAAQ,WAAW,EAAE;gBAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO,CAAC,uBAAuB,EAAE,QAAQ,IAAI,CAAC,EAAE,EAAE,QAAQ,WAAW,EAAE;gBAAC,GAC1E;oBAAE,QAAQ;gBAAI;YAElB;YAEA,4DAA4D;YAC5D,MAAM,kBAAkB,QAAQ,aAAa,GAAG,CAAC,IAAI,QAAQ,YAAY,IAAI,CAAC,IAAI,QAAQ,MAAM;YAChG,MAAM,YAAY,WAAW,KAAK,SAAS,KAAK;YAChD,MAAM,YAAY,WAAW;YAE7B,eAAe,IAAI,CAAC;gBAClB,WAAW,KAAK,SAAS;gBACzB;gBACA;gBACA,YAAY;YACd;YAEA,eAAe;QACjB;QAEA,uCAAuC;QACvC,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK;QAC3C,MAAM,cAAc,CAAC,IAAI,EAAE,IAAI,OAAO,WAAW,GAAG,CAAC,EAAE,OAAO,aAAa,GAAG,QAAQ,CAAC,GAAG,MAAM;QAEhG,sCAAsC;QACtC,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YACtC,MAAM;gBACJ;gBACA;gBACA;gBACA,QAAQ;gBACR,iBAAiB,mBAAmB,SAAS,OAAO;gBACpD,UAAU,YAAY;gBACtB,OAAO,SAAS;gBAChB,YAAY;oBACV,QAAQ;gBACV;YACF;YACA,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,OAAO;oBACT;gBACF;gBACA,YAAY;oBACV,SAAS;wBACP,SAAS;4BACP,QAAQ;gCACN,IAAI;gCACJ,MAAM;gCACN,UAAU;gCACV,eAAe;gCACf,cAAc;gCACd,QAAQ;4BACV;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,OAAO;YAAE,QAAQ;QAAI;IAChD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4C,GACrD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}