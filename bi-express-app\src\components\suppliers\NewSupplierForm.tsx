'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { User, MapPin, Phone, Mail, Building, Star } from 'lucide-react'

interface SupplierFormData {
  name: string
  email: string
  phone: string
  address: string
  city: 'LAGOS' | 'ABUJA' | 'KANO'
  contactPerson: string
  businessType: string
  specialties: string
  paymentTerms: string
  notes: string
}

const nigerianCities = [
  { value: 'LAGOS', label: 'Lagos' },
  { value: 'ABUJA', label: 'Abuja' },
  { value: 'KANO', label: 'Kano' }
]

const businessTypes = [
  'Grossiste', 'Fabricant', 'Importateur', 'Distributeur', 'Producteur'
]

const specialtyOptions = [
  'Tissus', 'Cosmétiques', 'Mèches', 'Accessoires', 'Bijoux', 
  'Chaussures', 'Sacs', 'Parfums', 'Produits de beauté'
]

export function NewSupplierForm() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState<SupplierFormData>({
    name: '',
    email: '',
    phone: '',
    address: '',
    city: 'LAGOS',
    contactPerson: '',
    businessType: '',
    specialties: '',
    paymentTerms: '',
    notes: ''
  })

  const handleInputChange = (field: keyof SupplierFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSpecialtyChange = (value: string) => {
    setFormData(prev => ({ ...prev, specialties: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name || !formData.phone || !formData.city) {
      alert('Veuillez remplir tous les champs obligatoires')
      return
    }

    setIsLoading(true)
    try {
      // Adapter les données au format API
      const apiData = {
        name: formData.name,
        phone: formData.phone,
        email: formData.email || undefined,
        address: formData.address || undefined,
        city: formData.city,
        contactPerson: formData.contactPerson || undefined,
        businessType: formData.businessType || undefined,
        specialties: formData.specialties || undefined,
        paymentTerms: formData.paymentTerms || undefined,
        notes: formData.notes || undefined
      }

      const response = await fetch('/api/suppliers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(apiData)
      })

      if (response.ok) {
        router.push('/suppliers')
      } else {
        const errorData = await response.json()
        alert(`Erreur: ${errorData.error || 'Erreur lors de la création du fournisseur'}`)
      }
    } catch (error) {
      console.error('Erreur:', error)
      alert('Erreur lors de la création du fournisseur')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Informations générales */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Informations générales
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nom de l'entreprise *
              </label>
              <input
                type="text"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Ex: ABC Trading Company"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Type d'entreprise
              </label>
              <select
                title="Type d'entreprise"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.businessType}
                onChange={(e) => handleInputChange('businessType', e.target.value)}
              >
                <option value="">Sélectionner un type</option>
                {businessTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Ville *
              </label>
              <select
                required
                title="Ville"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value as 'LAGOS' | 'ABUJA' | 'KANO')}
              >
                {nigerianCities.map(city => (
                  <option key={city.value} value={city.value}>{city.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Adresse complète
              </label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder="Adresse complète du fournisseur"
              />
            </div>
          </CardContent>
        </Card>

        {/* Contact */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Informations de contact
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Personne de contact
              </label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.contactPerson}
                onChange={(e) => handleInputChange('contactPerson', e.target.value)}
                placeholder="Nom du contact principal"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email *
              </label>
              <input
                type="email"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Téléphone *
              </label>
              <input
                type="tel"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="+234 xxx xxx xxxx"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Conditions de paiement
              </label>
              <select
                title="Conditions de paiement"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={formData.paymentTerms}
                onChange={(e) => handleInputChange('paymentTerms', e.target.value)}
              >
                <option value="">Sélectionner</option>
                <option value="Comptant">Comptant</option>
                <option value="30 jours">30 jours</option>
                <option value="60 jours">60 jours</option>
                <option value="90 jours">90 jours</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Spécialités */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              Spécialités
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <p className="text-sm text-gray-600">
                Décrivez les spécialités du fournisseur (ex: Tissus africains, Cosmétiques naturels, Mèches brésiliennes)
              </p>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                value={formData.specialties}
                onChange={(e) => handleSpecialtyChange(e.target.value)}
                placeholder="Ex: Tissus wax premium, Cosmétiques bio, Mèches naturelles..."
              />
            </div>
          </CardContent>
        </Card>

        {/* Notes */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Notes additionnelles</CardTitle>
          </CardHeader>
          <CardContent>
            <textarea
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={4}
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Informations supplémentaires sur le fournisseur..."
            />
          </CardContent>
        </Card>
      </div>

      {/* Boutons d'action */}
      <div className="flex justify-end gap-4 mt-6">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
        >
          Annuler
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
        >
          {isLoading ? 'Création...' : 'Créer le fournisseur'}
        </Button>
      </div>
    </form>
  )
}
