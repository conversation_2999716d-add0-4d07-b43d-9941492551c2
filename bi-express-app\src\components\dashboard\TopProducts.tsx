import { prisma } from '@/lib/prisma'
import { formatCurrency } from '@/lib/utils'
import { Badge } from '@/components/ui/Badge'

async function getTopProducts() {
  const products = await prisma.product.findMany({
    take: 5,
    where: { isActive: true },
    include: {
      supplier: true,
      orderItems: true
    },
    orderBy: {
      stockQuantity: 'desc'
    }
  })

  return products.map(product => ({
    ...product,
    totalSold: product.orderItems.reduce((sum, item) => sum + item.quantity, 0),
    revenue: product.orderItems.reduce((sum, item) => sum + item.totalPrice, 0)
  }))
}

const categoryColors = {
  TISSUS: 'bg-blue-100 text-blue-800',
  COSMETIQUES: 'bg-pink-100 text-pink-800',
  MECHES: 'bg-purple-100 text-purple-800'
}

const categoryLabels = {
  TISSUS: 'Tissus',
  COSMETIQUES: 'Cosmétiques',
  MECHES: 'Mèches'
}

export async function TopProducts() {
  const products = await getTopProducts()

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Produits populaires</h3>
        <p className="text-sm text-gray-600 mt-1">
          Produits les plus en stock et performants
        </p>
      </div>
      
      <div className="space-y-4">
        {products.map((product, index) => (
          <div key={product.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold">
                  {index + 1}
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {product.name}
                </p>
                <div className="flex items-center space-x-2 mt-1">
                  <Badge className={categoryColors[product.category]}>
                    {categoryLabels[product.category]}
                  </Badge>
                  <span className="text-xs text-gray-500">
                    {product.supplier.name}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="text-right">
              <p className="text-sm font-medium text-gray-900">
                {formatCurrency(product.supplierPrice)}
              </p>
              <p className="text-xs text-gray-500">
                Stock: {product.stockQuantity}
              </p>
            </div>
          </div>
        ))}
      </div>
      
      {products.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">Aucun produit disponible</p>
        </div>
      )}
    </div>
  )
}
