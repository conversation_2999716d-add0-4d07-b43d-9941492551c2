'use client'

import Link from 'next/link'
import { 
  Plus, 
  ShoppingCart, 
  Users, 
  Package, 
  Truck, 
  FileText, 
  Calculator,
  Search,
  Bell,
  Settings
} from 'lucide-react'
import { Button } from '@/components/ui/button'

const quickActions = [
  {
    title: 'Nouvelle Commande',
    description: 'Créer une nouvelle commande client',
    icon: ShoppingCart,
    href: '/orders/new',
    color: 'bg-blue-500 hover:bg-blue-600',
    textColor: 'text-white'
  },
  {
    title: 'Ajouter Fournisseur',
    description: 'Enregistrer un nouveau fournisseur',
    icon: Users,
    href: '/suppliers/new',
    color: 'bg-green-500 hover:bg-green-600',
    textColor: 'text-white'
  },
  {
    title: 'Ajouter Produit',
    description: 'Ajouter un nouveau produit au catalogue',
    icon: Package,
    href: '/products/new',
    color: 'bg-purple-500 hover:bg-purple-600',
    textColor: 'text-white'
  },
  {
    title: 'Nouvelle Expédition',
    description: 'Organiser une nouvelle expédition',
    icon: Truck,
    href: '/shipments/new',
    color: 'bg-orange-500 hover:bg-orange-600',
    textColor: 'text-white'
  },
  {
    title: 'Générer Rapport',
    description: 'Créer un rapport de ventes',
    icon: FileText,
    href: '/reports',
    color: 'bg-indigo-500 hover:bg-indigo-600',
    textColor: 'text-white'
  },
  {
    title: 'Calculateur Prix',
    description: 'Calculer prix avec marges',
    icon: Calculator,
    href: '/tools/calculator',
    color: 'bg-teal-500 hover:bg-teal-600',
    textColor: 'text-white'
  }
]

const utilityActions = [
  {
    title: 'Recherche Globale',
    description: 'Rechercher dans toute l\'application',
    icon: Search,
    action: 'search',
    color: 'bg-gray-100 hover:bg-gray-200',
    textColor: 'text-gray-700'
  },
  {
    title: 'Notifications',
    description: 'Voir toutes les notifications',
    icon: Bell,
    href: '/notifications',
    color: 'bg-yellow-100 hover:bg-yellow-200',
    textColor: 'text-yellow-700'
  },
  {
    title: 'Paramètres',
    description: 'Configuration de l\'application',
    icon: Settings,
    href: '/settings',
    color: 'bg-gray-100 hover:bg-gray-200',
    textColor: 'text-gray-700'
  }
]

export function QuickActions() {
  const handleSearch = () => {
    // Ouvrir une modal de recherche ou focus sur le champ de recherche
    const searchInput = document.querySelector('input[type="search"]') as HTMLInputElement
    if (searchInput) {
      searchInput.focus()
    } else {
      // Si pas de champ de recherche, rediriger vers une page de recherche
      window.location.href = '/search'
    }
  }

  const handleAction = (action: string) => {
    switch (action) {
      case 'search':
        handleSearch()
        break
      default:
        break
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Actions Rapides</h2>
        <p className="text-sm text-gray-600">
          Accès direct aux fonctionnalités principales de l'application
        </p>
      </div>

      {/* Actions principales */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-6">
        {quickActions.map((action, index) => (
          <Link key={index} href={action.href}>
            <Button
              className={`w-full h-auto p-4 flex flex-col items-center space-y-2 ${action.color} ${action.textColor} border-0`}
              variant="default"
            >
              <action.icon className="h-6 w-6" />
              <div className="text-center">
                <div className="font-medium text-sm">{action.title}</div>
                <div className="text-xs opacity-90 mt-1">{action.description}</div>
              </div>
            </Button>
          </Link>
        ))}
      </div>

      {/* Actions utilitaires */}
      <div className="border-t border-gray-200 pt-4">
        <h3 className="text-sm font-medium text-gray-700 mb-3">Outils</h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
          {utilityActions.map((action, index) => (
            action.href ? (
              <Link key={index} href={action.href}>
                <Button
                  className={`w-full h-auto p-3 flex items-center space-x-3 ${action.color} ${action.textColor} border border-gray-300`}
                  variant="outline"
                >
                  <action.icon className="h-4 w-4" />
                  <div className="text-left flex-1">
                    <div className="font-medium text-xs">{action.title}</div>
                    <div className="text-xs opacity-75">{action.description}</div>
                  </div>
                </Button>
              </Link>
            ) : (
              <Button
                key={index}
                onClick={() => handleAction(action.action!)}
                className={`w-full h-auto p-3 flex items-center space-x-3 ${action.color} ${action.textColor} border border-gray-300`}
                variant="outline"
              >
                <action.icon className="h-4 w-4" />
                <div className="text-left flex-1">
                  <div className="font-medium text-xs">{action.title}</div>
                  <div className="text-xs opacity-75">{action.description}</div>
                </div>
              </Button>
            )
          ))}
        </div>
      </div>
    </div>
  )
}
