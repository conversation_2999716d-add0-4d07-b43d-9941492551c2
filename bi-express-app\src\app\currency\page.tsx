import { Suspense } from 'react'
import { ExchangeRateWidget } from '@/components/currency/ExchangeRateWidget'
import { CurrencyConverter } from '@/components/currency/CurrencyConverter'
import { getCurrentRates } from '@/lib/currency'
import { TrendingUp, Calculator, Globe, Clock, AlertCircle } from 'lucide-react'

export default async function CurrencyPage() {
  let rates = null
  let error = null

  try {
    rates = await getCurrentRates()
  } catch (err) {
    error = 'Impossible de charger les taux de change'
    console.error('Erreur chargement taux:', err)
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Globe className="h-6 w-6 mr-3 text-blue-600" />
                Gestion des devises
              </h1>
              <p className="text-gray-600 mt-1">
                Conversion automatique NGN/XOF et suivi des taux de change
              </p>
            </div>
            {rates && (
              <div className="text-right">
                <div className="text-sm text-gray-500 flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  Dernière mise à jour: {new Intl.DateTimeFormat('fr-FR', {
                    hour: '2-digit',
                    minute: '2-digit',
                    day: '2-digit',
                    month: '2-digit'
                  }).format(rates.lastUpdated)}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      )}

      {/* Statistiques rapides */}
      {rates && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">NGN → XOF</p>
                <p className="text-2xl font-bold text-gray-900">
                  {rates.ngnToXof.toFixed(4)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">XOF → NGN</p>
                <p className="text-2xl font-bold text-gray-900">
                  {rates.xofToNgn.toFixed(4)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Calculator className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Exemple</p>
                <p className="text-lg font-bold text-gray-900">
                  1000 NGN = {(1000 * rates.ngnToXof).toFixed(0)} XOF
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Contenu principal */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Convertisseur */}
        <div className="lg:col-span-2">
          <Suspense fallback={
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                <div className="h-10 bg-gray-200 rounded"></div>
                <div className="h-10 bg-gray-200 rounded"></div>
              </div>
            </div>
          }>
            <CurrencyConverter />
          </Suspense>
        </div>

        {/* Widget taux de change */}
        <div>
          <Suspense fallback={
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="animate-pulse space-y-3">
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-6 bg-gray-200 rounded"></div>
                <div className="h-6 bg-gray-200 rounded"></div>
              </div>
            </div>
          }>
            <ExchangeRateWidget />
          </Suspense>
        </div>
      </div>

      {/* Informations utiles */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Informations sur les devises
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Naira Nigérian (NGN)</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Devise officielle du Nigeria</li>
              <li>• Code ISO: NGN</li>
              <li>• Symbole: ₦</li>
              <li>• Subdivisions: 100 kobo</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Franc CFA (XOF)</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Devise de l'Union Économique et Monétaire Ouest Africaine</li>
              <li>• Code ISO: XOF</li>
              <li>• Utilisé au Sénégal, Mali, Burkina Faso, etc.</li>
              <li>• Subdivisions: 100 centimes</li>
            </ul>
          </div>
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">💡 Conseils d'utilisation</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Les taux sont mis à jour automatiquement toutes les 30 minutes</li>
            <li>• En cas d'indisponibilité de l'API, des taux de secours sont utilisés</li>
            <li>• Utilisez le convertisseur pour calculer vos marges et prix de vente</li>
            <li>• Les conversions sont arrondies à 2 décimales pour plus de précision</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
