'use client'

import { useOrders } from '@/hooks/useApi'
import { formatCurrency, formatDateTime } from '@/lib/utils'
import { Badge } from '@/components/ui/badge-component'
import {
  Package,
  Truck,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  MapPin,
  Calendar,
  Loader2,
  AlertCircle
} from 'lucide-react'

const statusColors = {
  PENDING: 'bg-yellow-100 text-yellow-800',
  CONFIRMED: 'bg-blue-100 text-blue-800',
  SHIPPED: 'bg-purple-100 text-purple-800',
  DELIVERED: 'bg-green-100 text-green-800',
  CANCELLED: 'bg-red-100 text-red-800'
}

const statusLabels = {
  PENDING: 'En attente',
  CONFIRMED: 'Confirmée',
  SHIPPED: 'Expédiée',
  DELIVERED: 'Livrée',
  CANCELLED: 'Annulée'
}

const statusIcons = {
  PENDING: Clock,
  CONFIRMED: CheckCircle,
  SHIPPED: Truck,
  DELIVERED: CheckCircle,
  CANCELLED: XCircle
}

const transportModeLabels = {
  ROAD: 'Routier (5-7j)',
  AIR: 'Aérien (24-48h)'
}

const transportModeColors = {
  ROAD: 'bg-blue-50 text-blue-700',
  AIR: 'bg-purple-50 text-purple-700'
}

function getDeliveryEstimate(createdAt: Date, transportMode: string) {
  const created = new Date(createdAt)
  const days = transportMode === 'AIR' ? 2 : 6
  const estimate = new Date(created.getTime() + days * 24 * 60 * 60 * 1000)
  return estimate
}

export function OrdersList() {
  const { data: orders, loading, error, refetch } = useOrders()

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Chargement des commandes...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-center py-12">
          <AlertCircle className="h-8 w-8 text-red-500" />
          <div className="ml-3">
            <p className="text-red-600 font-medium">Erreur de chargement</p>
            <p className="text-red-500 text-sm">{error}</p>
            <button
              type="button"
              onClick={refetch}
              className="mt-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Réessayer
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (!orders || orders.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center py-12">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 text-lg">Aucune commande trouvée</p>
          <p className="text-gray-400 text-sm mt-1">
            Commencez par créer votre première commande
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Commande
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Client
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Fournisseur
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Transport
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Montant
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Statut
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Livraison
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {orders.map((order) => {
              const StatusIcon = statusIcons[order.status]
              const deliveryEstimate = getDeliveryEstimate(order.createdAt, order.transportMode)
              const isOverdue = order.status === 'SHIPPED' && new Date() > deliveryEstimate

              return (
                <tr key={order.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                          <Package className="h-5 w-5 text-white" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {order.orderNumber}
                        </div>
                        <div className="text-sm text-gray-500">
                          {order.orderItems.length} article{order.orderItems.length > 1 ? 's' : ''}
                        </div>
                        <div className="text-xs text-gray-400">
                          {formatDateTime(order.createdAt)}
                        </div>
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {order.customer.name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {order.customer.phone}
                    </div>
                    <div className="flex items-center text-xs text-gray-400 mt-1">
                      <MapPin className="h-3 w-3 mr-1" />
                      Dakar, Sénégal
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {order.supplier.name}
                    </div>
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                      <MapPin className="h-3 w-3 mr-1" />
                      {order.supplier.city}, Nigeria
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge className={transportModeColors[order.transportMode]}>
                      {transportModeLabels[order.transportMode]}
                    </Badge>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {formatCurrency(order.totalAmount)}
                    </div>
                    <div className="text-sm text-green-600">
                      +{formatCurrency(order.totalProfit)} bénéfice
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <StatusIcon className={`h-4 w-4 mr-2 ${
                        order.status === 'DELIVERED' ? 'text-green-500' :
                        order.status === 'CANCELLED' ? 'text-red-500' :
                        order.status === 'SHIPPED' ? 'text-purple-500' :
                        order.status === 'CONFIRMED' ? 'text-blue-500' :
                        'text-yellow-500'
                      }`} />
                      <Badge className={statusColors[order.status]}>
                        {statusLabels[order.status]}
                      </Badge>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    {order.status === 'DELIVERED' ? (
                      <div className="text-sm text-green-600 font-medium">
                        Livrée
                      </div>
                    ) : order.status === 'CANCELLED' ? (
                      <div className="text-sm text-red-600">
                        Annulée
                      </div>
                    ) : (
                      <div>
                        <div className={`text-sm ${isOverdue ? 'text-red-600 font-medium' : 'text-gray-900'}`}>
                          {formatDateTime(deliveryEstimate).split(' ')[0]}
                        </div>
                        <div className="flex items-center text-xs text-gray-500 mt-1">
                          <Calendar className="h-3 w-3 mr-1" />
                          {isOverdue ? 'En retard' : 'Estimée'}
                        </div>
                      </div>
                    )}
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        type="button"
                        title="Voir les détails"
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        type="button"
                        title="Modifier"
                        className="text-gray-600 hover:text-gray-900"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>
      
      {orders.length === 0 && (
        <div className="text-center py-12">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 text-lg">Aucune commande trouvée</p>
          <p className="text-gray-400 text-sm mt-1">
            Les nouvelles commandes apparaîtront ici
          </p>
        </div>
      )}
    </div>
  )
}
