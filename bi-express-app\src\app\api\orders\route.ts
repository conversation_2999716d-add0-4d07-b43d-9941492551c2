import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const customerId = searchParams.get('customerId')
    const search = searchParams.get('search')

    const where: any = {}

    if (status) {
      where.status = status
    }

    if (customerId) {
      where.customerId = customerId
    }

    if (search) {
      where.OR = [
        { orderNumber: { contains: search, mode: 'insensitive' } },
        { customer: { name: { contains: search, mode: 'insensitive' } } }
      ]
    }

    const orders = await prisma.order.findMany({
      where,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            city: true,
            type: true,
            phone: true
          }
        },
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                category: true,
                supplierPrice: true,
                logisticRate: true,
                margin: true
              }
            }
          }
        },
        shipment: {
          select: {
            id: true,
            status: true,
            trackingNumber: true,
            estimatedDelivery: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(orders)
  } catch (error) {
    console.error('Erreur lors de la récupération des commandes:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la récupération des commandes' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('Données commande reçues:', body)

    const {
      customerId,
      items,
      transportMode,
      notes,
      deliveryAddress,
      priority
    } = body

    // Validation des champs obligatoires
    if (!customerId || !items || items.length === 0) {
      return NextResponse.json(
        { error: 'Client et articles sont obligatoires' },
        { status: 400 }
      )
    }

    // Vérifier que le client existe
    const customer = await prisma.customer.findUnique({
      where: { id: customerId }
    })

    if (!customer) {
      return NextResponse.json(
        { error: 'Client non trouvé' },
        { status: 404 }
      )
    }

    // Vérifier que tous les produits existent et calculer le total
    let totalAmount = 0
    const validatedItems = []

    for (const item of items) {
      const product = await prisma.product.findUnique({
        where: { id: item.productId }
      })

      if (!product) {
        return NextResponse.json(
          { error: `Produit ${item.productId} non trouvé` },
          { status: 404 }
        )
      }

      if (!product.isActive) {
        return NextResponse.json(
          { error: `Le produit ${product.name} n'est plus disponible` },
          { status: 400 }
        )
      }

      const quantity = parseInt(item.quantity)
      if (quantity < product.minQuantity) {
        return NextResponse.json(
          { error: `Quantité minimale pour ${product.name}: ${product.minQuantity}` },
          { status: 400 }
        )
      }

      // Calculer le prix de vente à partir des données du produit
      const calculatedPrice = product.supplierPrice * (1 + product.logisticRate) * (1 + product.margin)
      const unitPrice = parseFloat(item.unitPrice) || calculatedPrice
      const itemTotal = quantity * unitPrice
      const itemProfit = (unitPrice - product.supplierPrice) * quantity

      validatedItems.push({
        productId: item.productId,
        quantity,
        unitPrice,
        totalPrice: itemTotal,
        profit: itemProfit
      })

      totalAmount += itemTotal
    }

    // Générer un numéro de commande unique
    const orderCount = await prisma.order.count()
    const orderNumber = `ORD-${new Date().getFullYear()}-${String(orderCount + 1).padStart(6, '0')}`

    // Obtenir le supplierId du premier produit (pour simplifier)
    const firstProduct = await prisma.product.findUnique({
      where: { id: validatedItems[0].productId },
      select: { supplierId: true }
    })

    if (!firstProduct) {
      return NextResponse.json(
        { error: 'Produit non trouvé' },
        { status: 404 }
      )
    }

    // Créer la commande avec les articles
    const order = await prisma.order.create({
      data: {
        orderNumber,
        customerId,
        supplierId: firstProduct.supplierId,
        transportMode,
        subtotal: totalAmount,
        logisticCosts: 0, // À calculer selon la logique métier
        totalAmount,
        totalProfit: 0, // À calculer selon la logique métier
        deliveryAddress: deliveryAddress || customer.address,
        orderItems: {
          create: validatedItems
        }
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            city: true,
            type: true,
            phone: true
          }
        },
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                category: true,
                supplierPrice: true,
                logisticRate: true,
                margin: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json(order, { status: 201 })
  } catch (error) {
    console.error('Erreur lors de la création de la commande:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la création de la commande', details: error.message },
      { status: 500 }
    )
  }
}
