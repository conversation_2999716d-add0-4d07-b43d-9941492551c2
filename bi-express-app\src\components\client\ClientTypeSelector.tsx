'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Truck, 
  ShoppingCart, 
  ArrowRight,
  Check,
  Package,
  DollarSign,
  Clock,
  Users
} from 'lucide-react'

interface ClientTypeSelectorProps {
  onSelect?: (type: 'LOGISTICS' | 'COMMERCE') => void
  showComparison?: boolean
}

export function ClientTypeSelector({ onSelect, showComparison = true }: ClientTypeSelectorProps) {
  const [selectedType, setSelectedType] = useState<'LOGISTICS' | 'COMMERCE' | null>(null)
  const router = useRouter()

  const handleSelect = (type: 'LOGISTICS' | 'COMMERCE') => {
    setSelectedType(type)
    if (onSelect) {
      onSelect(type)
    } else {
      // Navigation par défaut
      const route = type === 'LOGISTICS' ? '/logistics-client' : '/commerce-client'
      router.push(route)
    }
  }

  const clientTypes = {
    LOGISTICS: {
      title: 'Client Logistique',
      subtitle: 'Transport uniquement',
      description: 'Vous avez déjà vos produits et cherchez uniquement un service de transport fiable entre le Nigeria et Dakar.',
      icon: Truck,
      color: 'blue',
      features: [
        'Transport de vos marchandises existantes',
        'Tarification transparente au poids/volume',
        'Suivi en temps réel de vos expéditions',
        'Consolidation avec d\'autres clients',
        'Assurance transport optionnelle',
        'Enlèvement et livraison inclus'
      ],
      pricing: {
        base: 'À partir de 850 CFA/kg',
        margin: '20% de marge service',
        example: 'Ex: 100kg Lagos-Dakar = ~85,000 CFA'
      },
      advantages: [
        'Prix compétitifs',
        'Flexibilité totale',
        'Pas d\'intermédiaire produit'
      ]
    },
    COMMERCE: {
      title: 'Client Commerce',
      subtitle: 'Achat + Transport intégré',
      description: 'Vous souhaitez acheter des produits au Nigeria et les recevoir directement à Dakar, avec un service tout-en-un.',
      icon: ShoppingCart,
      color: 'purple',
      features: [
        'Catalogue de produits sélectionnés',
        'Prix "rendu Dakar" tout inclus',
        'Sourcing et négociation inclus',
        'Contrôle qualité avant expédition',
        'Gestion complète de A à Z',
        'Garantie satisfaction'
      ],
      pricing: {
        base: 'Prix produit + 35% marge globale',
        margin: 'Transport et marge intégrés',
        example: 'Ex: Produit 100k + transport = ~135k CFA'
      },
      advantages: [
        'Service clé en main',
        'Pas de gestion logistique',
        'Garantie qualité'
      ]
    }
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Choisissez votre type de service
        </h2>
        <p className="text-gray-600">
          Sélectionnez le service qui correspond le mieux à vos besoins
        </p>
      </div>

      {/* Sélection des types */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Object.entries(clientTypes).map(([type, config]) => {
          const Icon = config.icon
          const isSelected = selectedType === type
          const colorClasses = {
            blue: {
              border: isSelected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200 hover:border-blue-300',
              bg: 'bg-blue-100',
              text: 'text-blue-600',
              button: 'bg-blue-600 hover:bg-blue-700'
            },
            purple: {
              border: isSelected ? 'border-purple-500 ring-2 ring-purple-200' : 'border-gray-200 hover:border-purple-300',
              bg: 'bg-purple-100',
              text: 'text-purple-600',
              button: 'bg-purple-600 hover:bg-purple-700'
            }
          }
          const colors = colorClasses[config.color as keyof typeof colorClasses]

          return (
            <Card 
              key={type}
              className={`cursor-pointer transition-all duration-200 ${colors.border}`}
              onClick={() => handleSelect(type as 'LOGISTICS' | 'COMMERCE')}
            >
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${colors.bg}`}>
                      <Icon className={`h-6 w-6 ${colors.text}`} />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{config.title}</CardTitle>
                      <CardDescription className="font-medium">
                        {config.subtitle}
                      </CardDescription>
                    </div>
                  </div>
                  {isSelected && (
                    <div className={`p-1 rounded-full ${colors.bg}`}>
                      <Check className={`h-4 w-4 ${colors.text}`} />
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  {config.description}
                </p>

                {/* Fonctionnalités */}
                <div className="space-y-2 mb-4">
                  <h4 className="font-medium text-gray-900">Fonctionnalités incluses</h4>
                  <ul className="space-y-1">
                    {config.features.slice(0, 4).map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <Check className="h-3 w-3 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                    {config.features.length > 4 && (
                      <li className="text-sm text-gray-500">
                        +{config.features.length - 4} autres fonctionnalités
                      </li>
                    )}
                  </ul>
                </div>

                {/* Tarification */}
                <div className="bg-gray-50 rounded-lg p-3 mb-4">
                  <h4 className="font-medium text-gray-900 mb-2">Tarification</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Base:</span>
                      <span className="font-medium">{config.pricing.base}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Marge:</span>
                      <span className="font-medium">{config.pricing.margin}</span>
                    </div>
                    <div className="text-xs text-gray-500 mt-2">
                      {config.pricing.example}
                    </div>
                  </div>
                </div>

                {/* Avantages */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {config.advantages.map((advantage, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {advantage}
                    </Badge>
                  ))}
                </div>

                <Button 
                  className={`w-full ${colors.button} text-white`}
                  onClick={(e) => {
                    e.stopPropagation()
                    handleSelect(type as 'LOGISTICS' | 'COMMERCE')
                  }}
                >
                  Choisir ce service
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Tableau comparatif */}
      {showComparison && (
        <Card>
          <CardHeader>
            <CardTitle>Comparaison des services</CardTitle>
            <CardDescription>
              Tableau détaillé pour vous aider à choisir
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Critère</th>
                    <th className="text-center py-3 px-4">
                      <div className="flex items-center justify-center">
                        <Truck className="h-4 w-4 mr-2 text-blue-600" />
                        Logistique
                      </div>
                    </th>
                    <th className="text-center py-3 px-4">
                      <div className="flex items-center justify-center">
                        <ShoppingCart className="h-4 w-4 mr-2 text-purple-600" />
                        Commerce
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b">
                    <td className="py-3 px-4 font-medium">Sourcing produits</td>
                    <td className="py-3 px-4 text-center text-red-600">✗ À votre charge</td>
                    <td className="py-3 px-4 text-center text-green-600">✓ Inclus</td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-3 px-4 font-medium">Contrôle qualité</td>
                    <td className="py-3 px-4 text-center text-red-600">✗ À votre charge</td>
                    <td className="py-3 px-4 text-center text-green-600">✓ Inclus</td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-3 px-4 font-medium">Transport</td>
                    <td className="py-3 px-4 text-center text-green-600">✓ Inclus</td>
                    <td className="py-3 px-4 text-center text-green-600">✓ Inclus</td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-3 px-4 font-medium">Flexibilité prix</td>
                    <td className="py-3 px-4 text-center text-green-600">✓ Élevée</td>
                    <td className="py-3 px-4 text-center text-orange-600">~ Modérée</td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-3 px-4 font-medium">Simplicité</td>
                    <td className="py-3 px-4 text-center text-orange-600">~ Modérée</td>
                    <td className="py-3 px-4 text-center text-green-600">✓ Maximale</td>
                  </tr>
                  <tr>
                    <td className="py-3 px-4 font-medium">Marge appliquée</td>
                    <td className="py-3 px-4 text-center">20% sur transport</td>
                    <td className="py-3 px-4 text-center">35% globale</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Aide à la décision */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-blue-600">
              <Truck className="h-5 w-5 mr-2" />
              Choisir Logistique si...
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-start">
                <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                Vous avez déjà vos fournisseurs au Nigeria
              </li>
              <li className="flex items-start">
                <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                Vous maîtrisez la qualité de vos produits
              </li>
              <li className="flex items-start">
                <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                Vous voulez optimiser vos coûts de transport
              </li>
              <li className="flex items-start">
                <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                Vous expédiez régulièrement
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-purple-600">
              <ShoppingCart className="h-5 w-5 mr-2" />
              Choisir Commerce si...
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-start">
                <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                Vous débutez dans l'import Nigeria-Dakar
              </li>
              <li className="flex items-start">
                <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                Vous voulez un service clé en main
              </li>
              <li className="flex items-start">
                <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                Vous n'avez pas de contacts au Nigeria
              </li>
              <li className="flex items-start">
                <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                Vous préférez un prix fixe "rendu Dakar"
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
