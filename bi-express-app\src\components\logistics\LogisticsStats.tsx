import { getShipments } from '@/lib/actions/shipments'
import { calculateLogisticsKPIs, formatDuration } from '@/lib/logistics'
import { Package, Truck, Clock, TrendingUp, DollarSign, CheckCircle } from 'lucide-react'

async function getLogisticsStats() {
  const shipments = await getShipments()
  const kpis = calculateLogisticsKPIs(shipments)
  
  return {
    ...kpis,
    shipments
  }
}

export async function LogisticsStats() {
  const stats = await getLogisticsStats()

  const statCards = [
    {
      title: 'Total Expéditions',
      value: stats.totalShipments,
      icon: Package,
      color: 'bg-blue-50 text-blue-600',
      bgColor: 'bg-blue-500'
    },
    {
      title: 'En Transit',
      value: stats.inTransitShipments,
      icon: Truck,
      color: 'bg-yellow-50 text-yellow-600',
      bgColor: 'bg-yellow-500'
    },
    {
      title: 'Livrées',
      value: stats.deliveredShipments,
      icon: CheckCircle,
      color: 'bg-green-50 text-green-600',
      bgColor: 'bg-green-500'
    },
    {
      title: 'Temps Moyen',
      value: formatDuration(stats.averageDeliveryTime),
      icon: Clock,
      color: 'bg-purple-50 text-purple-600',
      bgColor: 'bg-purple-500'
    },
    {
      title: 'Taux Ponctualité',
      value: `${stats.onTimeDeliveryRate}%`,
      icon: TrendingUp,
      color: 'bg-indigo-50 text-indigo-600',
      bgColor: 'bg-indigo-500'
    },
    {
      title: 'Coûts Transport',
      value: `${stats.totalTransportCosts.toLocaleString()} XOF`,
      icon: DollarSign,
      color: 'bg-orange-50 text-orange-600',
      bgColor: 'bg-orange-500'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
      {statCards.map((stat, index) => {
        const Icon = stat.icon
        return (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center">
              <div className={`h-10 w-10 rounded-lg flex items-center justify-center ${stat.color}`}>
                <Icon className="h-5 w-5" />
              </div>
              <div className="ml-3 flex-1">
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-lg font-semibold text-gray-900">{stat.value}</p>
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}
