import { Suspense } from 'react'
import { OrdersList } from '@/components/orders/OrdersList'
import { OrderFilters } from '@/components/orders/OrderFilters'
import { CreateOrderButton } from '@/components/orders/CreateOrderButton'
import { OrderStats } from '@/components/orders/OrderStats'

export default function OrdersPage() {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 pb-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Commandes</h1>
          <p className="text-gray-600 mt-1">
            G<PERSON><PERSON> vos commandes avec suivi logistique Nigeria-Dakar
          </p>
        </div>
        <CreateOrderButton />
      </div>

      {/* Stats */}
      <Suspense fallback={<div className="animate-pulse h-24 bg-gray-200 rounded-lg" />}>
        <OrderStats />
      </Suspense>

      {/* Filters */}
      <Suspense fallback={<div className="animate-pulse h-16 bg-gray-200 rounded-lg" />}>
        <OrderFilters />
      </Suspense>

      {/* Orders List */}
      <Suspense fallback={<div className="animate-pulse h-96 bg-gray-200 rounded-lg" />}>
        <OrdersList />
      </Suspense>
    </div>
  )
}
