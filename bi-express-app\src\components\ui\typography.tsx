import * as React from "react"
import { cn } from "@/lib/utils"

interface TypographyProps extends React.HTMLAttributes<HTMLElement> {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body' | 'caption' | 'overline'
  component?: keyof JSX.IntrinsicElements
  color?: 'primary' | 'secondary' | 'muted' | 'success' | 'warning' | 'error'
}

const variantClasses = {
  h1: "text-3xl font-bold text-gray-900 leading-tight",
  h2: "text-2xl font-semibold text-gray-900 leading-tight",
  h3: "text-xl font-semibold text-gray-900 leading-snug",
  h4: "text-lg font-medium text-gray-900 leading-snug",
  h5: "text-base font-medium text-gray-900 leading-normal",
  h6: "text-sm font-medium text-gray-900 leading-normal",
  body: "text-sm text-gray-700 leading-relaxed",
  caption: "text-sm text-gray-500 leading-normal",
  overline: "text-sm font-medium text-gray-500 uppercase tracking-wide leading-normal"
}

const colorClasses = {
  primary: "text-gray-900",
  secondary: "text-gray-700",
  muted: "text-gray-500",
  success: "text-green-700",
  warning: "text-orange-700",
  error: "text-red-700"
}

const defaultComponents = {
  h1: 'h1',
  h2: 'h2',
  h3: 'h3',
  h4: 'h4',
  h5: 'h5',
  h6: 'h6',
  body: 'p',
  caption: 'span',
  overline: 'span'
} as const

export function Typography({ 
  variant = 'body', 
  component, 
  color = 'primary',
  className, 
  children, 
  ...props 
}: TypographyProps) {
  const Component = component || defaultComponents[variant]
  
  return React.createElement(
    Component,
    {
      className: cn(
        variantClasses[variant],
        colorClasses[color],
        className
      ),
      ...props
    },
    children
  )
}

// Convenience components
export const Heading1 = (props: Omit<TypographyProps, 'variant'>) => 
  <Typography variant="h1" {...props} />

export const Heading2 = (props: Omit<TypographyProps, 'variant'>) => 
  <Typography variant="h2" {...props} />

export const Heading3 = (props: Omit<TypographyProps, 'variant'>) => 
  <Typography variant="h3" {...props} />

export const Heading4 = (props: Omit<TypographyProps, 'variant'>) => 
  <Typography variant="h4" {...props} />

export const Body = (props: Omit<TypographyProps, 'variant'>) => 
  <Typography variant="body" {...props} />

export const Caption = (props: Omit<TypographyProps, 'variant'>) => 
  <Typography variant="caption" {...props} />
