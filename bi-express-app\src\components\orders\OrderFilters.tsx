'use client'

import { useState } from 'react'
import { Search, Filter, Calendar } from 'lucide-react'

export function OrderFilters() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [selectedTransport, setSelectedTransport] = useState('')
  const [dateRange, setDateRange] = useState('')

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {/* Search */}
        <div className="lg:col-span-2 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Rechercher par numéro, client..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Status Filter */}
        <div>
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Tous les statuts</option>
            <option value="PENDING">En attente</option>
            <option value="CONFIRMED">Confirmée</option>
            <option value="SHIPPED">Expédiée</option>
            <option value="DELIVERED">Livrée</option>
            <option value="CANCELLED">Annulée</option>
          </select>
        </div>

        {/* Transport Filter */}
        <div>
          <select
            value={selectedTransport}
            onChange={(e) => setSelectedTransport(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Tous transports</option>
            <option value="ROAD">Routier (5-7j)</option>
            <option value="AIR">Aérien (24-48h)</option>
          </select>
        </div>

        {/* Date Range */}
        <div>
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Toutes les dates</option>
            <option value="today">Aujourd'hui</option>
            <option value="week">Cette semaine</option>
            <option value="month">Ce mois</option>
            <option value="quarter">Ce trimestre</option>
          </select>
        </div>
      </div>

      {/* Active Filters */}
      {(searchTerm || selectedStatus || selectedTransport || dateRange) && (
        <div className="mt-4 flex flex-wrap items-center gap-2">
          <span className="text-sm text-gray-500">Filtres actifs:</span>
          
          {searchTerm && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Recherche: {searchTerm}
              <button
                onClick={() => setSearchTerm('')}
                className="ml-1 text-blue-600 hover:text-blue-800"
              >
                ×
              </button>
            </span>
          )}
          
          {selectedStatus && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              Statut: {selectedStatus}
              <button
                onClick={() => setSelectedStatus('')}
                className="ml-1 text-purple-600 hover:text-purple-800"
              >
                ×
              </button>
            </span>
          )}
          
          {selectedTransport && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Transport: {selectedTransport}
              <button
                onClick={() => setSelectedTransport('')}
                className="ml-1 text-green-600 hover:text-green-800"
              >
                ×
              </button>
            </span>
          )}
          
          {dateRange && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
              Période: {dateRange}
              <button
                onClick={() => setDateRange('')}
                className="ml-1 text-yellow-600 hover:text-yellow-800"
              >
                ×
              </button>
            </span>
          )}
          
          <button
            onClick={() => {
              setSearchTerm('')
              setSelectedStatus('')
              setSelectedTransport('')
              setDateRange('')
            }}
            className="text-xs text-gray-500 hover:text-gray-700 underline"
          >
            Effacer tous les filtres
          </button>
        </div>
      )}

      {/* Quick Actions */}
      <div className="mt-4 flex items-center space-x-4 pt-4 border-t border-gray-200">
        <span className="text-sm text-gray-500">Actions rapides:</span>
        <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
          Commandes en retard
        </button>
        <button className="text-sm text-purple-600 hover:text-purple-800 font-medium">
          En transit
        </button>
        <button className="text-sm text-yellow-600 hover:text-yellow-800 font-medium">
          À confirmer
        </button>
      </div>
    </div>
  )
}
