'use client'

import { useShipments } from '@/hooks/useApi'
import { formatCurrency } from '@/lib/utils'
import { 
  Package, 
  Truck, 
  Clock, 
  CheckCircle,
  TrendingUp,
  Loader2
} from 'lucide-react'

export function ShipmentStats() {
  const { data: shipments, loading } = useShipments()

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-center">
              <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (!shipments) {
    return null
  }

  // Calculate statistics
  const totalShipments = shipments.length
  const inTransit = shipments.filter(s => s.status === 'IN_TRANSIT').length
  const delivered = shipments.filter(s => s.status === 'DELIVERED').length
  const pending = shipments.filter(s => s.status === 'PENDING').length
  
  const totalRevenue = shipments.reduce((sum, shipment) => sum + (shipment.totalCost || 0), 0)
  const averageWeight = totalShipments > 0 
    ? shipments.reduce((sum, shipment) => sum + (shipment.weight || 0), 0) / totalShipments 
    : 0

  const deliveryRate = totalShipments > 0 ? (delivered / totalShipments) * 100 : 0

  const stats = [
    {
      name: 'Total Expéditions',
      value: totalShipments.toString(),
      icon: Package,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      change: '+12%',
      changeType: 'positive' as const
    },
    {
      name: 'En Transit',
      value: inTransit.toString(),
      icon: Truck,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      change: `${pending} en attente`,
      changeType: 'neutral' as const
    },
    {
      name: 'Taux de Livraison',
      value: `${deliveryRate.toFixed(1)}%`,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      change: `${delivered} livrées`,
      changeType: 'positive' as const
    },
    {
      name: 'Chiffre d\'Affaires',
      value: formatCurrency(totalRevenue, 'XOF'),
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      change: `${averageWeight.toFixed(1)}kg moy.`,
      changeType: 'neutral' as const
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat) => {
        const Icon = stat.icon
        return (
          <div key={stat.name} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className={`flex-shrink-0 ${stat.bgColor} rounded-lg p-3`}>
                <Icon className={`h-6 w-6 ${stat.color}`} />
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
            </div>
            <div className="mt-4">
              <div className={`flex items-center text-sm ${
                stat.changeType === 'positive' 
                  ? 'text-green-600' 
                  : stat.changeType === 'negative' 
                  ? 'text-red-600' 
                  : 'text-gray-600'
              }`}>
                {stat.changeType === 'positive' && (
                  <TrendingUp className="h-4 w-4 mr-1" />
                )}
                <span>{stat.change}</span>
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}
