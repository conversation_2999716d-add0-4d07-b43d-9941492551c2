
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.11.1
 * Query Engine version: f40f79ec31188888a2e33acda0ecc8fd10a853a9
 */
Prisma.prismaVersion = {
  client: "6.11.1",
  engine: "f40f79ec31188888a2e33acda0ecc8fd10a853a9"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.SupplierScalarFieldEnum = {
  id: 'id',
  name: 'name',
  phone: 'phone',
  email: 'email',
  address: 'address',
  city: 'city',
  specialties: 'specialties',
  rating: 'rating',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SupplierEvaluationScalarFieldEnum = {
  id: 'id',
  supplierId: 'supplierId',
  rating: 'rating',
  comment: 'comment',
  orderId: 'orderId',
  createdAt: 'createdAt'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  category: 'category',
  fabricType: 'fabricType',
  width: 'width',
  color: 'color',
  pattern: 'pattern',
  brand: 'brand',
  volume: 'volume',
  origin: 'origin',
  length: 'length',
  texture: 'texture',
  hairType: 'hairType',
  supplierPrice: 'supplierPrice',
  logisticRate: 'logisticRate',
  margin: 'margin',
  stockQuantity: 'stockQuantity',
  minStockAlert: 'minStockAlert',
  isActive: 'isActive',
  imageUrl: 'imageUrl',
  weight: 'weight',
  dimensions: 'dimensions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  supplierId: 'supplierId'
};

exports.Prisma.PriceHistoryScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  supplierPrice: 'supplierPrice',
  margin: 'margin',
  createdAt: 'createdAt'
};

exports.Prisma.CustomerScalarFieldEnum = {
  id: 'id',
  name: 'name',
  phone: 'phone',
  email: 'email',
  address: 'address',
  city: 'city',
  isActive: 'isActive',
  creditLimit: 'creditLimit',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  orderNumber: 'orderNumber',
  customerId: 'customerId',
  supplierId: 'supplierId',
  status: 'status',
  transportMode: 'transportMode',
  subtotal: 'subtotal',
  logisticCosts: 'logisticCosts',
  totalAmount: 'totalAmount',
  totalProfit: 'totalProfit',
  orderDate: 'orderDate',
  expectedDelivery: 'expectedDelivery',
  deliveredAt: 'deliveredAt',
  deliveryAddress: 'deliveryAddress',
  trackingNumber: 'trackingNumber',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderItemScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  productId: 'productId',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  totalPrice: 'totalPrice',
  profit: 'profit'
};

exports.Prisma.CarrierScalarFieldEnum = {
  id: 'id',
  name: 'name',
  phone: 'phone',
  email: 'email',
  address: 'address',
  city: 'city',
  transportModes: 'transportModes',
  capacity: 'capacity',
  rating: 'rating',
  onTimeRate: 'onTimeRate',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ShipmentScalarFieldEnum = {
  id: 'id',
  trackingNumber: 'trackingNumber',
  orderId: 'orderId',
  carrierId: 'carrierId',
  status: 'status',
  transportMode: 'transportMode',
  originCity: 'originCity',
  destinationCity: 'destinationCity',
  currentLocation: 'currentLocation',
  pickupDate: 'pickupDate',
  departureDate: 'departureDate',
  arrivalDate: 'arrivalDate',
  deliveryDate: 'deliveryDate',
  estimatedDelivery: 'estimatedDelivery',
  weight: 'weight',
  transportCost: 'transportCost',
  fuelSurcharge: 'fuelSurcharge',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TrackingEventScalarFieldEnum = {
  id: 'id',
  shipmentId: 'shipmentId',
  eventType: 'eventType',
  location: 'location',
  description: 'description',
  timestamp: 'timestamp'
};

exports.Prisma.CarrierEvaluationScalarFieldEnum = {
  id: 'id',
  carrierId: 'carrierId',
  shipmentId: 'shipmentId',
  punctuality: 'punctuality',
  condition: 'condition',
  communication: 'communication',
  overall: 'overall',
  comment: 'comment',
  createdAt: 'createdAt'
};

exports.Prisma.ExchangeRateScalarFieldEnum = {
  id: 'id',
  fromCurrency: 'fromCurrency',
  toCurrency: 'toCurrency',
  rate: 'rate',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.SupplierCity = exports.$Enums.SupplierCity = {
  LAGOS: 'LAGOS',
  ABUJA: 'ABUJA',
  KANO: 'KANO'
};

exports.ProductCategory = exports.$Enums.ProductCategory = {
  TISSUS: 'TISSUS',
  COSMETIQUES: 'COSMETIQUES',
  MECHES: 'MECHES'
};

exports.OrderStatus = exports.$Enums.OrderStatus = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED'
};

exports.TransportMode = exports.$Enums.TransportMode = {
  ROAD: 'ROAD',
  AIR_EXPRESS: 'AIR_EXPRESS'
};

exports.ShipmentStatus = exports.$Enums.ShipmentStatus = {
  PENDING: 'PENDING',
  PICKED_UP: 'PICKED_UP',
  IN_TRANSIT: 'IN_TRANSIT',
  CUSTOMS: 'CUSTOMS',
  OUT_FOR_DELIVERY: 'OUT_FOR_DELIVERY',
  DELIVERED: 'DELIVERED',
  DELAYED: 'DELAYED',
  CANCELLED: 'CANCELLED'
};

exports.TrackingEventType = exports.$Enums.TrackingEventType = {
  PICKUP_SCHEDULED: 'PICKUP_SCHEDULED',
  PICKED_UP: 'PICKED_UP',
  DEPARTED: 'DEPARTED',
  IN_TRANSIT: 'IN_TRANSIT',
  ARRIVED_HUB: 'ARRIVED_HUB',
  CUSTOMS_CLEARANCE: 'CUSTOMS_CLEARANCE',
  OUT_FOR_DELIVERY: 'OUT_FOR_DELIVERY',
  DELIVERED: 'DELIVERED',
  DELIVERY_FAILED: 'DELIVERY_FAILED',
  DELAYED: 'DELAYED'
};

exports.Prisma.ModelName = {
  Supplier: 'Supplier',
  SupplierEvaluation: 'SupplierEvaluation',
  Product: 'Product',
  PriceHistory: 'PriceHistory',
  Customer: 'Customer',
  Order: 'Order',
  OrderItem: 'OrderItem',
  Carrier: 'Carrier',
  Shipment: 'Shipment',
  TrackingEvent: 'TrackingEvent',
  CarrierEvaluation: 'CarrierEvaluation',
  ExchangeRate: 'ExchangeRate'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
