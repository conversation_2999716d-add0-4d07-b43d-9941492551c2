import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const type = searchParams.get('type')
    const city = searchParams.get('city')

    const where: any = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (type) {
      where.type = type
    }

    if (city) {
      where.city = city
    }

    const customers = await prisma.customer.findMany({
      where,
      include: {
        orders: {
          select: {
            id: true,
            totalAmount: true,
            status: true,
            createdAt: true
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 5
        },
        _count: {
          select: {
            orders: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(customers)
  } catch (error) {
    console.error('Erreur lors de la récupération des clients:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la récupération des clients' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('Données client reçues:', body)

    const {
      name,
      email,
      phone,
      address,
      city,
      type,
      companyName,
      taxNumber,
      creditLimit,
      paymentTerms
    } = body

    // Validation des champs obligatoires
    if (!name || !phone || !type) {
      console.log('Validation échouée - champs manquants:', { name, phone, type })
      return NextResponse.json(
        { error: 'Les champs nom, téléphone et type sont obligatoires' },
        { status: 400 }
      )
    }

    // Validation du type de client
    const validTypes = ['LOGISTICS', 'COMMERCE']
    if (!validTypes.includes(type)) {
      console.log('Type invalide:', type)
      return NextResponse.json(
        { error: 'Type de client invalide. Valeurs acceptées: LOGISTICS, COMMERCE' },
        { status: 400 }
      )
    }

    console.log('Validation réussie, création du client...')

    // Vérifier l'unicité du téléphone
    const existingPhone = await prisma.customer.findFirst({
      where: { phone }
    })

    if (existingPhone) {
      return NextResponse.json(
        { error: 'Ce numéro de téléphone est déjà utilisé' },
        { status: 400 }
      )
    }

    const customer = await prisma.customer.create({
      data: {
        name,
        email: email || null,
        phone,
        address: address || '',
        city: city || 'Dakar',
        type,
        companyName: companyName || null,
        taxNumber: taxNumber || null,
        creditLimit: creditLimit ? parseFloat(creditLimit) : 0,
        paymentTerms: paymentTerms ? parseInt(paymentTerms) : 30
      }
    })

    return NextResponse.json(customer, { status: 201 })
  } catch (error) {
    console.error('Erreur lors de la création du client:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la création du client', details: error.message },
      { status: 500 }
    )
  }
}
