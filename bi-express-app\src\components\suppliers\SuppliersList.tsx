import { prisma } from '@/lib/prisma'
import { Badge } from '@/components/ui/Badge'
import { Star, Phone, Mail, MapPin, Package } from 'lucide-react'

async function getSuppliers() {
  return await prisma.supplier.findMany({
    where: { isActive: true },
    include: {
      products: {
        where: { isActive: true }
      },
      orders: {
        take: 5,
        orderBy: { createdAt: 'desc' }
      }
    },
    orderBy: { rating: 'desc' }
  })
}

const cityColors = {
  LAGOS: 'bg-blue-100 text-blue-800',
  ABUJA: 'bg-green-100 text-green-800',
  KANO: 'bg-purple-100 text-purple-800'
}

const cityLabels = {
  LAGOS: 'Lagos',
  ABUJA: 'Abuja',
  KANO: 'Kano'
}

function StarRating({ rating }: { rating: number }) {
  return (
    <div className="flex items-center space-x-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`h-4 w-4 ${
            star <= rating
              ? 'text-yellow-400 fill-current'
              : 'text-gray-300'
          }`}
        />
      ))}
      <span className="text-sm text-gray-600 ml-1">({rating.toFixed(1)})</span>
    </div>
  )
}

export async function SuppliersList() {
  const suppliers = await getSuppliers()

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
        {suppliers.map((supplier) => (
          <div key={supplier.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 mb-1">
                  {supplier.name}
                </h3>
                <Badge className={cityColors[supplier.city]}>
                  {cityLabels[supplier.city]}
                </Badge>
              </div>
              <div className="text-right">
                <StarRating rating={supplier.rating} />
              </div>
            </div>

            {/* Contact Info */}
            <div className="space-y-2 mb-4">
              <div className="flex items-center text-sm text-gray-600">
                <Phone className="h-4 w-4 mr-2" />
                {supplier.phone}
              </div>
              {supplier.email && (
                <div className="flex items-center text-sm text-gray-600">
                  <Mail className="h-4 w-4 mr-2" />
                  {supplier.email}
                </div>
              )}
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="h-4 w-4 mr-2" />
                {supplier.address}
              </div>
            </div>

            {/* Specialties */}
            <div className="mb-4">
              <p className="text-sm font-medium text-gray-700 mb-2">Spécialités:</p>
              <p className="text-sm text-gray-600">{supplier.specialties}</p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <Package className="h-4 w-4 text-gray-400 mr-1" />
                </div>
                <p className="text-lg font-semibold text-gray-900">
                  {supplier.products.length}
                </p>
                <p className="text-xs text-gray-500">Produits</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <Package className="h-4 w-4 text-gray-400 mr-1" />
                </div>
                <p className="text-lg font-semibold text-gray-900">
                  {supplier.orders.length}
                </p>
                <p className="text-xs text-gray-500">Commandes</p>
              </div>
            </div>

            {/* Actions */}
            <div className="mt-4 flex space-x-2">
              <button className="flex-1 bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">
                Voir détails
              </button>
              <button className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                Modifier
              </button>
            </div>
          </div>
        ))}
      </div>

      {suppliers.length === 0 && (
        <div className="text-center py-12">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 text-lg">Aucun fournisseur trouvé</p>
          <p className="text-gray-400 text-sm mt-1">
            Commencez par ajouter votre premier fournisseur
          </p>
        </div>
      )}
    </div>
  )
}
