{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/settings/page.tsx"], "sourcesContent": ["export default function SettingsPage() {\n  return (\n    <div className=\"p-6\">\n      <div className=\"border-b border-gray-200 pb-4 mb-6\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">Paramètres</h1>\n        <p className=\"text-gray-600 mt-1\">\n          Configuration de votre application Bi-Express\n        </p>\n      </div>\n      \n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Paramètres généraux */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n            Paramètres généraux\n          </h3>\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Nom de l'entreprise\n              </label>\n              <input\n                type=\"text\"\n                defaultValue=\"Bi-Express\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Devise principale\n              </label>\n              <select className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n                <option value=\"CFA\">Franc CFA (XOF)</option>\n                <option value=\"NGN\">Naira Nigérian (NGN)</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Paramètres de tarification */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n            Paramètres de tarification\n          </h3>\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Taux logistique par défaut (%)\n              </label>\n              <input\n                type=\"number\"\n                defaultValue=\"30\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Marge par défaut (%)\n              </label>\n              <input\n                type=\"number\"\n                defaultValue=\"20\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Supplément transport aérien (%)\n              </label>\n              <input\n                type=\"number\"\n                defaultValue=\"30\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Taux de change */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n            Taux de change\n          </h3>\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between p-3 bg-blue-50 rounded-lg\">\n              <div>\n                <p className=\"font-medium text-gray-900\">1 NGN = 1.50 XOF</p>\n                <p className=\"text-sm text-gray-600\">Dernière mise à jour : Aujourd'hui</p>\n              </div>\n              <button className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\">\n                Actualiser\n              </button>\n            </div>\n            <div>\n              <label className=\"flex items-center\">\n                <input type=\"checkbox\" className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\" />\n                <span className=\"ml-2 text-sm text-gray-700\">\n                  Mise à jour automatique des taux de change\n                </span>\n              </label>\n            </div>\n          </div>\n        </div>\n\n        {/* Notifications */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n            Notifications\n          </h3>\n          <div className=\"space-y-3\">\n            <label className=\"flex items-center\">\n              <input type=\"checkbox\" defaultChecked className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\" />\n              <span className=\"ml-2 text-sm text-gray-700\">\n                Nouvelles commandes\n              </span>\n            </label>\n            <label className=\"flex items-center\">\n              <input type=\"checkbox\" defaultChecked className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\" />\n              <span className=\"ml-2 text-sm text-gray-700\">\n                Stock faible\n              </span>\n            </label>\n            <label className=\"flex items-center\">\n              <input type=\"checkbox\" className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\" />\n              <span className=\"ml-2 text-sm text-gray-700\">\n                Retards de livraison\n              </span>\n            </label>\n            <label className=\"flex items-center\">\n              <input type=\"checkbox\" className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\" />\n              <span className=\"ml-2 text-sm text-gray-700\">\n                Rapports hebdomadaires\n              </span>\n            </label>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"mt-6 flex justify-end\">\n        <button className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\">\n          Enregistrer les paramètres\n        </button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAKpC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,cAAa;gDACb,WAAU;;;;;;;;;;;;kDAGd,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDAAO,WAAU;;kEAChB,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,cAAa;gDACb,WAAU;;;;;;;;;;;;kDAGd,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,cAAa;gDACb,WAAU;;;;;;;;;;;;kDAGd,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,cAAa;gDACb,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAOlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAA4B;;;;;;kEACzC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC;gDAAO,WAAU;0DAAwD;;;;;;;;;;;;kDAI5E,8OAAC;kDACC,cAAA,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDAAM,MAAK;oDAAW,WAAU;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,cAAc;gDAAC,WAAU;;;;;;0DAChD,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;kDAI/C,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,cAAc;gDAAC,WAAU;;;;;;0DAChD,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;kDAI/C,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;;;;;;0DACjC,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;kDAI/C,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;;;;;;0DACjC,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAO,WAAU;8BAAkF;;;;;;;;;;;;;;;;;AAM5G", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 580, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,GAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}