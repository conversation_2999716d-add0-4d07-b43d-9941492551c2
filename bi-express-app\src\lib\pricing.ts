
// Utilitaires pour les calculs de prix et marges
import { CustomerType, TransportMode, UrgencyLevel } from '@prisma/client'

export interface PricingCalculation {
  supplierPrice: number;
  logisticCosts: number;
  costPrice: number; // Prix de revient
  sellingPrice: number;
  profit: number;
  marginPercentage: number;
}

// Types pour les calculs de tarification avancée
export interface PricingRequest {
  customerType: CustomerType
  transportMode: TransportMode
  originCity: string
  destinationCity: string
  weight: number
  volume?: number
  declaredValue?: number
  urgency?: UrgencyLevel
  insuranceRequired?: boolean
}

export interface PricingResult {
  baseTransportCost: number
  handlingFees: number
  fuelSurcharge: number
  insuranceCost: number
  urgencySurcharge: number
  bulkDiscount: number
  totalCost: number
  estimatedDays: number
  breakdown: PricingBreakdown[]
}

export interface PricingBreakdown {
  item: string
  amount: number
  description: string
}

/**
 * Calcule tous les prix et marges pour un produit
 */
export function calculatePricing(
  supplierPrice: number,
  logisticRate: number = 0.30,
  marginRate: number = 0.20
): PricingCalculation {
  const logisticCosts = supplierPrice * logisticRate;
  const costPrice = supplierPrice + logisticCosts;
  const sellingPrice = costPrice * (1 + marginRate);
  const profit = sellingPrice - costPrice;
  const marginPercentage = (profit / sellingPrice) * 100;

  return {
    supplierPrice,
    logisticCosts,
    costPrice,
    sellingPrice,
    profit,
    marginPercentage
  };
}

/**
 * Calcule le prix de vente à partir d'une marge souhaitée
 */
export function calculateSellingPriceFromMargin(
  supplierPrice: number,
  logisticRate: number,
  desiredMarginPercentage: number
): number {
  const logisticCosts = supplierPrice * logisticRate;
  const costPrice = supplierPrice + logisticCosts;
  return costPrice / (1 - desiredMarginPercentage / 100);
}

/**
 * Calcule les frais de transport selon le mode
 */
export function calculateTransportCosts(
  weight: number,
  mode: 'ROAD' | 'AIR_EXPRESS'
): number {
  const rates = {
    ROAD: 500, // 500 XOF par kg
    AIR_EXPRESS: 2000 // 2000 XOF par kg
  };
  
  return weight * rates[mode];
}

/**
 * Formate un montant en devise
 */
export function formatCurrency(
  amount: number,
  currency: 'NGN' | 'XOF' = 'XOF'
): string {
  const symbols = {
    NGN: '₦',
    XOF: 'CFA'
  };
  
  return `${amount.toLocaleString('fr-FR')} ${symbols[currency]}`;
}

/**
 * Convertit NGN vers XOF
 */
export function convertNGNtoXOF(
  amountNGN: number,
  exchangeRate: number = 0.85
): number {
  return amountNGN * exchangeRate;
}

// Configuration des tarifs de base pour transport différencié
const BASE_PRICING = {
  // Transport routier (par kg)
  ROAD: {
    LAGOS_DAKAR: { pricePerKg: 850, minimumCharge: 25000, estimatedDays: 6 },
    ABUJA_DAKAR: { pricePerKg: 900, minimumCharge: 27000, estimatedDays: 7 },
    KANO_DAKAR: { pricePerKg: 750, minimumCharge: 22000, estimatedDays: 5 }
  },
  // Transport aérien express (par kg)
  AIR_EXPRESS: {
    LAGOS_DAKAR: { pricePerKg: 2500, minimumCharge: 45000, estimatedDays: 2 },
    ABUJA_DAKAR: { pricePerKg: 2800, minimumCharge: 50000, estimatedDays: 2 },
    KANO_DAKAR: { pricePerKg: 3200, minimumCharge: 55000, estimatedDays: 2 }
  }
}

// Frais additionnels
const ADDITIONAL_FEES = {
  handlingFeeRate: 0.05, // 5% du coût de transport
  fuelSurchargeRate: 0.12, // 12% du coût de transport
  insuranceRate: 0.005, // 0.5% de la valeur déclarée
  urgencyMultiplier: {
    STANDARD: 1.0,
    EXPRESS: 1.3,
    URGENT: 1.8
  }
}

// Remises volume (seuils en kg)
const BULK_DISCOUNTS = [
  { minWeight: 1000, discount: 0.05 }, // 5% à partir de 1 tonne
  { minWeight: 2000, discount: 0.08 }, // 8% à partir de 2 tonnes
  { minWeight: 5000, discount: 0.12 }, // 12% à partir de 5 tonnes
  { minWeight: 10000, discount: 0.15 } // 15% à partir de 10 tonnes
]

// Marges par type de client
const CLIENT_MARGINS = {
  LOGISTICS: 0.20, // 20% de marge pour clients logistique
  COMMERCE: 0.35   // 35% de marge intégrée pour clients commerce
}

/**
 * Calcule le tarif pour un client logistique (transport seul)
 */
export function calculateLogisticsPricing(request: PricingRequest): PricingResult {
  const routeKey = `${request.originCity.toUpperCase()}_${request.destinationCity.toUpperCase()}`
  const modeConfig = BASE_PRICING[request.transportMode]
  const routeConfig = modeConfig[routeKey as keyof typeof modeConfig]

  // Coût de transport de base
  const baseTransportCost = Math.max(
    request.weight * routeConfig.pricePerKg,
    routeConfig.minimumCharge
  )
  
  // Application de la marge client logistique
  const totalCost = subtotal * (1 + CLIENT_MARGINS.LOGISTICS)

