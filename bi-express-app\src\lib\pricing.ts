// Utilitaires pour les calculs de prix et marges

export interface PricingCalculation {
  supplierPrice: number;
  logisticCosts: number;
  costPrice: number; // Prix de revient
  sellingPrice: number;
  profit: number;
  marginPercentage: number;
}

/**
 * Calcule tous les prix et marges pour un produit
 */
export function calculatePricing(
  supplierPrice: number,
  logisticRate: number = 0.30,
  marginRate: number = 0.20
): PricingCalculation {
  const logisticCosts = supplierPrice * logisticRate;
  const costPrice = supplierPrice + logisticCosts;
  const sellingPrice = costPrice * (1 + marginRate);
  const profit = sellingPrice - costPrice;
  const marginPercentage = (profit / sellingPrice) * 100;

  return {
    supplierPrice,
    logisticCosts,
    costPrice,
    sellingPrice,
    profit,
    marginPercentage
  };
}

/**
 * Calcule le prix de vente à partir d'une marge souhaitée
 */
export function calculateSellingPriceFromMargin(
  supplierPrice: number,
  logisticRate: number,
  desiredMarginPercentage: number
): number {
  const logisticCosts = supplierPrice * logisticRate;
  const costPrice = supplierPrice + logisticCosts;
  return costPrice / (1 - desiredMarginPercentage / 100);
}

/**
 * Calcule les frais de transport selon le mode
 */
export function calculateTransportCosts(
  weight: number,
  mode: 'ROAD' | 'AIR_EXPRESS'
): number {
  const rates = {
    ROAD: 500, // 500 XOF par kg
    AIR_EXPRESS: 2000 // 2000 XOF par kg
  };
  
  return weight * rates[mode];
}

/**
 * Formate un montant en devise
 */
export function formatCurrency(
  amount: number,
  currency: 'NGN' | 'XOF' = 'XOF'
): string {
  const symbols = {
    NGN: '₦',
    XOF: 'CFA'
  };
  
  return `${amount.toLocaleString('fr-FR')} ${symbols[currency]}`;
}

/**
 * Convertit NGN vers XOF
 */
export function convertNGNtoXOF(
  amountNGN: number,
  exchangeRate: number = 0.85
): number {
  return amountNGN * exchangeRate;
}
