// Schéma Prisma pour l'application Bi-Express
// Gestion de vente en gros Nigeria-Dakar

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// Modèle Fournisseur
model Supplier {
  id          String       @id @default(cuid())
  name        String
  phone       String
  email       String?
  address     String
  city        SupplierCity
  specialties String // Spécialités du fournisseur (séparées par des virgules)
  rating      Float        @default(0) // Note sur 5
  isActive    Boolean      @default(true)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // Relations
  products    Product[]
  orders      Order[]
  evaluations SupplierEvaluation[]

  @@map("suppliers")
}

// Évaluations des fournisseurs
model SupplierEvaluation {
  id         String   @id @default(cuid())
  supplierId String
  rating     Int // Note de 1 à 5
  comment    String?
  orderId    String? // Évaluation liée à une commande
  createdAt  DateTime @default(now())

  // Relations
  supplier Supplier @relation(fields: [supplierId], references: [id], onDelete: Cascade)
  order    Order?   @relation(fields: [orderId], references: [id])

  @@map("supplier_evaluations")
}

// Modèle Produit
model Product {
  id          String          @id @default(cuid())
  name        String
  description String?
  category    ProductCategory

  // Spécificités selon la catégorie
  // Pour les tissus
  fabricType String? // Type de tissu (wax, bazin, soie, coton)
  width      Float? // Largeur en mètres
  color      String?
  pattern    String? // Motif

  // Pour les cosmétiques
  brand  String? // Marque
  volume Float? // Contenance
  origin String? // Origine

  // Pour les mèches
  length   Float? // Longueur
  texture  String? // Texture
  hairType String? // Type de cheveux (naturel, synthétique)

  // Informations tarifaires
  supplierPrice Float // Prix fournisseur HT
  logisticRate  Float @default(0.30) // Taux de frais logistique (30% par défaut)
  margin        Float @default(0.20) // Marge souhaitée (20% par défaut)

  // Stock et disponibilité
  stockQuantity Int     @default(0)
  minStockAlert Int     @default(10)
  isActive      Boolean @default(true)

  // Métadonnées
  imageUrl   String?
  weight     Float? // Poids en kg
  dimensions String? // Dimensions (LxlxH)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  supplierId   String
  supplier     Supplier       @relation(fields: [supplierId], references: [id])
  orderItems   OrderItem[]
  priceHistory PriceHistory[]

  @@map("products")
}

// Historique des prix
model PriceHistory {
  id            String   @id @default(cuid())
  productId     String
  supplierPrice Float
  margin        Float
  createdAt     DateTime @default(now())

  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("price_history")
}

// Modèle Client
model Customer {
  id          String   @id @default(cuid())
  name        String
  phone       String
  email       String?
  address     String
  city        String   @default("Dakar")
  isActive    Boolean  @default(true)
  creditLimit Float    @default(0) // Limite de crédit
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  orders Order[]

  @@map("customers")
}

// Modèle Commande
model Order {
  id          String @id @default(cuid())
  orderNumber String @unique
  customerId  String
  supplierId  String

  // Statut et logistique
  status        OrderStatus   @default(PENDING)
  transportMode TransportMode

  // Calculs financiers
  subtotal      Float // Sous-total HT
  logisticCosts Float // Frais logistiques
  totalAmount   Float // Montant total TTC
  totalProfit   Float // Bénéfice total

  // Dates importantes
  orderDate        DateTime  @default(now())
  expectedDelivery DateTime?
  deliveredAt      DateTime?

  // Informations de livraison
  deliveryAddress String
  trackingNumber  String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  customer    Customer             @relation(fields: [customerId], references: [id])
  supplier    Supplier             @relation(fields: [supplierId], references: [id])
  orderItems  OrderItem[]
  evaluations SupplierEvaluation[]

  @@map("orders")
}

// Articles de commande
model OrderItem {
  id         String @id @default(cuid())
  orderId    String
  productId  String
  quantity   Int
  unitPrice  Float // Prix unitaire au moment de la commande
  totalPrice Float // Prix total pour cet article
  profit     Float // Bénéfice pour cet article

  // Relations
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// Taux de change
model ExchangeRate {
  id           String   @id @default(cuid())
  fromCurrency String // NGN
  toCurrency   String // XOF
  rate         Float
  createdAt    DateTime @default(now())

  @@map("exchange_rates")
}

// Enums
enum SupplierCity {
  LAGOS
  ABUJA
  KANO
}

enum ProductCategory {
  TISSUS
  COSMETIQUES
  MECHES
}

enum OrderStatus {
  PENDING // En attente
  CONFIRMED // Confirmée
  SHIPPED // Expédiée
  DELIVERED // Livrée
  CANCELLED // Annulée
}

enum TransportMode {
  ROAD // Transport routier (5-7 jours)
  AIR_EXPRESS // Fret aérien express (24-48h)
}
