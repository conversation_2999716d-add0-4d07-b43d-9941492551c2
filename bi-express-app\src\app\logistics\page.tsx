import { Suspense } from 'react'
import { LogisticsStats } from '@/components/logistics/LogisticsStats'
import { ShipmentsList } from '@/components/logistics/ShipmentsList'
import { CarriersList } from '@/components/logistics/CarriersList'
import { LogisticsFilters } from '@/components/logistics/LogisticsFilters'
import { CreateShipmentButton } from '@/components/logistics/CreateShipmentButton'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

export default function LogisticsPage() {
  return (
    <div className="p-6">
      <div className="border-b border-gray-200 pb-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Logistique</h1>
            <p className="text-gray-600 mt-1">
              Gestion des expéditions Nigeria → Dakar
            </p>
          </div>
          <CreateShipmentButton />
        </div>
      </div>

      <Suspense fallback={<div>Chargement des statistiques...</div>}>
        <LogisticsStats />
      </Suspense>

      <div className="mt-6">
        <Tabs defaultValue="shipments" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="shipments">Expéditions</TabsTrigger>
            <TabsTrigger value="carriers">Transporteurs</TabsTrigger>
          </TabsList>

          <TabsContent value="shipments" className="mt-6">
            <div className="space-y-6">
              <LogisticsFilters />
              <Suspense fallback={<div>Chargement des expéditions...</div>}>
                <ShipmentsList />
              </Suspense>
            </div>
          </TabsContent>

          <TabsContent value="carriers" className="mt-6">
            <Suspense fallback={<div>Chargement des transporteurs...</div>}>
              <CarriersList />
            </Suspense>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
