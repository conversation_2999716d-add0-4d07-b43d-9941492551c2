import { prisma } from '@/lib/prisma'
import { formatCurrency } from '@/lib/utils'
import { 
  ShoppingCart, 
  Clock, 
  Truck, 
  CheckCircle,
  TrendingUp,
  DollarSign
} from 'lucide-react'

async function getOrderStats() {
  const [
    totalOrders,
    pendingOrders,
    shippedOrders,
    deliveredOrders,
    orders
  ] = await Promise.all([
    prisma.order.count(),
    prisma.order.count({ where: { status: 'PENDING' } }),
    prisma.order.count({ where: { status: 'SHIPPED' } }),
    prisma.order.count({ where: { status: 'DELIVERED' } }),
    prisma.order.findMany({
      select: {
        totalAmount: true,
        totalProfit: true,
        transportMode: true,
        status: true
      }
    })
  ])

  // Calculs des statistiques financières
  const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0)
  const totalProfit = orders.reduce((sum, order) => sum + order.totalProfit, 0)
  
  // Statistiques par mode de transport
  const roadOrders = orders.filter(order => order.transportMode === 'ROAD').length
  const airOrders = orders.filter(order => order.transportMode === 'AIR').length

  return {
    totalOrders,
    pendingOrders,
    shippedOrders,
    deliveredOrders,
    totalRevenue,
    totalProfit,
    roadOrders,
    airOrders
  }
}

export async function OrderStats() {
  const stats = await getOrderStats()

  const statCards = [
    {
      title: 'Total Commandes',
      value: stats.totalOrders.toString(),
      icon: ShoppingCart,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      description: 'Toutes commandes'
    },
    {
      title: 'En attente',
      value: stats.pendingOrders.toString(),
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      description: 'À traiter'
    },
    {
      title: 'En transit',
      value: stats.shippedOrders.toString(),
      icon: Truck,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      description: 'En livraison'
    },
    {
      title: 'Livrées',
      value: stats.deliveredOrders.toString(),
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      description: 'Terminées'
    },
    {
      title: 'Chiffre d\'affaires',
      value: formatCurrency(stats.totalRevenue),
      icon: DollarSign,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50',
      description: 'Total des ventes'
    },
    {
      title: 'Bénéfices',
      value: formatCurrency(stats.totalProfit),
      icon: TrendingUp,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      description: 'Profit total'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Main Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {statCards.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">
                  {stat.title}
                </p>
                <p className="text-lg font-bold text-gray-900 mt-1">{stat.value}</p>
                <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
              </div>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Transport Mode Stats */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Répartition par mode de transport
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center">
              <Truck className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <p className="font-medium text-gray-900">Transport routier</p>
                <p className="text-sm text-gray-600">5-7 jours • Économique</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-blue-600">{stats.roadOrders}</p>
              <p className="text-sm text-gray-600">commandes</p>
            </div>
          </div>
          
          <div className="flex items-center justify-between p-4 bg-purple-50 rounded-lg">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-purple-600 rounded-lg flex items-center justify-center mr-3">
                <span className="text-white font-bold text-sm">✈</span>
              </div>
              <div>
                <p className="font-medium text-gray-900">Transport aérien</p>
                <p className="text-sm text-gray-600">24-48h • Express</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-purple-600">{stats.airOrders}</p>
              <p className="text-sm text-gray-600">commandes</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
