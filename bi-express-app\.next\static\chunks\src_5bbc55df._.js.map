{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/auth.ts"], "sourcesContent": ["// Système d'authentification simple pour l'application\nexport interface User {\n  id: string\n  email: string\n  name: string\n  role: 'admin' | 'manager' | 'user'\n  createdAt: Date\n}\n\nexport interface AuthSession {\n  user: User\n  token: string\n  expiresAt: Date\n}\n\n// Utilisateurs par défaut pour la démo\nconst DEFAULT_USERS: User[] = [\n  {\n    id: '1',\n    email: '<EMAIL>',\n    name: 'Administrateur',\n    role: 'admin',\n    createdAt: new Date('2024-01-01')\n  },\n  {\n    id: '2',\n    email: '<EMAIL>',\n    name: 'Gestionnaire',\n    role: 'manager',\n    createdAt: new Date('2024-01-01')\n  },\n  {\n    id: '3',\n    email: '<EMAIL>',\n    name: 'Utilisateur',\n    role: 'user',\n    createdAt: new Date('2024-01-01')\n  }\n]\n\n// Mots de passe par défaut (en production, utiliser un système de hachage)\nconst DEFAULT_PASSWORDS: Record<string, string> = {\n  '<EMAIL>': 'admin123',\n  '<EMAIL>': 'manager123',\n  '<EMAIL>': 'user123'\n}\n\n// Stockage en mémoire des sessions (en production, utiliser Redis ou une base de données)\nlet activeSessions: Map<string, AuthSession> = new Map()\n\n/**\n * Génère un token de session aléatoire\n */\nfunction generateSessionToken(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n\n/**\n * Authentifie un utilisateur avec email et mot de passe\n */\nexport async function authenticateUser(\n  email: string, \n  password: string\n): Promise<{ success: boolean; session?: AuthSession; error?: string }> {\n  // Simulation d'un délai d'authentification\n  await new Promise(resolve => setTimeout(resolve, 500))\n\n  // Vérifier si l'utilisateur existe\n  const user = DEFAULT_USERS.find(u => u.email === email)\n  if (!user) {\n    return { success: false, error: 'Utilisateur non trouvé' }\n  }\n\n  // Vérifier le mot de passe\n  if (DEFAULT_PASSWORDS[email] !== password) {\n    return { success: false, error: 'Mot de passe incorrect' }\n  }\n\n  // Créer une session\n  const token = generateSessionToken()\n  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 heures\n\n  const session: AuthSession = {\n    user,\n    token,\n    expiresAt\n  }\n\n  // Stocker la session\n  activeSessions.set(token, session)\n\n  return { success: true, session }\n}\n\n/**\n * Valide un token de session\n */\nexport async function validateSession(token: string): Promise<AuthSession | null> {\n  const session = activeSessions.get(token)\n  \n  if (!session) {\n    return null\n  }\n\n  // Vérifier si la session n'a pas expiré\n  if (session.expiresAt < new Date()) {\n    activeSessions.delete(token)\n    return null\n  }\n\n  return session\n}\n\n/**\n * Déconnecte un utilisateur\n */\nexport async function logoutUser(token: string): Promise<void> {\n  activeSessions.delete(token)\n}\n\n/**\n * Obtient tous les utilisateurs (pour l'administration)\n */\nexport async function getAllUsers(): Promise<User[]> {\n  return DEFAULT_USERS\n}\n\n/**\n * Vérifie si un utilisateur a les permissions pour une action\n */\nexport function hasPermission(user: User, action: string): boolean {\n  const permissions = {\n    admin: ['read', 'write', 'delete', 'manage_users', 'view_reports', 'manage_settings'],\n    manager: ['read', 'write', 'view_reports'],\n    user: ['read']\n  }\n\n  return permissions[user.role]?.includes(action) || false\n}\n\n/**\n * Obtient les informations de l'utilisateur connecté depuis les cookies\n */\nexport async function getCurrentUser(): Promise<User | null> {\n  // En production, récupérer le token depuis les cookies HTTP-only\n  if (typeof window === 'undefined') {\n    return null // Côté serveur\n  }\n\n  const token = localStorage.getItem('auth_token')\n  if (!token) {\n    return null\n  }\n\n  const session = await validateSession(token)\n  return session?.user || null\n}\n\n/**\n * Sauvegarde le token d'authentification\n */\nexport function saveAuthToken(token: string): void {\n  if (typeof window !== 'undefined') {\n    localStorage.setItem('auth_token', token)\n  }\n}\n\n/**\n * Supprime le token d'authentification\n */\nexport function removeAuthToken(): void {\n  if (typeof window !== 'undefined') {\n    localStorage.removeItem('auth_token')\n  }\n}\n\n/**\n * Vérifie si l'utilisateur est connecté\n */\nexport async function isAuthenticated(): Promise<boolean> {\n  const user = await getCurrentUser()\n  return user !== null\n}\n\n/**\n * Middleware d'authentification pour les pages protégées\n */\nexport async function requireAuth(): Promise<{ authenticated: boolean; user?: User; redirectTo?: string }> {\n  const user = await getCurrentUser()\n  \n  if (!user) {\n    return {\n      authenticated: false,\n      redirectTo: '/login'\n    }\n  }\n\n  return {\n    authenticated: true,\n    user\n  }\n}\n\n/**\n * Obtient les statistiques des utilisateurs connectés\n */\nexport function getActiveSessionsCount(): number {\n  // Nettoyer les sessions expirées\n  const now = new Date()\n  for (const [token, session] of activeSessions.entries()) {\n    if (session.expiresAt < now) {\n      activeSessions.delete(token)\n    }\n  }\n  \n  return activeSessions.size\n}\n\n/**\n * Obtient les rôles disponibles\n */\nexport function getAvailableRoles(): Array<{ value: string; label: string }> {\n  return [\n    { value: 'admin', label: 'Administrateur' },\n    { value: 'manager', label: 'Gestionnaire' },\n    { value: 'user', label: 'Utilisateur' }\n  ]\n}\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;;;;;;;;;;;;AAevD,uCAAuC;AACvC,MAAM,gBAAwB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,WAAW,IAAI,KAAK;IACtB;CACD;AAED,2EAA2E;AAC3E,MAAM,oBAA4C;IAChD,wBAAwB;IACxB,0BAA0B;IAC1B,uBAAuB;AACzB;AAEA,0FAA0F;AAC1F,IAAI,iBAA2C,IAAI;AAEnD;;CAEC,GACD,SAAS;IACP,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE;AAKO,eAAe,iBACpB,KAAa,EACb,QAAgB;IAEhB,2CAA2C;IAC3C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IAEjD,mCAAmC;IACnC,MAAM,OAAO,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;IACjD,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,SAAS;YAAO,OAAO;QAAyB;IAC3D;IAEA,2BAA2B;IAC3B,IAAI,iBAAiB,CAAC,MAAM,KAAK,UAAU;QACzC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAyB;IAC3D;IAEA,oBAAoB;IACpB,MAAM,QAAQ;IACd,MAAM,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,YAAY;;IAEzE,MAAM,UAAuB;QAC3B;QACA;QACA;IACF;IAEA,qBAAqB;IACrB,eAAe,GAAG,CAAC,OAAO;IAE1B,OAAO;QAAE,SAAS;QAAM;IAAQ;AAClC;AAKO,eAAe,gBAAgB,KAAa;IACjD,MAAM,UAAU,eAAe,GAAG,CAAC;IAEnC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,wCAAwC;IACxC,IAAI,QAAQ,SAAS,GAAG,IAAI,QAAQ;QAClC,eAAe,MAAM,CAAC;QACtB,OAAO;IACT;IAEA,OAAO;AACT;AAKO,eAAe,WAAW,KAAa;IAC5C,eAAe,MAAM,CAAC;AACxB;AAKO,eAAe;IACpB,OAAO;AACT;AAKO,SAAS,cAAc,IAAU,EAAE,MAAc;IACtD,MAAM,cAAc;QAClB,OAAO;YAAC;YAAQ;YAAS;YAAU;YAAgB;YAAgB;SAAkB;QACrF,SAAS;YAAC;YAAQ;YAAS;SAAe;QAC1C,MAAM;YAAC;SAAO;IAChB;IAEA,OAAO,WAAW,CAAC,KAAK,IAAI,CAAC,EAAE,SAAS,WAAW;AACrD;AAKO,eAAe;IACpB,iEAAiE;IACjE,uCAAmC;;IAEnC;IAEA,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,MAAM,UAAU,MAAM,gBAAgB;IACtC,OAAO,SAAS,QAAQ;AAC1B;AAKO,SAAS,cAAc,KAAa;IACzC,wCAAmC;QACjC,aAAa,OAAO,CAAC,cAAc;IACrC;AACF;AAKO,SAAS;IACd,wCAAmC;QACjC,aAAa,UAAU,CAAC;IAC1B;AACF;AAKO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,OAAO,SAAS;AAClB;AAKO,eAAe;IACpB,MAAM,OAAO,MAAM;IAEnB,IAAI,CAAC,MAAM;QACT,OAAO;YACL,eAAe;YACf,YAAY;QACd;IACF;IAEA,OAAO;QACL,eAAe;QACf;IACF;AACF;AAKO,SAAS;IACd,iCAAiC;IACjC,MAAM,MAAM,IAAI;IAChB,KAAK,MAAM,CAAC,OAAO,QAAQ,IAAI,eAAe,OAAO,GAAI;QACvD,IAAI,QAAQ,SAAS,GAAG,KAAK;YAC3B,eAAe,MAAM,CAAC;QACxB;IACF;IAEA,OAAO,eAAe,IAAI;AAC5B;AAKO,SAAS;IACd,OAAO;QACL;YAAE,OAAO;YAAS,OAAO;QAAiB;QAC1C;YAAE,OAAO;YAAW,OAAO;QAAe;QAC1C;YAAE,OAAO;YAAQ,OAAO;QAAc;KACvC;AACH", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: 'NGN' | 'XOF' = 'XOF'): string {\n  const symbols = {\n    NGN: '₦',\n    XOF: 'CFA'\n  }\n  \n  return `${amount.toLocaleString('fr-FR')} ${symbols[currency]}`\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric'\n  }).format(date)\n}\n\nexport function formatDateTime(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  }).format(date)\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAA0B,KAAK;IAC5E,MAAM,UAAU;QACd,KAAK;QACL,KAAK;IACP;IAEA,OAAO,GAAG,OAAO,cAAc,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,SAAS,EAAE;AACjE;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;IACR,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAU;IACvC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useEffect, useState } from 'react'\nimport { cn } from '@/lib/utils'\nimport { getCurrentUser } from '@/lib/auth'\nimport type { User } from '@/lib/auth'\nimport {\n  LayoutDashboard,\n  Users,\n  Package,\n  ShoppingCart,\n  Truck,\n  BarChart3,\n  Settings,\n  Building2,\n  DollarSign\n} from 'lucide-react'\n\nconst navigation = [\n  {\n    name: 'Dashboard',\n    href: '/',\n    icon: LayoutDashboard,\n  },\n  {\n    name: 'Fournisseurs',\n    href: '/suppliers',\n    icon: Building2,\n  },\n  {\n    name: 'Produits',\n    href: '/products',\n    icon: Package,\n  },\n  {\n    name: 'Commandes',\n    href: '/orders',\n    icon: ShoppingCart,\n  },\n  {\n    name: 'Clients',\n    href: '/customers',\n    icon: Users,\n  },\n  {\n    name: 'Logistique',\n    href: '/logistics',\n    icon: Truck,\n  },\n  {\n    name: 'Devi<PERSON>',\n    href: '/currency',\n    icon: DollarSign,\n  },\n  {\n    name: 'Rapports',\n    href: '/reports',\n    icon: BarChart3,\n  },\n  {\n    name: 'Utilisateurs',\n    href: '/users',\n    icon: Users,\n    adminOnly: true,\n  },\n  {\n    name: 'Paramètres',\n    href: '/settings',\n    icon: Settings,\n  },\n]\n\nexport function Sidebar() {\n  const pathname = usePathname()\n  const [user, setUser] = useState<User | null>(null)\n\n  useEffect(() => {\n    const loadUser = async () => {\n      try {\n        const currentUser = await getCurrentUser()\n        setUser(currentUser)\n      } catch (error) {\n        console.error('Erreur chargement utilisateur:', error)\n      }\n    }\n    loadUser()\n  }, [])\n\n  // Filtrer la navigation selon les permissions\n  const filteredNavigation = navigation.filter(item => {\n    if (item.adminOnly) {\n      return user?.role === 'admin'\n    }\n    return true\n  })\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-white border-r border-gray-200\">\n      {/* Logo */}\n      <div className=\"flex h-16 items-center px-6 border-b border-gray-200\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n            <span className=\"text-white font-bold text-sm\">BE</span>\n          </div>\n          <div>\n            <h1 className=\"text-lg font-semibold text-gray-900\">Bi-Express</h1>\n            <p className=\"text-xs text-gray-500\">Nigeria → Dakar</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-6 space-y-1\">\n        {filteredNavigation.map((item) => {\n          const isActive = pathname === item.href\n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',\n                isActive\n                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n              )}\n            >\n              <item.icon\n                className={cn(\n                  'mr-3 h-5 w-5',\n                  isActive ? 'text-blue-700' : 'text-gray-400'\n                )}\n              />\n              {item.name}\n            </Link>\n          )\n        })}\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-gray-200\">\n        <div className=\"text-xs text-gray-500 text-center\">\n          <p>Version 1.0.0</p>\n          <p className=\"mt-1\">© 2024 Bi-Express</p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;AAoBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,+NAAA,CAAA,kBAAe;IACvB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,mNAAA,CAAA,YAAS;IACjB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2MAAA,CAAA,UAAO;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,yNAAA,CAAA,eAAY;IACpB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;IACb;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;IACb;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qNAAA,CAAA,aAAU;IAClB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qNAAA,CAAA,YAAS;IACjB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,WAAW;IACb;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;IAChB;CACD;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;8CAAW;oBACf,IAAI;wBACF,MAAM,cAAc,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;wBACvC,QAAQ;oBACV,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;oBAClD;gBACF;;YACA;QACF;4BAAG,EAAE;IAEL,8CAA8C;IAC9C,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA;QAC3C,IAAI,KAAK,SAAS,EAAE;YAClB,OAAO,MAAM,SAAS;QACxB;QACA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAA+B;;;;;;;;;;;sCAEjD,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,6LAAC;gBAAI,WAAU;0BACZ,mBAAmB,GAAG,CAAC,CAAC;oBACvB,MAAM,WAAW,aAAa,KAAK,IAAI;oBACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,wDACA;;0CAGN,6LAAC,KAAK,IAAI;gCACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,WAAW,kBAAkB;;;;;;4BAGhC,KAAK,IAAI;;uBAfL,KAAK,IAAI;;;;;gBAkBpB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;sCACH,6LAAC;4BAAE,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;;AAK9B;GA3EgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/auth/UserMenu.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { getCurrentUser, logoutUser, removeAuthToken } from '@/lib/auth'\nimport { User, LogOut, Settings, ChevronDown } from 'lucide-react'\nimport type { User as UserType } from '@/lib/auth'\n\nexport function UserMenu() {\n  const [user, setUser] = useState<UserType | null>(null)\n  const [isOpen, setIsOpen] = useState(false)\n  const [isLoading, setIsLoading] = useState(true)\n  const router = useRouter()\n\n  useEffect(() => {\n    const loadUser = async () => {\n      try {\n        const currentUser = await getCurrentUser()\n        setUser(currentUser)\n      } catch (error) {\n        console.error('Erreur chargement utilisateur:', error)\n      } finally {\n        setIsLoading(false)\n      }\n    }\n\n    loadUser()\n  }, [])\n\n  const handleLogout = async () => {\n    try {\n      const token = localStorage.getItem('auth_token')\n      if (token) {\n        await logoutUser(token)\n      }\n      removeAuthToken()\n      router.push('/login')\n      router.refresh()\n    } catch (error) {\n      console.error('Erreur déconnexion:', error)\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center space-x-2\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 w-8 bg-gray-200 rounded-full\"></div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return (\n      <div className=\"flex items-center space-x-2\">\n        <button\n          onClick={() => router.push('/login')}\n          className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n        >\n          Se connecter\n        </button>\n      </div>\n    )\n  }\n\n  const getRoleLabel = (role: string) => {\n    const labels = {\n      admin: 'Administrateur',\n      manager: 'Gestionnaire',\n      user: 'Utilisateur'\n    }\n    return labels[role as keyof typeof labels] || role\n  }\n\n  const getRoleBadgeColor = (role: string) => {\n    const colors = {\n      admin: 'bg-red-100 text-red-800',\n      manager: 'bg-blue-100 text-blue-800',\n      user: 'bg-green-100 text-green-800'\n    }\n    return colors[role as keyof typeof colors] || 'bg-gray-100 text-gray-800'\n  }\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-3 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 p-2 hover:bg-gray-50\"\n      >\n        <div className=\"h-8 w-8 bg-blue-600 rounded-full flex items-center justify-center\">\n          <User className=\"h-4 w-4 text-white\" />\n        </div>\n        <div className=\"hidden md:block text-left\">\n          <p className=\"text-sm font-medium text-gray-900\">{user.name}</p>\n          <p className=\"text-xs text-gray-500\">{getRoleLabel(user.role)}</p>\n        </div>\n        <ChevronDown className=\"h-4 w-4 text-gray-400\" />\n      </button>\n\n      {isOpen && (\n        <>\n          {/* Overlay pour fermer le menu */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          \n          {/* Menu déroulant */}\n          <div className=\"absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-20\">\n            <div className=\"py-1\">\n              {/* Informations utilisateur */}\n              <div className=\"px-4 py-3 border-b border-gray-100\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"h-10 w-10 bg-blue-600 rounded-full flex items-center justify-center\">\n                    <User className=\"h-5 w-5 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium text-gray-900\">{user.name}</p>\n                    <p className=\"text-xs text-gray-500\">{user.email}</p>\n                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium mt-1 ${getRoleBadgeColor(user.role)}`}>\n                      {getRoleLabel(user.role)}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Actions */}\n              <div className=\"py-1\">\n                <button\n                  onClick={() => {\n                    setIsOpen(false)\n                    router.push('/settings')\n                  }}\n                  className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                >\n                  <Settings className=\"h-4 w-4 mr-3\" />\n                  Paramètres\n                </button>\n                \n                <button\n                  onClick={handleLogout}\n                  className=\"flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50\"\n                >\n                  <LogOut className=\"h-4 w-4 mr-3\" />\n                  Se déconnecter\n                </button>\n              </div>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAQO,SAAS;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM;+CAAW;oBACf,IAAI;wBACF,MAAM,cAAc,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;wBACvC,QAAQ;oBACV,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;oBAClD,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;6BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,MAAM,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD,EAAE;YACnB;YACA,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD;YACd,OAAO,IAAI,CAAC;YACZ,OAAO,OAAO;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBACC,SAAS,IAAM,OAAO,IAAI,CAAC;gBAC3B,WAAU;0BACX;;;;;;;;;;;IAKP;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAS;YACb,OAAO;YACP,SAAS;YACT,MAAM;QACR;QACA,OAAO,MAAM,CAAC,KAA4B,IAAI;IAChD;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,SAAS;YACb,OAAO;YACP,SAAS;YACT,MAAM;QACR;QACA,OAAO,MAAM,CAAC,KAA4B,IAAI;IAChD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAqC,KAAK,IAAI;;;;;;0CAC3D,6LAAC;gCAAE,WAAU;0CAAyB,aAAa,KAAK,IAAI;;;;;;;;;;;;kCAE9D,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;YAGxB,wBACC;;kCAEE,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAI3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqC,KAAK,IAAI;;;;;;kEAC3D,6LAAC;wDAAE,WAAU;kEAAyB,KAAK,KAAK;;;;;;kEAChD,6LAAC;wDAAK,WAAW,CAAC,2EAA2E,EAAE,kBAAkB,KAAK,IAAI,GAAG;kEAC1H,aAAa,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;8CAO/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;gDACP,UAAU;gDACV,OAAO,IAAI,CAAC;4CACd;4CACA,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAIvC,6LAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;GAlJgB;;QAIC,qIAAA,CAAA,YAAS;;;KAJV", "debugId": null}}, {"offset": {"line": 831, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { Bell, Search, Menu } from 'lucide-react'\nimport { UserMenu } from '@/components/auth/UserMenu'\nimport { useState } from 'react'\n\ninterface HeaderProps {\n  onMenuClick?: () => void\n}\n\nexport function Header({ onMenuClick }: HeaderProps) {\n  const [searchQuery, setSearchQuery] = useState('')\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <div className=\"flex items-center justify-between px-6 py-4\">\n        {/* Côté gauche - Menu mobile et recherche */}\n        <div className=\"flex items-center space-x-4 flex-1\">\n          {/* Bouton menu mobile */}\n          <button\n            onClick={onMenuClick}\n            className=\"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n          >\n            <Menu className=\"h-5 w-5\" />\n          </button>\n\n          {/* Barre de recherche */}\n          <div className=\"relative max-w-md w-full\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <Search className=\"h-4 w-4 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              placeholder=\"Rechercher produits, commandes...\"\n            />\n          </div>\n        </div>\n\n        {/* Côté droit - Notifications et menu utilisateur */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Notifications */}\n          <button className=\"relative p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n            <Bell className=\"h-5 w-5\" />\n            {/* Badge de notification */}\n            <span className=\"absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white\"></span>\n          </button>\n\n          {/* Menu utilisateur */}\n          <UserMenu />\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;;;AAJA;;;;AAUO,SAAS,OAAO,EAAE,WAAW,EAAe;;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAIlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;oCACV,aAAY;;;;;;;;;;;;;;;;;;8BAMlB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAO,WAAU;;8CAChB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAEhB,6LAAC;oCAAK,WAAU;;;;;;;;;;;;sCAIlB,6LAAC,yIAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;;;;;;AAKnB;GA9CgB;KAAA", "debugId": null}}, {"offset": {"line": 974, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/layout/AppLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { usePathname, useRouter } from 'next/navigation'\nimport { getCurrentUser } from '@/lib/auth'\nimport { Sidebar } from './Sidebar'\nimport { Header } from './Header'\nimport type { User } from '@/lib/auth'\n\ninterface AppLayoutProps {\n  children: React.ReactNode\n}\n\nexport function AppLayout({ children }: AppLayoutProps) {\n  const [user, setUser] = useState<User | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const pathname = usePathname()\n  const router = useRouter()\n\n  // Pages qui ne nécessitent pas d'authentification\n  const publicPages = ['/login']\n  const isPublicPage = publicPages.includes(pathname)\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        const currentUser = await getCurrentUser()\n        setUser(currentUser)\n        \n        // Rediriger vers login si pas connecté et page protégée\n        if (!currentUser && !isPublicPage) {\n          router.push(`/login?redirectTo=${encodeURIComponent(pathname)}`)\n          return\n        }\n        \n        // Rediriger vers dashboard si connecté et sur page login\n        if (currentUser && pathname === '/login') {\n          router.push('/')\n          return\n        }\n      } catch (error) {\n        console.error('Erreur vérification auth:', error)\n        if (!isPublicPage) {\n          router.push('/login')\n        }\n      } finally {\n        setIsLoading(false)\n      }\n    }\n\n    checkAuth()\n  }, [pathname, isPublicPage, router])\n\n  // Affichage de chargement\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Chargement...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Page publique (login)\n  if (isPublicPage) {\n    return <>{children}</>\n  }\n\n  // Page protégée sans utilisateur (ne devrait pas arriver grâce à la redirection)\n  if (!user) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <p className=\"text-gray-600\">Redirection vers la connexion...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Layout principal avec sidebar et header\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      {/* Sidebar desktop */}\n      <div className=\"hidden lg:flex\">\n        <Sidebar />\n      </div>\n\n      {/* Sidebar mobile */}\n      {sidebarOpen && (\n        <>\n          {/* Overlay */}\n          <div\n            className=\"fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden\"\n            onClick={() => setSidebarOpen(false)}\n          />\n          \n          {/* Sidebar */}\n          <div className=\"fixed inset-y-0 left-0 z-50 w-64 lg:hidden\">\n            <Sidebar />\n          </div>\n        </>\n      )}\n\n      {/* Contenu principal */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Header onMenuClick={() => setSidebarOpen(true)} />\n        \n        <main className=\"flex-1 overflow-auto\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAaO,SAAS,UAAU,EAAE,QAAQ,EAAkB;;IACpD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,kDAAkD;IAClD,MAAM,cAAc;QAAC;KAAS;IAC9B,MAAM,eAAe,YAAY,QAAQ,CAAC;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;iDAAY;oBAChB,IAAI;wBACF,MAAM,cAAc,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;wBACvC,QAAQ;wBAER,wDAAwD;wBACxD,IAAI,CAAC,eAAe,CAAC,cAAc;4BACjC,OAAO,IAAI,CAAC,CAAC,kBAAkB,EAAE,mBAAmB,WAAW;4BAC/D;wBACF;wBAEA,yDAAyD;wBACzD,IAAI,eAAe,aAAa,UAAU;4BACxC,OAAO,IAAI,CAAC;4BACZ;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,6BAA6B;wBAC3C,IAAI,CAAC,cAAc;4BACjB,OAAO,IAAI,CAAC;wBACd;oBACF,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;8BAAG;QAAC;QAAU;QAAc;KAAO;IAEnC,0BAA0B;IAC1B,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,wBAAwB;IACxB,IAAI,cAAc;QAChB,qBAAO;sBAAG;;IACZ;IAEA,iFAAiF;IACjF,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,0CAA0C;IAC1C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,0IAAA,CAAA,UAAO;;;;;;;;;;YAIT,6BACC;;kCAEE,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,eAAe;;;;;;kCAIhC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,0IAAA,CAAA,UAAO;;;;;;;;;;;;0BAMd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;wBAAC,aAAa,IAAM,eAAe;;;;;;kCAE1C,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GAvGgB;;QAIG,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;;;KALV", "debugId": null}}]}