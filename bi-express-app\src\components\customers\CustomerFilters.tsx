'use client'

import { useState } from 'react'
import { Search, Filter, Truck, ShoppingCart } from 'lucide-react'

export function CustomerFilters() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<'ALL' | 'LOGISTICS' | 'COMMERCE'>('ALL')
  const [selectedCity, setSelectedCity] = useState('')

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Search */}
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Rechercher un client..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Type Filter */}
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <select
            title="Type de client"
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value as 'ALL' | 'LOGISTICS' | 'COMMERCE')}
          >
            <option value="ALL">Tous les types</option>
            <option value="LOGISTICS">🚛 Logistique</option>
            <option value="COMMERCE">🛒 Commerce</option>
          </select>
        </div>

        {/* City Filter */}
        <div>
          <select
            title="Ville"
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={selectedCity}
            onChange={(e) => setSelectedCity(e.target.value)}
          >
            <option value="">Toutes les villes</option>
            <option value="Dakar">Dakar</option>
            <option value="Thiès">Thiès</option>
            <option value="Saint-Louis">Saint-Louis</option>
            <option value="Kaolack">Kaolack</option>
          </select>
        </div>

        {/* Quick Type Filters */}
        <div className="flex gap-2">
          <button
            type="button"
            onClick={() => setSelectedType(selectedType === 'LOGISTICS' ? 'ALL' : 'LOGISTICS')}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedType === 'LOGISTICS'
                ? 'bg-orange-100 text-orange-800 border border-orange-200'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Truck className="h-4 w-4" />
            Logistique
          </button>
          <button
            type="button"
            onClick={() => setSelectedType(selectedType === 'COMMERCE' ? 'ALL' : 'COMMERCE')}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedType === 'COMMERCE'
                ? 'bg-green-100 text-green-800 border border-green-200'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <ShoppingCart className="h-4 w-4" />
            Commerce
          </button>
        </div>
      </div>

      {/* Active Filters */}
      {(searchTerm || selectedType !== 'ALL' || selectedCity) && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center gap-2 flex-wrap">
            <span className="text-sm text-gray-600">Filtres actifs:</span>
            {searchTerm && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                Recherche: "{searchTerm}"
                <button
                  type="button"
                  onClick={() => setSearchTerm('')}
                  className="hover:bg-blue-200 rounded-full p-0.5"
                >
                  ×
                </button>
              </span>
            )}
            {selectedType !== 'ALL' && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                Type: {selectedType === 'LOGISTICS' ? 'Logistique' : 'Commerce'}
                <button
                  type="button"
                  onClick={() => setSelectedType('ALL')}
                  className="hover:bg-purple-200 rounded-full p-0.5"
                >
                  ×
                </button>
              </span>
            )}
            {selectedCity && (
              <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                Ville: {selectedCity}
                <button
                  type="button"
                  onClick={() => setSelectedCity('')}
                  className="hover:bg-green-200 rounded-full p-0.5"
                >
                  ×
                </button>
              </span>
            )}
            <button
              type="button"
              onClick={() => {
                setSearchTerm('')
                setSelectedType('ALL')
                setSelectedCity('')
              }}
              className="text-xs text-gray-500 hover:text-gray-700 underline"
            >
              Effacer tous les filtres
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
