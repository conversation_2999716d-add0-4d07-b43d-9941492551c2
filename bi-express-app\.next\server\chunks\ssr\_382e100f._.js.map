{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/tools/PriceCalculator.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Calculator, DollarSign, TrendingUp, Package } from 'lucide-react'\n\ninterface CalculationData {\n  supplierPrice: number\n  logisticsCost: number\n  margin: number\n  quantity: number\n  transportMode: 'TRUCK' | 'AIR'\n  weight: number\n  exchangeRate: number\n}\n\ninterface CalculationResult {\n  totalCost: number\n  sellingPrice: number\n  totalProfit: number\n  profitMargin: number\n  pricePerUnit: number\n  totalRevenue: number\n}\n\nexport function PriceCalculator() {\n  const [data, setData] = useState<CalculationData>({\n    supplierPrice: 0,\n    logisticsCost: 0,\n    margin: 30,\n    quantity: 1,\n    transportMode: 'TRUCK',\n    weight: 0,\n    exchangeRate: 650 // NGN vers XOF\n  })\n\n  const [result, setResult] = useState<CalculationResult>({\n    totalCost: 0,\n    sellingPrice: 0,\n    totalProfit: 0,\n    profitMargin: 0,\n    pricePerUnit: 0,\n    totalRevenue: 0\n  })\n\n  const [savedCalculations, setSavedCalculations] = useState<Array<{\n    id: string\n    name: string\n    data: CalculationData\n    result: CalculationResult\n    createdAt: string\n  }>>([])\n\n  // Calculer automatiquement quand les données changent\n  useEffect(() => {\n    calculatePrices()\n  }, [data])\n\n  // Charger les calculs sauvegardés\n  useEffect(() => {\n    const saved = localStorage.getItem('priceCalculations')\n    if (saved) {\n      setSavedCalculations(JSON.parse(saved))\n    }\n  }, [])\n\n  const calculatePrices = () => {\n    const { supplierPrice, logisticsCost, margin, quantity, transportMode, weight } = data\n\n    // Coût de transport basé sur le mode et le poids\n    const transportCost = transportMode === 'AIR' \n      ? weight * 150 // 150 FCFA par kg pour l'aérien\n      : weight * 75   // 75 FCFA par kg pour le routier\n\n    // Coût total par unité\n    const totalCostPerUnit = supplierPrice + logisticsCost + (transportCost / quantity)\n    \n    // Prix de vente avec marge\n    const sellingPricePerUnit = totalCostPerUnit * (1 + margin / 100)\n    \n    // Calculs totaux\n    const totalCost = totalCostPerUnit * quantity\n    const totalRevenue = sellingPricePerUnit * quantity\n    const totalProfit = totalRevenue - totalCost\n    const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0\n\n    setResult({\n      totalCost,\n      sellingPrice: sellingPricePerUnit,\n      totalProfit,\n      profitMargin,\n      pricePerUnit: sellingPricePerUnit,\n      totalRevenue\n    })\n  }\n\n  const handleInputChange = (field: keyof CalculationData, value: number | string) => {\n    setData(prev => ({ ...prev, [field]: value }))\n  }\n\n  const saveCalculation = () => {\n    const name = prompt('Nom du calcul:')\n    if (!name) return\n\n    const newCalculation = {\n      id: Date.now().toString(),\n      name,\n      data: { ...data },\n      result: { ...result },\n      createdAt: new Date().toISOString()\n    }\n\n    const updated = [...savedCalculations, newCalculation]\n    setSavedCalculations(updated)\n    localStorage.setItem('priceCalculations', JSON.stringify(updated))\n  }\n\n  const loadCalculation = (calculation: typeof savedCalculations[0]) => {\n    setData(calculation.data)\n  }\n\n  const deleteCalculation = (id: string) => {\n    const updated = savedCalculations.filter(calc => calc.id !== id)\n    setSavedCalculations(updated)\n    localStorage.setItem('priceCalculations', JSON.stringify(updated))\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Formulaire de calcul */}\n        <Card className=\"lg:col-span-2\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Calculator className=\"h-5 w-5\" />\n              Paramètres de calcul\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Prix fournisseur (FCFA)\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"0\"\n                  step=\"0.01\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  value={data.supplierPrice}\n                  onChange={(e) => handleInputChange('supplierPrice', parseFloat(e.target.value) || 0)}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Coûts logistiques (FCFA)\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"0\"\n                  step=\"0.01\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  value={data.logisticsCost}\n                  onChange={(e) => handleInputChange('logisticsCost', parseFloat(e.target.value) || 0)}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Marge souhaitée (%)\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"0\"\n                  max=\"100\"\n                  step=\"0.1\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  value={data.margin}\n                  onChange={(e) => handleInputChange('margin', parseFloat(e.target.value) || 0)}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Quantité\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"1\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  value={data.quantity}\n                  onChange={(e) => handleInputChange('quantity', parseInt(e.target.value) || 1)}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Poids total (kg)\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"0\"\n                  step=\"0.1\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  value={data.weight}\n                  onChange={(e) => handleInputChange('weight', parseFloat(e.target.value) || 0)}\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Mode de transport\n              </label>\n              <div className=\"flex gap-2\">\n                <Button\n                  type=\"button\"\n                  variant={data.transportMode === 'TRUCK' ? 'default' : 'outline'}\n                  onClick={() => handleInputChange('transportMode', 'TRUCK')}\n                  className=\"flex-1\"\n                >\n                  Routier (75 FCFA/kg)\n                </Button>\n                <Button\n                  type=\"button\"\n                  variant={data.transportMode === 'AIR' ? 'default' : 'outline'}\n                  onClick={() => handleInputChange('transportMode', 'AIR')}\n                  className=\"flex-1\"\n                >\n                  Aérien (150 FCFA/kg)\n                </Button>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Taux de change NGN → XOF\n              </label>\n              <input\n                type=\"number\"\n                min=\"0\"\n                step=\"0.01\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                value={data.exchangeRate}\n                onChange={(e) => handleInputChange('exchangeRate', parseFloat(e.target.value) || 650)}\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Résultats */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <TrendingUp className=\"h-5 w-5\" />\n              Résultats\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"bg-blue-50 p-3 rounded-lg\">\n              <div className=\"text-sm text-blue-700 mb-1\">Prix de vente unitaire</div>\n              <div className=\"text-lg font-bold text-blue-900\">\n                {result.pricePerUnit.toLocaleString()} FCFA\n              </div>\n            </div>\n\n            <div className=\"bg-green-50 p-3 rounded-lg\">\n              <div className=\"text-sm text-green-700 mb-1\">Chiffre d'affaires total</div>\n              <div className=\"text-lg font-bold text-green-900\">\n                {result.totalRevenue.toLocaleString()} FCFA\n              </div>\n            </div>\n\n            <div className=\"bg-purple-50 p-3 rounded-lg\">\n              <div className=\"text-sm text-purple-700 mb-1\">Bénéfice total</div>\n              <div className=\"text-lg font-bold text-purple-900\">\n                {result.totalProfit.toLocaleString()} FCFA\n              </div>\n            </div>\n\n            <div className=\"bg-orange-50 p-3 rounded-lg\">\n              <div className=\"text-sm text-orange-700 mb-1\">Marge réelle</div>\n              <div className=\"text-lg font-bold text-orange-900\">\n                {result.profitMargin.toFixed(1)}%\n              </div>\n            </div>\n\n            <Button\n              onClick={saveCalculation}\n              className=\"w-full\"\n              disabled={result.totalRevenue === 0}\n            >\n              Sauvegarder ce calcul\n            </Button>\n          </CardContent>\n        </Card>\n\n        {/* Calculs sauvegardés */}\n        {savedCalculations.length > 0 && (\n          <Card className=\"lg:col-span-3\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Package className=\"h-5 w-5\" />\n                Calculs sauvegardés ({savedCalculations.length})\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                {savedCalculations.map(calculation => (\n                  <div\n                    key={calculation.id}\n                    className=\"p-4 border border-gray-200 rounded-lg hover:border-gray-300\"\n                  >\n                    <div className=\"font-medium mb-2\">{calculation.name}</div>\n                    <div className=\"text-sm text-gray-600 mb-2\">\n                      Prix: {calculation.result.pricePerUnit.toLocaleString()} FCFA\n                    </div>\n                    <div className=\"text-sm text-gray-600 mb-3\">\n                      Marge: {calculation.result.profitMargin.toFixed(1)}%\n                    </div>\n                    <div className=\"flex gap-2\">\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => loadCalculation(calculation)}\n                        className=\"flex-1\"\n                      >\n                        Charger\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => deleteCalculation(calculation.id)}\n                        className=\"text-red-600 hover:text-red-700\"\n                      >\n                        Supprimer\n                      </Button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AALA;;;;;;AA0BO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QAChD,eAAe;QACf,eAAe;QACf,QAAQ;QACR,UAAU;QACV,eAAe;QACf,QAAQ;QACR,cAAc,IAAI,eAAe;IACnC;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;QACtD,WAAW;QACX,cAAc;QACd,aAAa;QACb,cAAc;QACd,cAAc;QACd,cAAc;IAChB;IAEA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAMrD,EAAE;IAEN,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAK;IAET,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,qBAAqB,KAAK,KAAK,CAAC;QAClC;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,EAAE,GAAG;QAElF,iDAAiD;QACjD,MAAM,gBAAgB,kBAAkB,QACpC,SAAS,IAAI,gCAAgC;WAC7C,SAAS,GAAK,iCAAiC;;QAEnD,uBAAuB;QACvB,MAAM,mBAAmB,gBAAgB,gBAAiB,gBAAgB;QAE1E,2BAA2B;QAC3B,MAAM,sBAAsB,mBAAmB,CAAC,IAAI,SAAS,GAAG;QAEhE,iBAAiB;QACjB,MAAM,YAAY,mBAAmB;QACrC,MAAM,eAAe,sBAAsB;QAC3C,MAAM,cAAc,eAAe;QACnC,MAAM,eAAe,eAAe,IAAI,AAAC,cAAc,eAAgB,MAAM;QAE7E,UAAU;YACR;YACA,cAAc;YACd;YACA;YACA,cAAc;YACd;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC,OAA8B;QACvD,QAAQ,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IAC9C;IAEA,MAAM,kBAAkB;QACtB,MAAM,OAAO,OAAO;QACpB,IAAI,CAAC,MAAM;QAEX,MAAM,iBAAiB;YACrB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB;YACA,MAAM;gBAAE,GAAG,IAAI;YAAC;YAChB,QAAQ;gBAAE,GAAG,MAAM;YAAC;YACpB,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,UAAU;eAAI;YAAmB;SAAe;QACtD,qBAAqB;QACrB,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;IAC3D;IAEA,MAAM,kBAAkB,CAAC;QACvB,QAAQ,YAAY,IAAI;IAC1B;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,UAAU,kBAAkB,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC7D,qBAAqB;QACrB,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;IAC3D;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,8MAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAItC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,MAAK;oDACL,WAAU;oDACV,OAAO,KAAK,aAAa;oDACzB,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;sDAItF,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,MAAK;oDACL,WAAU;oDACV,OAAO,KAAK,aAAa;oDACzB,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;;;;;;;8CAKxF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,MAAK;oDACL,WAAU;oDACV,OAAO,KAAK,MAAM;oDAClB,UAAU,CAAC,IAAM,kBAAkB,UAAU,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;sDAI/E,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,WAAU;oDACV,OAAO,KAAK,QAAQ;oDACpB,UAAU,CAAC,IAAM,kBAAkB,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;sDAI/E,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,MAAK;oDACL,WAAU;oDACV,OAAO,KAAK,MAAM;oDAClB,UAAU,CAAC,IAAM,kBAAkB,UAAU,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;;;;;;;8CAKjF,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,KAAK,aAAa,KAAK,UAAU,YAAY;oDACtD,SAAS,IAAM,kBAAkB,iBAAiB;oDAClD,WAAU;8DACX;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,KAAK,aAAa,KAAK,QAAQ,YAAY;oDACpD,SAAS,IAAM,kBAAkB,iBAAiB;oDAClD,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAML,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,MAAK;4CACL,WAAU;4CACV,OAAO,KAAK,YAAY;4CACxB,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAOzF,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAItC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA6B;;;;;;sDAC5C,8OAAC;4CAAI,WAAU;;gDACZ,OAAO,YAAY,CAAC,cAAc;gDAAG;;;;;;;;;;;;;8CAI1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,8OAAC;4CAAI,WAAU;;gDACZ,OAAO,YAAY,CAAC,cAAc;gDAAG;;;;;;;;;;;;;8CAI1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA+B;;;;;;sDAC9C,8OAAC;4CAAI,WAAU;;gDACZ,OAAO,WAAW,CAAC,cAAc;gDAAG;;;;;;;;;;;;;8CAIzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA+B;;;;;;sDAC9C,8OAAC;4CAAI,WAAU;;gDACZ,OAAO,YAAY,CAAC,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAIpC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;oCACV,UAAU,OAAO,YAAY,KAAK;8CACnC;;;;;;;;;;;;;;;;;;gBAOJ,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAY;oCACT,kBAAkB,MAAM;oCAAC;;;;;;;;;;;;sCAGnD,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;0CACZ,kBAAkB,GAAG,CAAC,CAAA,4BACrB,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DAAoB,YAAY,IAAI;;;;;;0DACnD,8OAAC;gDAAI,WAAU;;oDAA6B;oDACnC,YAAY,MAAM,CAAC,YAAY,CAAC,cAAc;oDAAG;;;;;;;0DAE1D,8OAAC;gDAAI,WAAU;;oDAA6B;oDAClC,YAAY,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC;oDAAG;;;;;;;0DAErD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,gBAAgB;wDAC/B,WAAU;kEACX;;;;;;kEAGD,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,kBAAkB,YAAY,EAAE;wDAC/C,WAAU;kEACX;;;;;;;;;;;;;uCAxBE,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCvC", "debugId": null}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "file": "calculator.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/calculator.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '16', height: '20', x: '4', y: '2', rx: '2', key: '1nb95v' }],\n  ['line', { x1: '8', x2: '16', y1: '6', y2: '6', key: 'x4nwl0' }],\n  ['line', { x1: '16', x2: '16', y1: '14', y2: '18', key: 'wjye3r' }],\n  ['path', { d: 'M16 10h.01', key: '1m94wz' }],\n  ['path', { d: 'M12 10h.01', key: '1nrarc' }],\n  ['path', { d: 'M8 10h.01', key: '19clt8' }],\n  ['path', { d: 'M12 14h.01', key: '1etili' }],\n  ['path', { d: 'M8 14h.01', key: '6423bh' }],\n  ['path', { d: 'M12 18h.01', key: 'mhygvu' }],\n  ['path', { d: 'M8 18h.01', key: 'lrp35t' }],\n];\n\n/**\n * @component @name Calculator\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMjAiIHg9IjQiIHk9IjIiIHJ4PSIyIiAvPgogIDxsaW5lIHgxPSI4IiB4Mj0iMTYiIHkxPSI2IiB5Mj0iNiIgLz4KICA8bGluZSB4MT0iMTYiIHgyPSIxNiIgeTE9IjE0IiB5Mj0iMTgiIC8+CiAgPHBhdGggZD0iTTE2IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTBoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTRoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDE0aC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMThoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDE4aC4wMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/calculator\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calculator = createLucideIcon('calculator', __iconNode);\n\nexport default Calculator;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 885, "column": 0}, "map": {"version": 3, "file": "trending-up.js", "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}