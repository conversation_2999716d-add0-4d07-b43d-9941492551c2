{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/api/suppliers/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const search = searchParams.get('search')\n    const city = searchParams.get('city')\n    const businessType = searchParams.get('businessType')\n\n    const where: any = {}\n\n    if (search) {\n      where.OR = [\n        { name: { contains: search, mode: 'insensitive' } },\n        { email: { contains: search, mode: 'insensitive' } },\n        { phone: { contains: search, mode: 'insensitive' } },\n        { businessName: { contains: search, mode: 'insensitive' } }\n      ]\n    }\n\n    if (city) {\n      where.city = city\n    }\n\n    if (businessType) {\n      where.businessType = businessType\n    }\n\n    const suppliers = await prisma.supplier.findMany({\n      where,\n      include: {\n        products: {\n          select: {\n            id: true,\n            name: true,\n            category: true,\n            supplierPrice: true,\n            logisticRate: true,\n            margin: true,\n            isActive: true\n          },\n          where: {\n            isActive: true\n          },\n          take: 10\n        },\n        _count: {\n          select: {\n            products: true\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'desc'\n      }\n    })\n\n    return NextResponse.json(suppliers)\n  } catch (error) {\n    console.error('Erreur lors de la récupération des fournisseurs:', error)\n    return NextResponse.json(\n      { error: 'Erreur lors de la récupération des fournisseurs' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    console.log('Données reçues:', body)\n\n    const {\n      name,\n      businessName,\n      email,\n      phone,\n      address,\n      city,\n      country,\n      businessType,\n      taxId,\n      bankAccount,\n      paymentTerms,\n      specialties,\n      rating,\n      notes\n    } = body\n\n    // Validation des champs obligatoires\n    if (!name || !phone || !city) {\n      return NextResponse.json(\n        { error: 'Les champs nom, téléphone et ville sont obligatoires' },\n        { status: 400 }\n      )\n    }\n\n    // Validation de la ville (doit être une valeur de l'enum)\n    const validCities = ['LAGOS', 'ABUJA', 'KANO']\n    if (!validCities.includes(city)) {\n      return NextResponse.json(\n        { error: 'Ville invalide. Valeurs acceptées: LAGOS, ABUJA, KANO' },\n        { status: 400 }\n      )\n    }\n\n    // Vérifier l'unicité du téléphone\n    const existingPhone = await prisma.supplier.findFirst({\n      where: { phone }\n    })\n\n    if (existingPhone) {\n      return NextResponse.json(\n        { error: 'Ce numéro de téléphone est déjà utilisé' },\n        { status: 400 }\n      )\n    }\n\n    const supplier = await prisma.supplier.create({\n      data: {\n        name,\n        email: email || null,\n        phone,\n        address: address || '',\n        city,\n        specialties: (specialties || []).join(','),\n        rating: rating ? parseFloat(rating) : 5.0\n      }\n    })\n\n    return NextResponse.json(supplier, { status: 201 })\n  } catch (error) {\n    console.error('Erreur lors de la création du fournisseur:', error)\n    return NextResponse.json(\n      { error: 'Erreur lors de la création du fournisseur' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,MAAM,eAAe,aAAa,GAAG,CAAC;QAEtC,MAAM,QAAa,CAAC;QAEpB,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBAAE,MAAM;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBAClD;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACnD;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACnD;oBAAE,cAAc;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;aAC3D;QACH;QAEA,IAAI,MAAM;YACR,MAAM,IAAI,GAAG;QACf;QAEA,IAAI,cAAc;YAChB,MAAM,YAAY,GAAG;QACvB;QAEA,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC/C;YACA,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,UAAU;wBACV,eAAe;wBACf,cAAc;wBACd,QAAQ;wBACR,UAAU;oBACZ;oBACA,OAAO;wBACL,UAAU;oBACZ;oBACA,MAAM;gBACR;gBACA,QAAQ;oBACN,QAAQ;wBACN,UAAU;oBACZ;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oDAAoD;QAClE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkD,GAC3D;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,QAAQ,GAAG,CAAC,mBAAmB;QAE/B,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,KAAK,EACL,OAAO,EACP,IAAI,EACJ,OAAO,EACP,YAAY,EACZ,KAAK,EACL,WAAW,EACX,YAAY,EACZ,WAAW,EACX,MAAM,EACN,KAAK,EACN,GAAG;QAEJ,qCAAqC;QACrC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuD,GAChE;gBAAE,QAAQ;YAAI;QAElB;QAEA,0DAA0D;QAC1D,MAAM,cAAc;YAAC;YAAS;YAAS;SAAO;QAC9C,IAAI,CAAC,YAAY,QAAQ,CAAC,OAAO;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwD,GACjE;gBAAE,QAAQ;YAAI;QAElB;QAEA,kCAAkC;QAClC,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YACpD,OAAO;gBAAE;YAAM;QACjB;QAEA,IAAI,eAAe;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0C,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ;gBACA,OAAO,SAAS;gBAChB;gBACA,SAAS,WAAW;gBACpB;gBACA,aAAa,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC;gBACtC,QAAQ,SAAS,WAAW,UAAU;YACxC;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4C,GACrD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}