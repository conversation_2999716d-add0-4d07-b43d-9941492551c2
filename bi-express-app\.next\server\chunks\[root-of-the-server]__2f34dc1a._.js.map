{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/api/customers/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const search = searchParams.get('search')\n    const type = searchParams.get('type')\n    const city = searchParams.get('city')\n\n    const where: any = {}\n\n    if (search) {\n      where.OR = [\n        { name: { contains: search, mode: 'insensitive' } },\n        { email: { contains: search, mode: 'insensitive' } },\n        { phone: { contains: search, mode: 'insensitive' } }\n      ]\n    }\n\n    if (type) {\n      where.type = type\n    }\n\n    if (city) {\n      where.city = city\n    }\n\n    const customers = await prisma.customer.findMany({\n      where,\n      include: {\n        orders: {\n          select: {\n            id: true,\n            totalAmount: true,\n            status: true,\n            createdAt: true\n          },\n          orderBy: {\n            createdAt: 'desc'\n          },\n          take: 5\n        },\n        _count: {\n          select: {\n            orders: true\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'desc'\n      }\n    })\n\n    return NextResponse.json(customers)\n  } catch (error) {\n    console.error('Erreur lors de la récupération des clients:', error)\n    return NextResponse.json(\n      { error: 'Erreur lors de la récupération des clients' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    console.log('Données client reçues:', body)\n\n    const {\n      name,\n      email,\n      phone,\n      address,\n      city,\n      type,\n      companyName,\n      taxNumber,\n      creditLimit,\n      paymentTerms\n    } = body\n\n    // Validation des champs obligatoires\n    if (!name || !phone || !type) {\n      console.log('Validation échouée - champs manquants:', { name, phone, type })\n      return NextResponse.json(\n        { error: 'Les champs nom, téléphone et type sont obligatoires' },\n        { status: 400 }\n      )\n    }\n\n    // Validation du type de client\n    const validTypes = ['LOGISTICS', 'COMMERCE']\n    if (!validTypes.includes(type)) {\n      console.log('Type invalide:', type)\n      return NextResponse.json(\n        { error: 'Type de client invalide. Valeurs acceptées: LOGISTICS, COMMERCE' },\n        { status: 400 }\n      )\n    }\n\n    console.log('Validation réussie, création du client...')\n\n    // Vérifier l'unicité du téléphone\n    const existingPhone = await prisma.customer.findFirst({\n      where: { phone }\n    })\n\n    if (existingPhone) {\n      return NextResponse.json(\n        { error: 'Ce numéro de téléphone est déjà utilisé' },\n        { status: 400 }\n      )\n    }\n\n    const customer = await prisma.customer.create({\n      data: {\n        name,\n        email: email || null,\n        phone,\n        address: address || '',\n        city: city || 'Dakar',\n        type,\n        companyName: companyName || null,\n        taxNumber: taxNumber || null,\n        creditLimit: creditLimit ? parseFloat(creditLimit) : 0,\n        paymentTerms: paymentTerms ? parseInt(paymentTerms) : 30\n      }\n    })\n\n    return NextResponse.json(customer, { status: 201 })\n  } catch (error) {\n    console.error('Erreur lors de la création du client:', error)\n    return NextResponse.json(\n      { error: 'Erreur lors de la création du client', details: error.message },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,MAAM,OAAO,aAAa,GAAG,CAAC;QAE9B,MAAM,QAAa,CAAC;QAEpB,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBAAE,MAAM;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBAClD;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACnD;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;aACpD;QACH;QAEA,IAAI,MAAM;YACR,MAAM,IAAI,GAAG;QACf;QAEA,IAAI,MAAM;YACR,MAAM,IAAI,GAAG;QACf;QAEA,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC/C;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,aAAa;wBACb,QAAQ;wBACR,WAAW;oBACb;oBACA,SAAS;wBACP,WAAW;oBACb;oBACA,MAAM;gBACR;gBACA,QAAQ;oBACN,QAAQ;wBACN,QAAQ;oBACV;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA6C,GACtD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,QAAQ,GAAG,CAAC,0BAA0B;QAEtC,MAAM,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,WAAW,EACX,SAAS,EACT,WAAW,EACX,YAAY,EACb,GAAG;QAEJ,qCAAqC;QACrC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM;YAC5B,QAAQ,GAAG,CAAC,0CAA0C;gBAAE;gBAAM;gBAAO;YAAK;YAC1E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsD,GAC/D;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,MAAM,aAAa;YAAC;YAAa;SAAW;QAC5C,IAAI,CAAC,WAAW,QAAQ,CAAC,OAAO;YAC9B,QAAQ,GAAG,CAAC,kBAAkB;YAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkE,GAC3E;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC;QAEZ,kCAAkC;QAClC,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YACpD,OAAO;gBAAE;YAAM;QACjB;QAEA,IAAI,eAAe;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0C,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ;gBACA,OAAO,SAAS;gBAChB;gBACA,SAAS,WAAW;gBACpB,MAAM,QAAQ;gBACd;gBACA,aAAa,eAAe;gBAC5B,WAAW,aAAa;gBACxB,aAAa,cAAc,WAAW,eAAe;gBACrD,cAAc,eAAe,SAAS,gBAAgB;YACxD;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAwC,SAAS,MAAM,OAAO;QAAC,GACxE;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}