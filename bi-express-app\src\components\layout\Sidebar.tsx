'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useEffect, useState } from 'react'
import { cn } from '@/lib/utils'
import { getCurrentUser } from '@/lib/auth'
import type { User } from '@/lib/auth'
import {
  LayoutDashboard,
  Users,
  Package,
  ShoppingCart,
  Truck,
  BarChart3,
  Settings,
  Building2,
  DollarSign
} from 'lucide-react'

const navigation = [
  {
    name: 'Dashboard',
    href: '/',
    icon: LayoutDashboard,
  },
  {
    name: 'Fournisseurs',
    href: '/suppliers',
    icon: Building2,
  },
  {
    name: 'Produits',
    href: '/products',
    icon: Package,
  },
  {
    name: 'Commandes',
    href: '/orders',
    icon: ShoppingCart,
  },
  {
    name: 'Clients',
    href: '/customers',
    icon: Users,
  },
  {
    name: 'Logistique',
    href: '/logistics',
    icon: Truck,
  },
  {
    name: 'Portail Client',
    href: '/client-portal',
    icon: Users,
  },
  {
    name: 'Consolidation',
    href: '/consolidation',
    icon: Package,
    adminOnly: true,
  },
  {
    name: 'Devises',
    href: '/currency',
    icon: DollarSign,
  },
  {
    name: 'Rapports',
    href: '/reports',
    icon: BarChart3,
  },
  {
    name: 'Utilisateurs',
    href: '/users',
    icon: Users,
    adminOnly: true,
  },
  {
    name: 'Paramètres',
    href: '/settings',
    icon: Settings,
  },
]

export function Sidebar() {
  const pathname = usePathname()
  const [user, setUser] = useState<User | null>(null)

  useEffect(() => {
    const loadUser = async () => {
      try {
        const currentUser = await getCurrentUser()
        setUser(currentUser)
      } catch (error) {
        console.error('Erreur chargement utilisateur:', error)
      }
    }
    loadUser()
  }, [])

  // Filtrer la navigation selon les permissions
  const filteredNavigation = navigation.filter(item => {
    if (item.adminOnly) {
      return user?.role === 'admin'
    }
    return true
  })

  return (
    <div className="flex h-full w-64 flex-col bg-white border-r border-gray-200">
      {/* Logo */}
      <div className="flex h-16 items-center px-6 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">BE</span>
          </div>
          <div>
            <h1 className="text-lg font-semibold text-gray-900">Bi-Express</h1>
            <p className="text-xs text-gray-500">Nigeria → Dakar</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-1">
        {filteredNavigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                isActive
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              )}
            >
              <item.icon
                className={cn(
                  'mr-3 h-5 w-5',
                  isActive ? 'text-blue-700' : 'text-gray-400'
                )}
              />
              {item.name}
            </Link>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="text-xs text-gray-500 text-center">
          <p>Version 1.0.0</p>
          <p className="mt-1">© 2024 Bi-Express</p>
        </div>
      </div>
    </div>
  )
}
