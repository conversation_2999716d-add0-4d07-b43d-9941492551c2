{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/dashboard/SalesChart.tsx"], "sourcesContent": ["'use client'\n\nimport { \n  LineChart, \n  Line, \n  XAxis, \n  YAxis, \n  CartesianGrid, \n  Tooltip, \n  ResponsiveContainer \n} from 'recharts'\n\n// Données d'exemple pour le graphique\nconst salesData = [\n  { month: 'Jan', revenue: 2400000, profit: 480000 },\n  { month: 'Fév', revenue: 1398000, profit: 279600 },\n  { month: 'Mar', revenue: 9800000, profit: 1960000 },\n  { month: 'Avr', revenue: 3908000, profit: 781600 },\n  { month: 'Mai', revenue: 4800000, profit: 960000 },\n  { month: 'Jun', revenue: 3800000, profit: 760000 },\n  { month: 'Jul', revenue: 4300000, profit: 860000 },\n]\n\nexport function SalesChart() {\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n      <div className=\"mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Évolution des ventes</h3>\n        <p className=\"text-sm text-gray-600 mt-1\">\n          Chi<PERSON><PERSON> d'affaires et bénéfices des 7 derniers mois\n        </p>\n      </div>\n      \n      <div className=\"h-80\">\n        <ResponsiveContainer width=\"100%\" height=\"100%\">\n          <LineChart data={salesData}>\n            <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f0f0f0\" />\n            <XAxis \n              dataKey=\"month\" \n              stroke=\"#6b7280\"\n              fontSize={12}\n            />\n            <YAxis \n              stroke=\"#6b7280\"\n              fontSize={12}\n              tickFormatter={(value) => `${(value / 1000000).toFixed(1)}M`}\n            />\n            <Tooltip \n              formatter={(value: number, name: string) => [\n                `${(value / 1000000).toFixed(2)}M CFA`,\n                name === 'revenue' ? 'Chiffre d\\'affaires' : 'Bénéfices'\n              ]}\n              labelStyle={{ color: '#374151' }}\n              contentStyle={{ \n                backgroundColor: 'white', \n                border: '1px solid #e5e7eb',\n                borderRadius: '8px'\n              }}\n            />\n            <Line \n              type=\"monotone\" \n              dataKey=\"revenue\" \n              stroke=\"#3b82f6\" \n              strokeWidth={3}\n              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}\n              activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}\n            />\n            <Line \n              type=\"monotone\" \n              dataKey=\"profit\" \n              stroke=\"#10b981\" \n              strokeWidth={3}\n              dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}\n              activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2 }}\n            />\n          </LineChart>\n        </ResponsiveContainer>\n      </div>\n      \n      <div className=\"flex items-center justify-center space-x-6 mt-4\">\n        <div className=\"flex items-center\">\n          <div className=\"w-3 h-3 bg-blue-500 rounded-full mr-2\"></div>\n          <span className=\"text-sm text-gray-600\">Chiffre d'affaires</span>\n        </div>\n        <div className=\"flex items-center\">\n          <div className=\"w-3 h-3 bg-green-500 rounded-full mr-2\"></div>\n          <span className=\"text-sm text-gray-600\">Bénéfices</span>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AAYA,sCAAsC;AACtC,MAAM,YAAY;IAChB;QAAE,OAAO;QAAO,SAAS;QAAS,QAAQ;IAAO;IACjD;QAAE,OAAO;QAAO,SAAS;QAAS,QAAQ;IAAO;IACjD;QAAE,OAAO;QAAO,SAAS;QAAS,QAAQ;IAAQ;IAClD;QAAE,OAAO;QAAO,SAAS;QAAS,QAAQ;IAAO;IACjD;QAAE,OAAO;QAAO,SAAS;QAAS,QAAQ;IAAO;IACjD;QAAE,OAAO;QAAO,SAAS;QAAS,QAAQ;IAAO;IACjD;QAAE,OAAO;QAAO,SAAS;QAAS,QAAQ;IAAO;CAClD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAK5C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACvC,cAAA,8OAAC,qJAAA,CAAA,YAAS;wBAAC,MAAM;;0CACf,8OAAC,6JAAA,CAAA,gBAAa;gCAAC,iBAAgB;gCAAM,QAAO;;;;;;0CAC5C,8OAAC,qJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,QAAO;gCACP,UAAU;;;;;;0CAEZ,8OAAC,qJAAA,CAAA,QAAK;gCACJ,QAAO;gCACP,UAAU;gCACV,eAAe,CAAC,QAAU,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;0CAE9D,8OAAC,uJAAA,CAAA,UAAO;gCACN,WAAW,CAAC,OAAe,OAAiB;wCAC1C,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,KAAK,CAAC;wCACtC,SAAS,YAAY,wBAAwB;qCAC9C;gCACD,YAAY;oCAAE,OAAO;gCAAU;gCAC/B,cAAc;oCACZ,iBAAiB;oCACjB,QAAQ;oCACR,cAAc;gCAChB;;;;;;0CAEF,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;oCAAE,MAAM;oCAAW,aAAa;oCAAG,GAAG;gCAAE;gCAC7C,WAAW;oCAAE,GAAG;oCAAG,QAAQ;oCAAW,aAAa;gCAAE;;;;;;0CAEvD,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;oCAAE,MAAM;oCAAW,aAAa;oCAAG,GAAG;gCAAE;gCAC7C,WAAW;oCAAE,GAAG;oCAAG,QAAQ;oCAAW,aAAa;gCAAE;;;;;;;;;;;;;;;;;;;;;;0BAM7D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;kCAE1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;AAKlD", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/currency.ts"], "sourcesContent": ["// Service de conversion de devises NGN/XOF\nexport interface ExchangeRate {\n  from: 'NGN' | 'XOF'\n  to: 'NGN' | 'XOF'\n  rate: number\n  lastUpdated: Date\n}\n\nexport interface CurrencyConversion {\n  amount: number\n  fromCurrency: 'NGN' | 'XOF'\n  toCurrency: 'NGN' | 'XOF'\n  convertedAmount: number\n  exchangeRate: number\n  timestamp: Date\n}\n\n// Cache des taux de change pour éviter trop d'appels API\nlet exchangeRateCache: {\n  NGN_TO_XOF: { rate: number; timestamp: Date } | null\n  XOF_TO_NGN: { rate: number; timestamp: Date } | null\n} = {\n  NGN_TO_XOF: null,\n  XOF_TO_NGN: null\n}\n\nconst CACHE_DURATION = 30 * 60 * 1000 // 30 minutes en millisecondes\n\n// Taux de change par défaut (fallback) basés sur les taux moyens récents\nconst DEFAULT_RATES = {\n  NGN_TO_XOF: 0.38, // 1 NGN ≈ 0.38 XOF\n  XOF_TO_NGN: 2.63   // 1 XOF ≈ 2.63 NGN\n}\n\n/**\n * Récupère les taux de change depuis l'API ExchangeRate-API\n */\nasync function fetchExchangeRates(): Promise<{ NGN_TO_XOF: number; XOF_TO_NGN: number }> {\n  try {\n    // Utilisation de l'API gratuite ExchangeRate-API\n    const response = await fetch('https://api.exchangerate-api.com/v4/latest/NGN', {\n      next: { revalidate: 1800 } // Cache pendant 30 minutes\n    })\n    \n    if (!response.ok) {\n      throw new Error(`API Error: ${response.status}`)\n    }\n    \n    const data = await response.json()\n    \n    // Vérifier si XOF est disponible dans les taux\n    if (!data.rates || !data.rates.XOF) {\n      throw new Error('XOF rate not available')\n    }\n    \n    const ngnToXof = data.rates.XOF\n    const xofToNgn = 1 / ngnToXof\n    \n    return {\n      NGN_TO_XOF: ngnToXof,\n      XOF_TO_NGN: xofToNgn\n    }\n  } catch (error) {\n    console.warn('Erreur lors de la récupération des taux de change:', error)\n    \n    // Fallback vers les taux par défaut\n    return {\n      NGN_TO_XOF: DEFAULT_RATES.NGN_TO_XOF,\n      XOF_TO_NGN: DEFAULT_RATES.XOF_TO_NGN\n    }\n  }\n}\n\n/**\n * Obtient le taux de change actuel avec mise en cache\n */\nexport async function getExchangeRate(from: 'NGN' | 'XOF', to: 'NGN' | 'XOF'): Promise<ExchangeRate> {\n  if (from === to) {\n    return {\n      from,\n      to,\n      rate: 1,\n      lastUpdated: new Date()\n    }\n  }\n  \n  const cacheKey = `${from}_TO_${to}` as keyof typeof exchangeRateCache\n  const cached = exchangeRateCache[cacheKey]\n  \n  // Vérifier si le cache est encore valide\n  if (cached && (Date.now() - cached.timestamp.getTime()) < CACHE_DURATION) {\n    return {\n      from,\n      to,\n      rate: cached.rate,\n      lastUpdated: cached.timestamp\n    }\n  }\n  \n  // Récupérer les nouveaux taux\n  try {\n    const rates = await fetchExchangeRates()\n    const now = new Date()\n    \n    // Mettre à jour le cache\n    exchangeRateCache.NGN_TO_XOF = {\n      rate: rates.NGN_TO_XOF,\n      timestamp: now\n    }\n    exchangeRateCache.XOF_TO_NGN = {\n      rate: rates.XOF_TO_NGN,\n      timestamp: now\n    }\n    \n    const rate = cacheKey === 'NGN_TO_XOF' ? rates.NGN_TO_XOF : rates.XOF_TO_NGN\n    \n    return {\n      from,\n      to,\n      rate,\n      lastUpdated: now\n    }\n  } catch (error) {\n    console.error('Erreur lors de la récupération du taux de change:', error)\n    \n    // Utiliser les taux par défaut\n    const rate = from === 'NGN' ? DEFAULT_RATES.NGN_TO_XOF : DEFAULT_RATES.XOF_TO_NGN\n    \n    return {\n      from,\n      to,\n      rate,\n      lastUpdated: new Date()\n    }\n  }\n}\n\n/**\n * Convertit un montant d'une devise à une autre\n */\nexport async function convertCurrency(\n  amount: number,\n  fromCurrency: 'NGN' | 'XOF',\n  toCurrency: 'NGN' | 'XOF'\n): Promise<CurrencyConversion> {\n  const exchangeRate = await getExchangeRate(fromCurrency, toCurrency)\n  const convertedAmount = amount * exchangeRate.rate\n  \n  return {\n    amount,\n    fromCurrency,\n    toCurrency,\n    convertedAmount: Math.round(convertedAmount * 100) / 100, // Arrondir à 2 décimales\n    exchangeRate: exchangeRate.rate,\n    timestamp: new Date()\n  }\n}\n\n/**\n * Formate un montant avec la devise appropriée\n */\nexport function formatCurrency(amount: number, currency: 'NGN' | 'XOF'): string {\n  const formatter = new Intl.NumberFormat('fr-FR', {\n    style: 'currency',\n    currency: currency,\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0\n  })\n  \n  return formatter.format(amount)\n}\n\n/**\n * Affiche un montant dans les deux devises\n */\nexport async function formatDualCurrency(\n  amount: number,\n  baseCurrency: 'NGN' | 'XOF'\n): Promise<string> {\n  const otherCurrency = baseCurrency === 'NGN' ? 'XOF' : 'NGN'\n  const conversion = await convertCurrency(amount, baseCurrency, otherCurrency)\n  \n  const baseFormatted = formatCurrency(amount, baseCurrency)\n  const convertedFormatted = formatCurrency(conversion.convertedAmount, otherCurrency)\n  \n  return `${baseFormatted} (≈ ${convertedFormatted})`\n}\n\n/**\n * Obtient les taux de change actuels pour l'affichage\n */\nexport async function getCurrentRates(): Promise<{\n  ngnToXof: number\n  xofToNgn: number\n  lastUpdated: Date\n}> {\n  const ngnRate = await getExchangeRate('NGN', 'XOF')\n  const xofRate = await getExchangeRate('XOF', 'NGN')\n  \n  return {\n    ngnToXof: ngnRate.rate,\n    xofToNgn: xofRate.rate,\n    lastUpdated: ngnRate.lastUpdated\n  }\n}\n\n/**\n * Vide le cache des taux de change (utile pour forcer une mise à jour)\n */\nexport function clearExchangeRateCache(): void {\n  exchangeRateCache = {\n    NGN_TO_XOF: null,\n    XOF_TO_NGN: null\n  }\n}\n\n/**\n * Calcule la marge en tenant compte de la conversion de devise\n */\nexport async function calculateMarginWithCurrency(\n  costPrice: number,\n  costCurrency: 'NGN' | 'XOF',\n  sellingPrice: number,\n  sellingCurrency: 'NGN' | 'XOF'\n): Promise<{\n  marginAmount: number\n  marginPercentage: number\n  currency: 'NGN' | 'XOF'\n}> {\n  let normalizedCostPrice = costPrice\n  let normalizedSellingPrice = sellingPrice\n  let resultCurrency = sellingCurrency\n  \n  // Convertir le prix de coût dans la même devise que le prix de vente\n  if (costCurrency !== sellingCurrency) {\n    const conversion = await convertCurrency(costPrice, costCurrency, sellingCurrency)\n    normalizedCostPrice = conversion.convertedAmount\n  }\n  \n  const marginAmount = normalizedSellingPrice - normalizedCostPrice\n  const marginPercentage = normalizedCostPrice > 0 \n    ? (marginAmount / normalizedCostPrice) * 100 \n    : 0\n  \n  return {\n    marginAmount: Math.round(marginAmount * 100) / 100,\n    marginPercentage: Math.round(marginPercentage * 100) / 100,\n    currency: resultCurrency\n  }\n}\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;;;;;;;AAiB3C,yDAAyD;AACzD,IAAI,oBAGA;IACF,YAAY;IACZ,YAAY;AACd;AAEA,MAAM,iBAAiB,KAAK,KAAK,KAAK,8BAA8B;;AAEpE,yEAAyE;AACzE,MAAM,gBAAgB;IACpB,YAAY;IACZ,YAAY,KAAO,mBAAmB;AACxC;AAEA;;CAEC,GACD,eAAe;IACb,IAAI;QACF,iDAAiD;QACjD,MAAM,WAAW,MAAM,MAAM,kDAAkD;YAC7E,MAAM;gBAAE,YAAY;YAAK,EAAE,2BAA2B;QACxD;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,EAAE;QACjD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,+CAA+C;QAC/C,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,GAAG,EAAE;YAClC,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,KAAK,KAAK,CAAC,GAAG;QAC/B,MAAM,WAAW,IAAI;QAErB,OAAO;YACL,YAAY;YACZ,YAAY;QACd;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,sDAAsD;QAEnE,oCAAoC;QACpC,OAAO;YACL,YAAY,cAAc,UAAU;YACpC,YAAY,cAAc,UAAU;QACtC;IACF;AACF;AAKO,eAAe,gBAAgB,IAAmB,EAAE,EAAiB;IAC1E,IAAI,SAAS,IAAI;QACf,OAAO;YACL;YACA;YACA,MAAM;YACN,aAAa,IAAI;QACnB;IACF;IAEA,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE,IAAI;IACnC,MAAM,SAAS,iBAAiB,CAAC,SAAS;IAE1C,yCAAyC;IACzC,IAAI,UAAU,AAAC,KAAK,GAAG,KAAK,OAAO,SAAS,CAAC,OAAO,KAAM,gBAAgB;QACxE,OAAO;YACL;YACA;YACA,MAAM,OAAO,IAAI;YACjB,aAAa,OAAO,SAAS;QAC/B;IACF;IAEA,8BAA8B;IAC9B,IAAI;QACF,MAAM,QAAQ,MAAM;QACpB,MAAM,MAAM,IAAI;QAEhB,yBAAyB;QACzB,kBAAkB,UAAU,GAAG;YAC7B,MAAM,MAAM,UAAU;YACtB,WAAW;QACb;QACA,kBAAkB,UAAU,GAAG;YAC7B,MAAM,MAAM,UAAU;YACtB,WAAW;QACb;QAEA,MAAM,OAAO,aAAa,eAAe,MAAM,UAAU,GAAG,MAAM,UAAU;QAE5E,OAAO;YACL;YACA;YACA;YACA,aAAa;QACf;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QAEnE,+BAA+B;QAC/B,MAAM,OAAO,SAAS,QAAQ,cAAc,UAAU,GAAG,cAAc,UAAU;QAEjF,OAAO;YACL;YACA;YACA;YACA,aAAa,IAAI;QACnB;IACF;AACF;AAKO,eAAe,gBACpB,MAAc,EACd,YAA2B,EAC3B,UAAyB;IAEzB,MAAM,eAAe,MAAM,gBAAgB,cAAc;IACzD,MAAM,kBAAkB,SAAS,aAAa,IAAI;IAElD,OAAO;QACL;QACA;QACA;QACA,iBAAiB,KAAK,KAAK,CAAC,kBAAkB,OAAO;QACrD,cAAc,aAAa,IAAI;QAC/B,WAAW,IAAI;IACjB;AACF;AAKO,SAAS,eAAe,MAAc,EAAE,QAAuB;IACpE,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,SAAS;QAC/C,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB;IAEA,OAAO,UAAU,MAAM,CAAC;AAC1B;AAKO,eAAe,mBACpB,MAAc,EACd,YAA2B;IAE3B,MAAM,gBAAgB,iBAAiB,QAAQ,QAAQ;IACvD,MAAM,aAAa,MAAM,gBAAgB,QAAQ,cAAc;IAE/D,MAAM,gBAAgB,eAAe,QAAQ;IAC7C,MAAM,qBAAqB,eAAe,WAAW,eAAe,EAAE;IAEtE,OAAO,GAAG,cAAc,IAAI,EAAE,mBAAmB,CAAC,CAAC;AACrD;AAKO,eAAe;IAKpB,MAAM,UAAU,MAAM,gBAAgB,OAAO;IAC7C,MAAM,UAAU,MAAM,gBAAgB,OAAO;IAE7C,OAAO;QACL,UAAU,QAAQ,IAAI;QACtB,UAAU,QAAQ,IAAI;QACtB,aAAa,QAAQ,WAAW;IAClC;AACF;AAKO,SAAS;IACd,oBAAoB;QAClB,YAAY;QACZ,YAAY;IACd;AACF;AAKO,eAAe,4BACpB,SAAiB,EACjB,YAA2B,EAC3B,YAAoB,EACpB,eAA8B;IAM9B,IAAI,sBAAsB;IAC1B,IAAI,yBAAyB;IAC7B,IAAI,iBAAiB;IAErB,qEAAqE;IACrE,IAAI,iBAAiB,iBAAiB;QACpC,MAAM,aAAa,MAAM,gBAAgB,WAAW,cAAc;QAClE,sBAAsB,WAAW,eAAe;IAClD;IAEA,MAAM,eAAe,yBAAyB;IAC9C,MAAM,mBAAmB,sBAAsB,IAC3C,AAAC,eAAe,sBAAuB,MACvC;IAEJ,OAAO;QACL,cAAc,KAAK,KAAK,CAAC,eAAe,OAAO;QAC/C,kBAAkB,KAAK,KAAK,CAAC,mBAAmB,OAAO;QACvD,UAAU;IACZ;AACF", "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/currency/ExchangeRateWidget.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { getCurrentRates, clearExchangeRateCache } from '@/lib/currency'\nimport { RefreshCw, TrendingUp, TrendingDown, Clock } from 'lucide-react'\n\ninterface ExchangeRateWidgetProps {\n  className?: string\n  showRefreshButton?: boolean\n}\n\nexport function ExchangeRateWidget({ \n  className = '', \n  showRefreshButton = true \n}: ExchangeRateWidgetProps) {\n  const [rates, setRates] = useState<{\n    ngnToXof: number\n    xofToNgn: number\n    lastUpdated: Date\n  } | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [isRefreshing, setIsRefreshing] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  const fetchRates = async (forceRefresh = false) => {\n    try {\n      setError(null)\n      if (forceRefresh) {\n        setIsRefreshing(true)\n        clearExchangeRateCache()\n      } else {\n        setIsLoading(true)\n      }\n\n      const currentRates = await getCurrentRates()\n      setRates(currentRates)\n    } catch (err) {\n      setError('Erreur lors du chargement des taux')\n      console.error('Erreur taux de change:', err)\n    } finally {\n      setIsLoading(false)\n      setIsRefreshing(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchRates()\n    \n    // Actualiser automatiquement toutes les 30 minutes\n    const interval = setInterval(() => {\n      fetchRates(true)\n    }, 30 * 60 * 1000)\n\n    return () => clearInterval(interval)\n  }, [])\n\n  const formatTime = (date: Date) => {\n    return new Intl.DateTimeFormat('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    }).format(date)\n  }\n\n  const formatRate = (rate: number) => {\n    return rate.toFixed(4)\n  }\n\n  if (isLoading && !rates) {\n    return (\n      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 ${className}`}>\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-2\"></div>\n          <div className=\"h-6 bg-gray-200 rounded w-3/4 mb-1\"></div>\n          <div className=\"h-6 bg-gray-200 rounded w-3/4\"></div>\n        </div>\n      </div>\n    )\n  }\n\n  if (error && !rates) {\n    return (\n      <div className={`bg-white rounded-lg shadow-sm border border-red-200 p-4 ${className}`}>\n        <div className=\"text-red-600 text-sm\">\n          <p className=\"font-medium\">Erreur de chargement</p>\n          <p>{error}</p>\n          <button\n            onClick={() => fetchRates(true)}\n            className=\"mt-2 text-xs underline hover:no-underline\"\n          >\n            Réessayer\n          </button>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 ${className}`}>\n      <div className=\"flex items-center justify-between mb-3\">\n        <h3 className=\"text-sm font-medium text-gray-900 flex items-center\">\n          <TrendingUp className=\"h-4 w-4 mr-2 text-green-600\" />\n          Taux de change\n        </h3>\n        {showRefreshButton && (\n          <button\n            onClick={() => fetchRates(true)}\n            disabled={isRefreshing}\n            className=\"p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50\"\n            title=\"Actualiser les taux\"\n          >\n            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />\n          </button>\n        )}\n      </div>\n\n      {rates && (\n        <div className=\"space-y-3\">\n          {/* NGN vers XOF */}\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-sm font-medium text-gray-700\">1 NGN</span>\n              <span className=\"text-xs text-gray-400\">→</span>\n              <span className=\"text-sm font-medium text-blue-600\">\n                {formatRate(rates.ngnToXof)} XOF\n              </span>\n            </div>\n            <TrendingUp className=\"h-3 w-3 text-green-500\" />\n          </div>\n\n          {/* XOF vers NGN */}\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-sm font-medium text-gray-700\">1 XOF</span>\n              <span className=\"text-xs text-gray-400\">→</span>\n              <span className=\"text-sm font-medium text-blue-600\">\n                {formatRate(rates.xofToNgn)} NGN\n              </span>\n            </div>\n            <TrendingDown className=\"h-3 w-3 text-red-500\" />\n          </div>\n\n          {/* Dernière mise à jour */}\n          <div className=\"pt-2 border-t border-gray-100\">\n            <div className=\"flex items-center text-xs text-gray-500\">\n              <Clock className=\"h-3 w-3 mr-1\" />\n              <span>Mis à jour à {formatTime(rates.lastUpdated)}</span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {error && rates && (\n        <div className=\"mt-2 text-xs text-amber-600 bg-amber-50 p-2 rounded\">\n          ⚠️ Utilisation des taux en cache - {error}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAWO,SAAS,mBAAmB,EACjC,YAAY,EAAE,EACd,oBAAoB,IAAI,EACA;IACxB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIvB;IACV,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,aAAa,OAAO,eAAe,KAAK;QAC5C,IAAI;YACF,SAAS;YACT,IAAI,cAAc;gBAChB,gBAAgB;gBAChB,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;YACvB,OAAO;gBACL,aAAa;YACf;YAEA,MAAM,eAAe,MAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD;YACzC,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;YACb,gBAAgB;QAClB;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,mDAAmD;QACnD,MAAM,WAAW,YAAY;YAC3B,WAAW;QACb,GAAG,KAAK,KAAK;QAEb,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,OAAO,CAAC;IACtB;IAEA,IAAI,aAAa,CAAC,OAAO;QACvB,qBACE,8OAAC;YAAI,WAAW,CAAC,yDAAyD,EAAE,WAAW;sBACrF,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,SAAS,CAAC,OAAO;QACnB,qBACE,8OAAC;YAAI,WAAW,CAAC,wDAAwD,EAAE,WAAW;sBACpF,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAc;;;;;;kCAC3B,8OAAC;kCAAG;;;;;;kCACJ,8OAAC;wBACC,SAAS,IAAM,WAAW;wBAC1B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,yDAAyD,EAAE,WAAW;;0BACrF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAgC;;;;;;;oBAGvD,mCACC,8OAAC;wBACC,SAAS,IAAM,WAAW;wBAC1B,UAAU;wBACV,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAW,CAAC,QAAQ,EAAE,eAAe,iBAAiB,IAAI;;;;;;;;;;;;;;;;;YAK1E,uBACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;kDACpD,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;;4CACb,WAAW,MAAM,QAAQ;4CAAE;;;;;;;;;;;;;0CAGhC,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;kCAIxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;kDACpD,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;;4CACb,WAAW,MAAM,QAAQ;4CAAE;;;;;;;;;;;;;0CAGhC,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;kCAI1B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;;wCAAK;wCAAc,WAAW,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;;YAMvD,SAAS,uBACR,8OAAC;gBAAI,WAAU;;oBAAsD;oBAC/B;;;;;;;;;;;;;AAK9C", "debugId": null}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/dashboard/QuickActions.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { \n  Plus, \n  ShoppingCart, \n  Users, \n  Package, \n  Truck, \n  FileText, \n  Calculator,\n  Search,\n  Bell,\n  Settings\n} from 'lucide-react'\nimport { Button } from '@/components/ui/button'\n\nconst quickActions = [\n  {\n    title: 'Nouvelle Commande',\n    description: 'Créer une nouvelle commande client',\n    icon: ShoppingCart,\n    href: '/orders/new',\n    color: 'bg-blue-500 hover:bg-blue-600',\n    textColor: 'text-white'\n  },\n  {\n    title: 'Ajouter Fournisseur',\n    description: 'Enregistrer un nouveau fournisseur',\n    icon: Users,\n    href: '/suppliers/new',\n    color: 'bg-green-500 hover:bg-green-600',\n    textColor: 'text-white'\n  },\n  {\n    title: 'Ajouter Produit',\n    description: 'Ajouter un nouveau produit au catalogue',\n    icon: Package,\n    href: '/products/new',\n    color: 'bg-purple-500 hover:bg-purple-600',\n    textColor: 'text-white'\n  },\n  {\n    title: 'Nouvelle Expédition',\n    description: 'Organiser une nouvelle expédition',\n    icon: Truck,\n    href: '/shipments/new',\n    color: 'bg-orange-500 hover:bg-orange-600',\n    textColor: 'text-white'\n  },\n  {\n    title: 'Générer Rapport',\n    description: 'Créer un rapport de ventes',\n    icon: FileText,\n    href: '/reports',\n    color: 'bg-indigo-500 hover:bg-indigo-600',\n    textColor: 'text-white'\n  },\n  {\n    title: 'Calculateur Prix',\n    description: 'Calculer prix avec marges',\n    icon: Calculator,\n    href: '/tools/calculator',\n    color: 'bg-teal-500 hover:bg-teal-600',\n    textColor: 'text-white'\n  }\n]\n\nconst utilityActions = [\n  {\n    title: 'Recherche Globale',\n    description: 'Rechercher dans toute l\\'application',\n    icon: Search,\n    action: 'search',\n    color: 'bg-gray-100 hover:bg-gray-200',\n    textColor: 'text-gray-700'\n  },\n  {\n    title: 'Notifications',\n    description: 'Voir toutes les notifications',\n    icon: Bell,\n    href: '/notifications',\n    color: 'bg-yellow-100 hover:bg-yellow-200',\n    textColor: 'text-yellow-700'\n  },\n  {\n    title: 'Paramètres',\n    description: 'Configuration de l\\'application',\n    icon: Settings,\n    href: '/settings',\n    color: 'bg-gray-100 hover:bg-gray-200',\n    textColor: 'text-gray-700'\n  }\n]\n\nexport function QuickActions() {\n  const handleSearch = () => {\n    // Ouvrir une modal de recherche ou focus sur le champ de recherche\n    const searchInput = document.querySelector('input[type=\"search\"]') as HTMLInputElement\n    if (searchInput) {\n      searchInput.focus()\n    } else {\n      // Si pas de champ de recherche, rediriger vers une page de recherche\n      window.location.href = '/search'\n    }\n  }\n\n  const handleAction = (action: string) => {\n    switch (action) {\n      case 'search':\n        handleSearch()\n        break\n      default:\n        break\n    }\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n      <div className=\"mb-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-2\">Actions Rapides</h2>\n        <p className=\"text-sm text-gray-600\">\n          Accès direct aux fonctionnalités principales de l'application\n        </p>\n      </div>\n\n      {/* Actions principales */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-6\">\n        {quickActions.map((action, index) => (\n          <Link key={index} href={action.href}>\n            <Button\n              className={`w-full h-auto p-4 flex flex-col items-center space-y-2 ${action.color} ${action.textColor} border-0`}\n              variant=\"default\"\n            >\n              <action.icon className=\"h-6 w-6\" />\n              <div className=\"text-center\">\n                <div className=\"font-medium text-sm\">{action.title}</div>\n                <div className=\"text-xs opacity-90 mt-1\">{action.description}</div>\n              </div>\n            </Button>\n          </Link>\n        ))}\n      </div>\n\n      {/* Actions utilitaires */}\n      <div className=\"border-t border-gray-200 pt-4\">\n        <h3 className=\"text-sm font-medium text-gray-700 mb-3\">Outils</h3>\n        <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-3\">\n          {utilityActions.map((action, index) => (\n            action.href ? (\n              <Link key={index} href={action.href}>\n                <Button\n                  className={`w-full h-auto p-3 flex items-center space-x-3 ${action.color} ${action.textColor} border border-gray-300`}\n                  variant=\"outline\"\n                >\n                  <action.icon className=\"h-4 w-4\" />\n                  <div className=\"text-left flex-1\">\n                    <div className=\"font-medium text-xs\">{action.title}</div>\n                    <div className=\"text-xs opacity-75\">{action.description}</div>\n                  </div>\n                </Button>\n              </Link>\n            ) : (\n              <Button\n                key={index}\n                onClick={() => handleAction(action.action!)}\n                className={`w-full h-auto p-3 flex items-center space-x-3 ${action.color} ${action.textColor} border border-gray-300`}\n                variant=\"outline\"\n              >\n                <action.icon className=\"h-4 w-4\" />\n                <div className=\"text-left flex-1\">\n                  <div className=\"font-medium text-xs\">{action.title}</div>\n                  <div className=\"text-xs opacity-75\">{action.description}</div>\n                </div>\n              </Button>\n            )\n          ))}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAfA;;;;;AAiBA,MAAM,eAAe;IACnB;QACE,OAAO;QACP,aAAa;QACb,MAAM,sNAAA,CAAA,eAAY;QAClB,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,wMAAA,CAAA,UAAO;QACb,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,8MAAA,CAAA,WAAQ;QACd,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,8MAAA,CAAA,aAAU;QAChB,MAAM;QACN,OAAO;QACP,WAAW;IACb;CACD;AAED,MAAM,iBAAiB;IACrB;QACE,OAAO;QACP,aAAa;QACb,MAAM,sMAAA,CAAA,SAAM;QACZ,QAAQ;QACR,OAAO;QACP,WAAW;IACb;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,kMAAA,CAAA,OAAI;QACV,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,0MAAA,CAAA,WAAQ;QACd,MAAM;QACN,OAAO;QACP,WAAW;IACb;CACD;AAEM,SAAS;IACd,MAAM,eAAe;QACnB,mEAAmE;QACnE,MAAM,cAAc,SAAS,aAAa,CAAC;QAC3C,IAAI,aAAa;YACf,YAAY,KAAK;QACnB,OAAO;YACL,qEAAqE;YACrE,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH;gBACA;YACF;gBACE;QACJ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAMvC,8OAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,8OAAC,4JAAA,CAAA,UAAI;wBAAa,MAAM,OAAO,IAAI;kCACjC,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,WAAW,CAAC,uDAAuD,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC,SAAS,CAAC;4BAChH,SAAQ;;8CAER,8OAAC,OAAO,IAAI;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAuB,OAAO,KAAK;;;;;;sDAClD,8OAAC;4CAAI,WAAU;sDAA2B,OAAO,WAAW;;;;;;;;;;;;;;;;;;uBARvD;;;;;;;;;;0BAgBf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,QAC3B,OAAO,IAAI,iBACT,8OAAC,4JAAA,CAAA,UAAI;gCAAa,MAAM,OAAO,IAAI;0CACjC,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,WAAW,CAAC,8CAA8C,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC,uBAAuB,CAAC;oCACrH,SAAQ;;sDAER,8OAAC,OAAO,IAAI;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAuB,OAAO,KAAK;;;;;;8DAClD,8OAAC;oDAAI,WAAU;8DAAsB,OAAO,WAAW;;;;;;;;;;;;;;;;;;+BARlD;;;;qDAaX,8OAAC,kIAAA,CAAA,SAAM;gCAEL,SAAS,IAAM,aAAa,OAAO,MAAM;gCACzC,WAAW,CAAC,8CAA8C,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC,uBAAuB,CAAC;gCACrH,SAAQ;;kDAER,8OAAC,OAAO,IAAI;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAuB,OAAO,KAAK;;;;;;0DAClD,8OAAC;gDAAI,WAAU;0DAAsB,OAAO,WAAW;;;;;;;;;;;;;+BARpD;;;;;;;;;;;;;;;;;;;;;;AAiBrB", "debugId": null}}]}