{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/hooks/useApi.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\ninterface ApiState<T> {\n  data: T | null\n  loading: boolean\n  error: string | null\n}\n\ninterface ApiOptions {\n  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'\n  body?: any\n  headers?: Record<string, string>\n}\n\nexport function useApi<T>(url: string, options?: ApiOptions): ApiState<T> & { refetch: () => void } {\n  const [state, setState] = useState<ApiState<T>>({\n    data: null,\n    loading: true,\n    error: null\n  })\n\n  const fetchData = async () => {\n    try {\n      setState(prev => ({ ...prev, loading: true, error: null }))\n      \n      const fetchOptions: RequestInit = {\n        method: options?.method || 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          ...options?.headers\n        }\n      }\n\n      if (options?.body && options.method !== 'GET') {\n        fetchOptions.body = JSON.stringify(options.body)\n      }\n\n      const response = await fetch(url, fetchOptions)\n      \n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`)\n      }\n      \n      const data = await response.json()\n      setState({ data, loading: false, error: null })\n    } catch (error) {\n      setState({\n        data: null,\n        loading: false,\n        error: error instanceof Error ? error.message : 'Une erreur est survenue'\n      })\n    }\n  }\n\n  useEffect(() => {\n    fetchData()\n  }, [url, JSON.stringify(options)])\n\n  return {\n    ...state,\n    refetch: fetchData\n  }\n}\n\nexport async function apiCall<T>(url: string, options?: ApiOptions): Promise<T> {\n  const fetchOptions: RequestInit = {\n    method: options?.method || 'GET',\n    headers: {\n      'Content-Type': 'application/json',\n      ...options?.headers\n    }\n  }\n\n  if (options?.body && options.method !== 'GET') {\n    fetchOptions.body = JSON.stringify(options.body)\n  }\n\n  const response = await fetch(url, fetchOptions)\n  \n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}))\n    throw new Error(errorData.error || `HTTP error! status: ${response.status}`)\n  }\n  \n  return response.json()\n}\n\n// Hooks spécialisés pour chaque entité\nexport function useSuppliers() {\n  return useApi<any[]>('/api/suppliers')\n}\n\nexport function useCustomers() {\n  return useApi<any[]>('/api/customers')\n}\n\nexport function useProducts() {\n  return useApi<any[]>('/api/products')\n}\n\nexport function useOrders() {\n  return useApi<any[]>('/api/orders')\n}\n\nexport function useCarriers() {\n  return useApi<any[]>('/api/carriers')\n}\n\nexport function useShipments() {\n  return useApi<any[]>('/api/shipments')\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;AAFA;;AAgBO,SAAS,OAAU,GAAW,EAAE,OAAoB;;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAC9C,MAAM;QACN,SAAS;QACT,OAAO;IACT;IAEA,MAAM,YAAY;QAChB,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK,CAAC;YAEzD,MAAM,eAA4B;gBAChC,QAAQ,SAAS,UAAU;gBAC3B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,SAAS,OAAO;gBACrB;YACF;YAEA,IAAI,SAAS,QAAQ,QAAQ,MAAM,KAAK,OAAO;gBAC7C,aAAa,IAAI,GAAG,KAAK,SAAS,CAAC,QAAQ,IAAI;YACjD;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS;gBAAE;gBAAM,SAAS;gBAAO,OAAO;YAAK;QAC/C,EAAE,OAAO,OAAO;YACd,SAAS;gBACP,MAAM;gBACN,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR;QACF;2BAAG;QAAC;QAAK,KAAK,SAAS,CAAC;KAAS;IAEjC,OAAO;QACL,GAAG,KAAK;QACR,SAAS;IACX;AACF;GAhDgB;AAkDT,eAAe,QAAW,GAAW,EAAE,OAAoB;IAChE,MAAM,eAA4B;QAChC,QAAQ,SAAS,UAAU;QAC3B,SAAS;YACP,gBAAgB;YAChB,GAAG,SAAS,OAAO;QACrB;IACF;IAEA,IAAI,SAAS,QAAQ,QAAQ,MAAM,KAAK,OAAO;QAC7C,aAAa,IAAI,GAAG,KAAK,SAAS,CAAC,QAAQ,IAAI;IACjD;IAEA,MAAM,WAAW,MAAM,MAAM,KAAK;IAElC,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QACvD,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;IAC7E;IAEA,OAAO,SAAS,IAAI;AACtB;AAGO,SAAS;;IACd,OAAO,OAAc;AACvB;IAFgB;;QACP;;;AAGF,SAAS;;IACd,OAAO,OAAc;AACvB;IAFgB;;QACP;;;AAGF,SAAS;;IACd,OAAO,OAAc;AACvB;IAFgB;;QACP;;;AAGF,SAAS;;IACd,OAAO,OAAc;AACvB;IAFgB;;QACP;;;AAGF,SAAS;;IACd,OAAO,OAAc;AACvB;IAFgB;;QACP;;;AAGF,SAAS;;IACd,OAAO,OAAc;AACvB;IAFgB;;QACP", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/customers/CustomersList.tsx"], "sourcesContent": ["'use client'\n\nimport { useCustomers } from '@/hooks/useApi'\nimport { Badge } from '@/components/ui/badge-component'\nimport { Star, Phone, Mail, MapPin, Package, AlertCircle, Loader2, Truck, ShoppingCart } from 'lucide-react'\n\nconst typeColors = {\n  LOGISTICS: 'bg-orange-100 text-orange-800',\n  COMMERCE: 'bg-green-100 text-green-800'\n}\n\nconst typeLabels = {\n  LOGISTICS: 'Logistique',\n  COMMERCE: 'Commerce'\n}\n\nconst typeIcons = {\n  LOGISTICS: Truck,\n  COMMERCE: ShoppingCart\n}\n\nfunction StarRating({ rating }: { rating: number }) {\n  return (\n    <div className=\"flex items-center space-x-1\">\n      {[1, 2, 3, 4, 5].map((star) => (\n        <Star\n          key={star}\n          className={`h-4 w-4 ${\n            star <= rating\n              ? 'text-yellow-400 fill-current'\n              : 'text-gray-300'\n          }`}\n        />\n      ))}\n      <span className=\"text-sm text-gray-600 ml-1\">({rating.toFixed(1)})</span>\n    </div>\n  )\n}\n\nexport function CustomersList() {\n  const { data: customers, loading, error, refetch } = useCustomers()\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center justify-center py-12\">\n          <Loader2 className=\"h-8 w-8 animate-spin text-blue-600\" />\n          <span className=\"ml-2 text-gray-600\">Chargement des clients...</span>\n        </div>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center justify-center py-12\">\n          <AlertCircle className=\"h-8 w-8 text-red-500\" />\n          <div className=\"ml-3\">\n            <p className=\"text-red-600 font-medium\">Erreur de chargement</p>\n            <p className=\"text-red-500 text-sm\">{error}</p>\n            <button \n              type=\"button\"\n              onClick={refetch}\n              className=\"mt-2 text-blue-600 hover:text-blue-700 text-sm font-medium\"\n            >\n              Réessayer\n            </button>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6\">\n        {customers?.map((customer) => {\n          const TypeIcon = typeIcons[customer.type]\n          return (\n            <div key={customer.id} className=\"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow\">\n              {/* Header */}\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"flex-1\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                    {customer.name}\n                  </h3>\n                  <div className=\"flex items-center gap-2\">\n                    <Badge className={typeColors[customer.type]}>\n                      <TypeIcon className=\"h-3 w-3 mr-1\" />\n                      {typeLabels[customer.type]}\n                    </Badge>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <StarRating rating={customer.rating || 4.0} />\n                </div>\n              </div>\n\n              {/* Contact Info */}\n              <div className=\"space-y-2 mb-4\">\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <Phone className=\"h-4 w-4 mr-2\" />\n                  {customer.phone}\n                </div>\n                {customer.email && (\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <Mail className=\"h-4 w-4 mr-2\" />\n                    {customer.email}\n                  </div>\n                )}\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <MapPin className=\"h-4 w-4 mr-2\" />\n                  {customer.address || customer.city}\n                </div>\n              </div>\n\n              {/* Business Info */}\n              {customer.businessType && (\n                <div className=\"mb-4\">\n                  <p className=\"text-sm font-medium text-gray-700 mb-1\">Type d'entreprise:</p>\n                  <p className=\"text-sm text-gray-600\">{customer.businessType}</p>\n                </div>\n              )}\n\n              {/* Stats */}\n              <div className=\"grid grid-cols-2 gap-4 pt-4 border-t border-gray-200\">\n                <div className=\"text-center\">\n                  <div className=\"flex items-center justify-center mb-1\">\n                    <Package className=\"h-4 w-4 text-gray-400 mr-1\" />\n                  </div>\n                  <p className=\"text-lg font-semibold text-gray-900\">\n                    {customer.orders?.length || 0}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">Commandes</p>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"flex items-center justify-center mb-1\">\n                    <Package className=\"h-4 w-4 text-gray-400 mr-1\" />\n                  </div>\n                  <p className=\"text-lg font-semibold text-gray-900\">\n                    {customer.shipments?.length || 0}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">Expéditions</p>\n                </div>\n              </div>\n\n              {/* Actions */}\n              <div className=\"mt-4 flex space-x-2\">\n                <button \n                  type=\"button\"\n                  className=\"flex-1 bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors\"\n                >\n                  Voir détails\n                </button>\n                <button \n                  type=\"button\"\n                  className=\"px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors\"\n                >\n                  Modifier\n                </button>\n              </div>\n            </div>\n          )\n        })}\n      </div>\n\n      {(!customers || customers.length === 0) && (\n        <div className=\"text-center py-12\">\n          <Package className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n          <p className=\"text-gray-500 text-lg\">Aucun client trouvé</p>\n          <p className=\"text-gray-400 text-sm mt-1\">\n            Commencez par ajouter votre premier client\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,aAAa;IACjB,WAAW;IACX,UAAU;AACZ;AAEA,MAAM,aAAa;IACjB,WAAW;IACX,UAAU;AACZ;AAEA,MAAM,YAAY;IAChB,WAAW,uMAAA,CAAA,QAAK;IAChB,UAAU,yNAAA,CAAA,eAAY;AACxB;AAEA,SAAS,WAAW,EAAE,MAAM,EAAsB;IAChD,qBACE,6LAAC;QAAI,WAAU;;YACZ;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC,qMAAA,CAAA,OAAI;oBAEH,WAAW,CAAC,QAAQ,EAClB,QAAQ,SACJ,iCACA,iBACJ;mBALG;;;;;0BAQT,6LAAC;gBAAK,WAAU;;oBAA6B;oBAAE,OAAO,OAAO,CAAC;oBAAG;;;;;;;;;;;;;AAGvE;KAhBS;AAkBF,SAAS;;IACd,MAAM,EAAE,MAAM,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEhE,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAK,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI7C;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA2B;;;;;;0CACxC,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;0CACrC,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACZ,WAAW,IAAI,CAAC;oBACf,MAAM,WAAW,SAAS,CAAC,SAAS,IAAI,CAAC;oBACzC,qBACE,6LAAC;wBAAsB,WAAU;;0CAE/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,SAAS,IAAI;;;;;;0DAEhB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,iJAAA,CAAA,QAAK;oDAAC,WAAW,UAAU,CAAC,SAAS,IAAI,CAAC;;sEACzC,6LAAC;4DAAS,WAAU;;;;;;wDACnB,UAAU,CAAC,SAAS,IAAI,CAAC;;;;;;;;;;;;;;;;;;kDAIhC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAW,QAAQ,SAAS,MAAM,IAAI;;;;;;;;;;;;;;;;;0CAK3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,SAAS,KAAK;;;;;;;oCAEhB,SAAS,KAAK,kBACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,SAAS,KAAK;;;;;;;kDAGnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CACjB,SAAS,OAAO,IAAI,SAAS,IAAI;;;;;;;;;;;;;4BAKrC,SAAS,YAAY,kBACpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAyC;;;;;;kDACtD,6LAAC;wCAAE,WAAU;kDAAyB,SAAS,YAAY;;;;;;;;;;;;0CAK/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6LAAC;gDAAE,WAAU;0DACV,SAAS,MAAM,EAAE,UAAU;;;;;;0DAE9B,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6LAAC;gDAAE,WAAU;0DACV,SAAS,SAAS,EAAE,UAAU;;;;;;0DAEjC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAKzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;uBA9EK,SAAS,EAAE;;;;;gBAoFzB;;;;;;YAGD,CAAC,CAAC,aAAa,UAAU,MAAM,KAAK,CAAC,mBACpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;;AAOpD;GA3IgB;;QACuC,yHAAA,CAAA,eAAY;;;MADnD", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/customers/CustomerFilters.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Search, Filter, Truck, ShoppingCart } from 'lucide-react'\n\nexport function CustomerFilters() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedType, setSelectedType] = useState<'ALL' | 'LOGISTICS' | 'COMMERCE'>('ALL')\n  const [selectedCity, setSelectedCity] = useState('')\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4\">\n      <div className=\"flex flex-col lg:flex-row gap-4\">\n        {/* Search */}\n        <div className=\"flex-1\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Rechercher un client...\"\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n            />\n          </div>\n        </div>\n\n        {/* Type Filter */}\n        <div className=\"flex items-center gap-2\">\n          <Filter className=\"h-4 w-4 text-gray-500\" />\n          <select\n            title=\"Type de client\"\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            value={selectedType}\n            onChange={(e) => setSelectedType(e.target.value as 'ALL' | 'LOGISTICS' | 'COMMERCE')}\n          >\n            <option value=\"ALL\">Tous les types</option>\n            <option value=\"LOGISTICS\">🚛 Logistique</option>\n            <option value=\"COMMERCE\">🛒 Commerce</option>\n          </select>\n        </div>\n\n        {/* City Filter */}\n        <div>\n          <select\n            title=\"Ville\"\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            value={selectedCity}\n            onChange={(e) => setSelectedCity(e.target.value)}\n          >\n            <option value=\"\">Toutes les villes</option>\n            <option value=\"Dakar\">Dakar</option>\n            <option value=\"Thiès\">Thiès</option>\n            <option value=\"Saint-Louis\">Saint-Louis</option>\n            <option value=\"Kaolack\">Kaolack</option>\n          </select>\n        </div>\n\n        {/* Quick Type Filters */}\n        <div className=\"flex gap-2\">\n          <button\n            type=\"button\"\n            onClick={() => setSelectedType(selectedType === 'LOGISTICS' ? 'ALL' : 'LOGISTICS')}\n            className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${\n              selectedType === 'LOGISTICS'\n                ? 'bg-orange-100 text-orange-800 border border-orange-200'\n                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n            }`}\n          >\n            <Truck className=\"h-4 w-4\" />\n            Logistique\n          </button>\n          <button\n            type=\"button\"\n            onClick={() => setSelectedType(selectedType === 'COMMERCE' ? 'ALL' : 'COMMERCE')}\n            className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${\n              selectedType === 'COMMERCE'\n                ? 'bg-green-100 text-green-800 border border-green-200'\n                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n            }`}\n          >\n            <ShoppingCart className=\"h-4 w-4\" />\n            Commerce\n          </button>\n        </div>\n      </div>\n\n      {/* Active Filters */}\n      {(searchTerm || selectedType !== 'ALL' || selectedCity) && (\n        <div className=\"mt-4 pt-4 border-t border-gray-200\">\n          <div className=\"flex items-center gap-2 flex-wrap\">\n            <span className=\"text-sm text-gray-600\">Filtres actifs:</span>\n            {searchTerm && (\n              <span className=\"inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">\n                Recherche: \"{searchTerm}\"\n                <button\n                  type=\"button\"\n                  onClick={() => setSearchTerm('')}\n                  className=\"hover:bg-blue-200 rounded-full p-0.5\"\n                >\n                  ×\n                </button>\n              </span>\n            )}\n            {selectedType !== 'ALL' && (\n              <span className=\"inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full\">\n                Type: {selectedType === 'LOGISTICS' ? 'Logistique' : 'Commerce'}\n                <button\n                  type=\"button\"\n                  onClick={() => setSelectedType('ALL')}\n                  className=\"hover:bg-purple-200 rounded-full p-0.5\"\n                >\n                  ×\n                </button>\n              </span>\n            )}\n            {selectedCity && (\n              <span className=\"inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full\">\n                Ville: {selectedCity}\n                <button\n                  type=\"button\"\n                  onClick={() => setSelectedCity('')}\n                  className=\"hover:bg-green-200 rounded-full p-0.5\"\n                >\n                  ×\n                </button>\n              </span>\n            )}\n            <button\n              type=\"button\"\n              onClick={() => {\n                setSearchTerm('')\n                setSelectedType('ALL')\n                setSelectedCity('')\n              }}\n              className=\"text-xs text-gray-500 hover:text-gray-700 underline\"\n            >\n              Effacer tous les filtres\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC;IACnF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;kCAMnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCACC,OAAM;gCACN,WAAU;gCACV,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;kDAE/C,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAW;;;;;;;;;;;;;;;;;;kCAK7B,6LAAC;kCACC,cAAA,6LAAC;4BACC,OAAM;4BACN,WAAU;4BACV,OAAO;4BACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;8CAE/C,6LAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,6LAAC;oCAAO,OAAM;8CAAQ;;;;;;8CACtB,6LAAC;oCAAO,OAAM;8CAAQ;;;;;;8CACtB,6LAAC;oCAAO,OAAM;8CAAc;;;;;;8CAC5B,6LAAC;oCAAO,OAAM;8CAAU;;;;;;;;;;;;;;;;;kCAK5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,gBAAgB,iBAAiB,cAAc,QAAQ;gCACtE,WAAW,CAAC,mFAAmF,EAC7F,iBAAiB,cACb,2DACA,+CACJ;;kDAEF,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG/B,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,gBAAgB,iBAAiB,aAAa,QAAQ;gCACrE,WAAW,CAAC,mFAAmF,EAC7F,iBAAiB,aACb,wDACA,+CACJ;;kDAEF,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;YAOzC,CAAC,cAAc,iBAAiB,SAAS,YAAY,mBACpD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAwB;;;;;;wBACvC,4BACC,6LAAC;4BAAK,WAAU;;gCAA0F;gCAC3F;gCAAW;8CACxB,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CACX;;;;;;;;;;;;wBAKJ,iBAAiB,uBAChB,6LAAC;4BAAK,WAAU;;gCAA8F;gCACrG,iBAAiB,cAAc,eAAe;8CACrD,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;;;;;;;wBAKJ,8BACC,6LAAC;4BAAK,WAAU;;gCAA4F;gCAClG;8CACR,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;;;;;;;sCAKL,6LAAC;4BACC,MAAK;4BACL,SAAS;gCACP,cAAc;gCACd,gBAAgB;gCAChB,gBAAgB;4BAClB;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA3IgB;KAAA", "debugId": null}}, {"offset": {"line": 1010, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/customers/NewCustomerForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { User, MapPin, Phone, Mail, Building, Truck, ShoppingCart } from 'lucide-react'\nimport { apiCall } from '@/hooks/useApi'\n\ninterface CustomerFormData {\n  name: string\n  email: string\n  phone: string\n  address: string\n  city: string\n  type: 'LOGISTICS' | 'COMMERCE'\n  contactPerson: string\n  businessType: string\n  notes: string\n}\n\nconst senegalCities = [\n  'Dakar', 'Thiès', 'Saint-Louis', 'Kaolack', 'Ziguinchor', \n  'Diourbel', 'Tambacounda', 'Kolda', 'Fatick', 'Louga'\n]\n\nconst businessTypes = [\n  'Boutique', 'Grossiste', 'Détaillant', 'Salon de beauté', 'Marché',\n  'Entreprise de transport', 'Société de logistique', 'Transitaire'\n]\n\ninterface NewCustomerFormProps {\n  onSuccess: () => void\n  onCancel: () => void\n}\n\nexport function NewCustomerForm({ onSuccess, onCancel }: NewCustomerFormProps) {\n  const [isLoading, setIsLoading] = useState(false)\n  const [formData, setFormData] = useState<CustomerFormData>({\n    name: '',\n    email: '',\n    phone: '',\n    address: '',\n    city: 'Dakar',\n    type: 'COMMERCE',\n    contactPerson: '',\n    businessType: '',\n    notes: ''\n  })\n\n  const handleInputChange = (field: keyof CustomerFormData, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!formData.name || !formData.phone || !formData.type) {\n      alert('Veuillez remplir tous les champs obligatoires')\n      return\n    }\n\n    setIsLoading(true)\n    try {\n      // Adapter les données au format API\n      const apiData = {\n        name: formData.name,\n        phone: formData.phone,\n        email: formData.email || undefined,\n        address: formData.address || undefined,\n        city: formData.city,\n        type: formData.type,\n        contactPerson: formData.contactPerson || undefined,\n        businessType: formData.businessType || undefined,\n        notes: formData.notes || undefined\n      }\n\n      await apiCall('/api/customers', {\n        method: 'POST',\n        body: apiData\n      })\n\n      onSuccess()\n    } catch (error) {\n      console.error('Erreur:', error)\n      alert(`Erreur: ${error instanceof Error ? error.message : 'Erreur lors de la création du client'}`)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Informations générales */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Building className=\"h-5 w-5\" />\n              Informations générales\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Nom du client *\n              </label>\n              <input\n                type=\"text\"\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                value={formData.name}\n                onChange={(e) => handleInputChange('name', e.target.value)}\n                placeholder=\"Ex: Boutique Elegance\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Type de client *\n              </label>\n              <div className=\"grid grid-cols-2 gap-3\">\n                <label className=\"flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50\">\n                  <input\n                    type=\"radio\"\n                    name=\"type\"\n                    value=\"COMMERCE\"\n                    checked={formData.type === 'COMMERCE'}\n                    onChange={(e) => handleInputChange('type', e.target.value as 'LOGISTICS' | 'COMMERCE')}\n                    className=\"mr-3\"\n                  />\n                  <ShoppingCart className=\"h-5 w-5 text-green-600 mr-2\" />\n                  <div>\n                    <div className=\"font-medium text-gray-900\">Commerce</div>\n                    <div className=\"text-xs text-gray-500\">Achat + Transport</div>\n                  </div>\n                </label>\n                <label className=\"flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50\">\n                  <input\n                    type=\"radio\"\n                    name=\"type\"\n                    value=\"LOGISTICS\"\n                    checked={formData.type === 'LOGISTICS'}\n                    onChange={(e) => handleInputChange('type', e.target.value as 'LOGISTICS' | 'COMMERCE')}\n                    className=\"mr-3\"\n                  />\n                  <Truck className=\"h-5 w-5 text-orange-600 mr-2\" />\n                  <div>\n                    <div className=\"font-medium text-gray-900\">Logistique</div>\n                    <div className=\"text-xs text-gray-500\">Transport uniquement</div>\n                  </div>\n                </label>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Type d'entreprise\n              </label>\n              <select\n                title=\"Type d'entreprise\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                value={formData.businessType}\n                onChange={(e) => handleInputChange('businessType', e.target.value)}\n              >\n                <option value=\"\">Sélectionner un type</option>\n                {businessTypes.map(type => (\n                  <option key={type} value={type}>{type}</option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Ville\n              </label>\n              <select\n                title=\"Ville\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                value={formData.city}\n                onChange={(e) => handleInputChange('city', e.target.value)}\n              >\n                {senegalCities.map(city => (\n                  <option key={city} value={city}>{city}</option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Adresse complète\n              </label>\n              <textarea\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                rows={3}\n                value={formData.address}\n                onChange={(e) => handleInputChange('address', e.target.value)}\n                placeholder=\"Adresse complète du client\"\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Contact */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <User className=\"h-5 w-5\" />\n              Informations de contact\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Personne de contact\n              </label>\n              <input\n                type=\"text\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                value={formData.contactPerson}\n                onChange={(e) => handleInputChange('contactPerson', e.target.value)}\n                placeholder=\"Nom du contact principal\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Email\n              </label>\n              <input\n                type=\"email\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                value={formData.email}\n                onChange={(e) => handleInputChange('email', e.target.value)}\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Téléphone *\n              </label>\n              <input\n                type=\"tel\"\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                value={formData.phone}\n                onChange={(e) => handleInputChange('phone', e.target.value)}\n                placeholder=\"+221 xx xxx xx xx\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Notes additionnelles\n              </label>\n              <textarea\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                rows={4}\n                value={formData.notes}\n                onChange={(e) => handleInputChange('notes', e.target.value)}\n                placeholder=\"Informations supplémentaires sur le client...\"\n              />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Boutons d'action */}\n      <div className=\"flex justify-end gap-4\">\n        <Button\n          type=\"button\"\n          variant=\"outline\"\n          onClick={onCancel}\n        >\n          Annuler\n        </Button>\n        <Button\n          type=\"submit\"\n          disabled={isLoading}\n        >\n          {isLoading ? 'Création...' : 'Créer le client'}\n        </Button>\n      </div>\n    </form>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAoBA,MAAM,gBAAgB;IACpB;IAAS;IAAS;IAAe;IAAW;IAC5C;IAAY;IAAe;IAAS;IAAU;CAC/C;AAED,MAAM,gBAAgB;IACpB;IAAY;IAAa;IAAc;IAAmB;IAC1D;IAA2B;IAAyB;CACrD;AAOM,SAAS,gBAAgB,EAAE,SAAS,EAAE,QAAQ,EAAwB;;IAC3E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,MAAM;QACN,MAAM;QACN,eAAe;QACf,cAAc;QACd,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC,OAA+B;QACxD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IAClD;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,IAAI,EAAE;YACvD,MAAM;YACN;QACF;QAEA,aAAa;QACb,IAAI;YACF,oCAAoC;YACpC,MAAM,UAAU;gBACd,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS,KAAK;gBACrB,OAAO,SAAS,KAAK,IAAI;gBACzB,SAAS,SAAS,OAAO,IAAI;gBAC7B,MAAM,SAAS,IAAI;gBACnB,MAAM,SAAS,IAAI;gBACnB,eAAe,SAAS,aAAa,IAAI;gBACzC,cAAc,SAAS,YAAY,IAAI;gBACvC,OAAO,SAAS,KAAK,IAAI;YAC3B;YAEA,MAAM,CAAA,GAAA,yHAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;gBAC9B,QAAQ;gBACR,MAAM;YACR;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,wCAAwC;QACpG,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIpC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,QAAQ;gDACR,WAAU;gDACV,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gDACzD,aAAY;;;;;;;;;;;;kDAIhB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAM;gEACN,SAAS,SAAS,IAAI,KAAK;gEAC3B,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gEACzD,WAAU;;;;;;0EAEZ,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;0EACxB,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAA4B;;;;;;kFAC3C,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAG3C,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAM;gEACN,SAAS,SAAS,IAAI,KAAK;gEAC3B,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gEACzD,WAAU;;;;;;0EAEZ,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAA4B;;;;;;kFAC3C,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAM/C,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,OAAM;gDACN,WAAU;gDACV,OAAO,SAAS,YAAY;gDAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;;kEAEjE,6LAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,cAAc,GAAG,CAAC,CAAA,qBACjB,6LAAC;4DAAkB,OAAO;sEAAO;2DAApB;;;;;;;;;;;;;;;;;kDAKnB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,OAAM;gDACN,WAAU;gDACV,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;0DAExD,cAAc,GAAG,CAAC,CAAA,qBACjB,6LAAC;wDAAkB,OAAO;kEAAO;uDAApB;;;;;;;;;;;;;;;;kDAKnB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,WAAU;gDACV,MAAM;gDACN,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;gDAC5D,aAAY;;;;;;;;;;;;;;;;;;;;;;;;kCAOpB,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIhC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,OAAO,SAAS,aAAa;gDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAClE,aAAY;;;;;;;;;;;;kDAIhB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC1D,aAAY;;;;;;;;;;;;kDAIhB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,QAAQ;gDACR,WAAU;gDACV,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC1D,aAAY;;;;;;;;;;;;kDAIhB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,WAAU;gDACV,MAAM;gDACN,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC1D,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS;kCACV;;;;;;kCAGD,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,UAAU;kCAET,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;AAKvC;GA1PgB;KAAA", "debugId": null}}, {"offset": {"line": 1719, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/customers/AddCustomerButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Plus } from 'lucide-react'\nimport { NewCustomerForm } from './NewCustomerForm'\n\nexport function AddCustomerButton() {\n  const [showForm, setShowForm] = useState(false)\n\n  return (\n    <>\n      <button\n        type=\"button\"\n        onClick={() => setShowForm(true)}\n        className=\"inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium\"\n      >\n        <Plus className=\"h-4 w-4\" />\n        Nouveau client\n      </button>\n\n      {showForm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h2 className=\"text-xl font-bold text-gray-900\">Nouveau client</h2>\n                <button\n                  type=\"button\"\n                  onClick={() => setShowForm(false)}\n                  className=\"text-gray-400 hover:text-gray-600\"\n                >\n                  <span className=\"sr-only\">Fermer</span>\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              <NewCustomerForm onSuccess={() => setShowForm(false)} onCancel={() => setShowForm(false)} />\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,qBACE;;0BACE,6LAAC;gBACC,MAAK;gBACL,SAAS,IAAM,YAAY;gBAC3B,WAAU;;kCAEV,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAY;;;;;;;YAI7B,0BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;0CAI3E,6LAAC,qJAAA,CAAA,kBAAe;gCAAC,WAAW,IAAM,YAAY;gCAAQ,UAAU,IAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;;;AAOhG;GAtCgB;KAAA", "debugId": null}}]}