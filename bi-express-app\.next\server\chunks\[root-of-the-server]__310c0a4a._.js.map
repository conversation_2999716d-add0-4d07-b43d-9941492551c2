{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/app/api/shipments/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const status = searchParams.get('status')\n    const carrierId = searchParams.get('carrierId')\n    const transportMode = searchParams.get('transportMode')\n\n    const where: any = {}\n\n    if (status) {\n      where.status = status\n    }\n\n    if (carrierId) {\n      where.carrierId = carrierId\n    }\n\n    if (transportMode) {\n      where.transportMode = transportMode\n    }\n\n    const shipments = await prisma.shipment.findMany({\n      where,\n      include: {\n        carrier: {\n          select: {\n            id: true,\n            name: true,\n            phone: true,\n            rating: true\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'desc'\n      }\n    })\n\n    return NextResponse.json(shipments)\n  } catch (error) {\n    console.error('Erreur lors de la récupération des expéditions:', error)\n    return NextResponse.json(\n      { error: 'Erreur lors de la récupération des expéditions' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const {\n      carrierId,\n      transportMode,\n      orderIds,\n      pickupDate,\n      estimatedDelivery,\n      destinationCity = 'DAKAR',\n      originCity = 'LAGOS', // Valeur par défaut\n      notes,\n      priority\n    } = body\n\n    // Validation des champs obligatoires\n    if (!carrierId || !transportMode || !orderIds || orderIds.length === 0 || !pickupDate) {\n      return NextResponse.json(\n        { error: 'Transporteur, mode de transport, commandes et date d\\'enlèvement sont obligatoires' },\n        { status: 400 }\n      )\n    }\n\n    // Vérifier que le transporteur existe\n    const carrier = await prisma.carrier.findUnique({\n      where: { id: carrierId }\n    })\n\n    if (!carrier) {\n      return NextResponse.json(\n        { error: 'Transporteur non trouvé' },\n        { status: 404 }\n      )\n    }\n\n    // Vérifier que le transporteur supporte ce mode de transport\n    if (!carrier.transportModes.includes(transportMode)) {\n      return NextResponse.json(\n        { error: 'Ce transporteur ne supporte pas ce mode de transport' },\n        { status: 400 }\n      )\n    }\n\n    // Vérifier que toutes les commandes existent et sont confirmées\n    const orders = await prisma.order.findMany({\n      where: {\n        id: { in: orderIds },\n        status: { in: ['PENDING', 'CONFIRMED'] }\n      },\n      include: {\n        orderItems: {\n          include: {\n            product: true\n          }\n        }\n      }\n    })\n\n    if (orders.length !== orderIds.length) {\n      return NextResponse.json(\n        { error: 'Certaines commandes sont introuvables ou non confirmées' },\n        { status: 400 }\n      )\n    }\n\n    // Calculer le poids total et le coût de transport\n    let totalWeight = 0\n    let totalValue = 0\n\n    orders.forEach(order => {\n      order.orderItems.forEach(item => {\n        totalWeight += (item.product.weight || 25) * item.quantity // 25kg par défaut\n        totalValue += item.totalPrice\n      })\n    })\n\n    const transportCostPerKg = transportMode === 'AIR' ? 150 : 75\n    const totalTransportCost = totalWeight * transportCostPerKg\n\n    // Générer un numéro de suivi unique\n    const shipmentCount = await prisma.shipment.count()\n    const trackingNumber = `${transportMode === 'AIR' ? 'AIR' : 'TRK'}-${new Date().getFullYear()}-${String(shipmentCount + 1).padStart(6, '0')}`\n\n    // Créer l'expédition\n    const shipment = await prisma.shipment.create({\n      data: {\n        trackingNumber,\n        carrierId,\n        transportMode,\n        status: 'PENDING',\n        originCity,\n        pickupDate: new Date(pickupDate),\n        estimatedDelivery: estimatedDelivery ? new Date(estimatedDelivery) : null,\n        destinationCity,\n        currentWeight: totalWeight,\n        maxWeight: totalWeight * 1.5, // Capacité maximale = 150% du poids actuel\n        totalTransportCost,\n        notes: notes || '',\n        orders: {\n          connect: orderIds.map((id: string) => ({ id }))\n        }\n      },\n      include: {\n        carrier: {\n          select: {\n            id: true,\n            name: true,\n            phone: true,\n            rating: true\n          }\n        },\n        orders: {\n          include: {\n            customer: {\n              select: {\n                id: true,\n                name: true,\n                city: true,\n                type: true\n              }\n            }\n          }\n        }\n      }\n    })\n\n    // Mettre à jour le statut des commandes\n    await prisma.order.updateMany({\n      where: {\n        id: { in: orderIds }\n      },\n      data: {\n        status: 'SHIPPED'\n      }\n    })\n\n    // Créer l'événement de suivi initial\n    await prisma.trackingEvent.create({\n      data: {\n        shipmentId: shipment.id,\n        eventType: 'PICKUP_SCHEDULED',\n        description: 'Expédition créée et en attente d\\'enlèvement',\n        location: originCity\n      }\n    })\n\n    return NextResponse.json(shipment, { status: 201 })\n  } catch (error) {\n    console.error('Erreur lors de la création de l\\'expédition:', error)\n    return NextResponse.json(\n      { error: 'Erreur lors de la création de l\\'expédition' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,gBAAgB,aAAa,GAAG,CAAC;QAEvC,MAAM,QAAa,CAAC;QAEpB,IAAI,QAAQ;YACV,MAAM,MAAM,GAAG;QACjB;QAEA,IAAI,WAAW;YACb,MAAM,SAAS,GAAG;QACpB;QAEA,IAAI,eAAe;YACjB,MAAM,aAAa,GAAG;QACxB;QAEA,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC/C;YACA,SAAS;gBACP,SAAS;oBACP,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,QAAQ;oBACV;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mDAAmD;QACjE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiD,GAC1D;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,SAAS,EACT,aAAa,EACb,QAAQ,EACR,UAAU,EACV,iBAAiB,EACjB,kBAAkB,OAAO,EACzB,aAAa,OAAO,EACpB,KAAK,EACL,QAAQ,EACT,GAAG;QAEJ,qCAAqC;QACrC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,SAAS,MAAM,KAAK,KAAK,CAAC,YAAY;YACrF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqF,GAC9F;gBAAE,QAAQ;YAAI;QAElB;QAEA,sCAAsC;QACtC,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBAAE,IAAI;YAAU;QACzB;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,6DAA6D;QAC7D,IAAI,CAAC,QAAQ,cAAc,CAAC,QAAQ,CAAC,gBAAgB;YACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuD,GAChE;gBAAE,QAAQ;YAAI;QAElB;QAEA,gEAAgE;QAChE,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YACzC,OAAO;gBACL,IAAI;oBAAE,IAAI;gBAAS;gBACnB,QAAQ;oBAAE,IAAI;wBAAC;wBAAW;qBAAY;gBAAC;YACzC;YACA,SAAS;gBACP,YAAY;oBACV,SAAS;wBACP,SAAS;oBACX;gBACF;YACF;QACF;QAEA,IAAI,OAAO,MAAM,KAAK,SAAS,MAAM,EAAE;YACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0D,GACnE;gBAAE,QAAQ;YAAI;QAElB;QAEA,kDAAkD;QAClD,IAAI,cAAc;QAClB,IAAI,aAAa;QAEjB,OAAO,OAAO,CAAC,CAAA;YACb,MAAM,UAAU,CAAC,OAAO,CAAC,CAAA;gBACvB,eAAe,CAAC,KAAK,OAAO,CAAC,MAAM,IAAI,EAAE,IAAI,KAAK,QAAQ,CAAC,kBAAkB;;gBAC7E,cAAc,KAAK,UAAU;YAC/B;QACF;QAEA,MAAM,qBAAqB,kBAAkB,QAAQ,MAAM;QAC3D,MAAM,qBAAqB,cAAc;QAEzC,oCAAoC;QACpC,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK;QACjD,MAAM,iBAAiB,GAAG,kBAAkB,QAAQ,QAAQ,MAAM,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,CAAC,EAAE,OAAO,gBAAgB,GAAG,QAAQ,CAAC,GAAG,MAAM;QAE7I,qBAAqB;QACrB,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ;gBACA;gBACA;gBACA,QAAQ;gBACR;gBACA,YAAY,IAAI,KAAK;gBACrB,mBAAmB,oBAAoB,IAAI,KAAK,qBAAqB;gBACrE;gBACA,eAAe;gBACf,WAAW,cAAc;gBACzB;gBACA,OAAO,SAAS;gBAChB,QAAQ;oBACN,SAAS,SAAS,GAAG,CAAC,CAAC,KAAe,CAAC;4BAAE;wBAAG,CAAC;gBAC/C;YACF;YACA,SAAS;gBACP,SAAS;oBACP,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,SAAS;wBACP,UAAU;4BACR,QAAQ;gCACN,IAAI;gCACJ,MAAM;gCACN,MAAM;gCACN,MAAM;4BACR;wBACF;oBACF;gBACF;YACF;QACF;QAEA,wCAAwC;QACxC,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,UAAU,CAAC;YAC5B,OAAO;gBACL,IAAI;oBAAE,IAAI;gBAAS;YACrB;YACA,MAAM;gBACJ,QAAQ;YACV;QACF;QAEA,qCAAqC;QACrC,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ,YAAY,SAAS,EAAE;gBACvB,WAAW;gBACX,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA8C,GACvD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}