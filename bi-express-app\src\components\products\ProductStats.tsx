import { prisma } from '@/lib/prisma'
import { formatCurrency } from '@/lib/utils'
import { Package, AlertTriangle, TrendingUp, DollarSign } from 'lucide-react'

async function getProductStats() {
  const [
    totalProducts,
    lowStockProducts,
    products
  ] = await Promise.all([
    prisma.product.count({ where: { isActive: true } }),
    prisma.product.count({ 
      where: { 
        isActive: true,
        stockQuantity: { lte: prisma.product.fields.minStockAlert }
      }
    }),
    prisma.product.findMany({
      where: { isActive: true },
      select: {
        supplierPrice: true,
        margin: true,
        logisticRate: true,
        stockQuantity: true
      }
    })
  ])

  // Calculs des statistiques
  const totalValue = products.reduce((sum, product) => {
    const costPrice = product.supplierPrice * (1 + product.logisticRate)
    return sum + (costPrice * product.stockQuantity)
  }, 0)

  const averageMargin = products.length > 0 
    ? products.reduce((sum, product) => sum + product.margin, 0) / products.length * 100
    : 0

  const totalStock = products.reduce((sum, product) => sum + product.stockQuantity, 0)

  return {
    totalProducts,
    lowStockProducts,
    totalValue,
    averageMargin,
    totalStock
  }
}

export async function ProductStats() {
  const stats = await getProductStats()

  const statCards = [
    {
      title: 'Total Produits',
      value: stats.totalProducts.toString(),
      icon: Package,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      description: 'Produits actifs'
    },
    {
      title: 'Stock Total',
      value: stats.totalStock.toString(),
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      description: 'Unités en stock'
    },
    {
      title: 'Valeur Stock',
      value: formatCurrency(stats.totalValue),
      icon: DollarSign,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      description: 'Valeur totale'
    },
    {
      title: 'Stock Faible',
      value: stats.lowStockProducts.toString(),
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      description: 'Produits à réapprovisionner'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statCards.map((stat, index) => (
        <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">{stat.title}</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
              <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
            </div>
            <div className={`p-3 rounded-lg ${stat.bgColor}`}>
              <stat.icon className={`h-6 w-6 ${stat.color}`} />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
