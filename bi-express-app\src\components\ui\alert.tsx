import * as React from "react"
import { CheckCircle, AlertCircle, XCircle, Info, X } from "lucide-react"
import { cn } from "@/lib/utils"

interface AlertProps {
  variant?: 'success' | 'error' | 'warning' | 'info'
  title?: string
  children: React.ReactNode
  dismissible?: boolean
  onDismiss?: () => void
  className?: string
}

const variantConfig = {
  success: {
    icon: CheckCircle,
    classes: "bg-green-50 border-green-200 text-green-800",
    iconClasses: "text-green-600"
  },
  error: {
    icon: XCircle,
    classes: "bg-red-50 border-red-200 text-red-800",
    iconClasses: "text-red-600"
  },
  warning: {
    icon: AlertCircle,
    classes: "bg-orange-50 border-orange-200 text-orange-800",
    iconClasses: "text-orange-600"
  },
  info: {
    icon: Info,
    classes: "bg-blue-50 border-blue-200 text-blue-800",
    iconClasses: "text-blue-600"
  }
}

export function Alert({ 
  variant = 'info', 
  title, 
  children, 
  dismissible = false,
  onDismiss,
  className 
}: AlertProps) {
  const config = variantConfig[variant]
  const Icon = config.icon

  return (
    <div className={cn(
      "relative rounded-lg border p-4",
      config.classes,
      className
    )}>
      <div className="flex items-start gap-3">
        <Icon className={cn("h-5 w-5 flex-shrink-0 mt-0.5", config.iconClasses)} />
        
        <div className="flex-1 min-w-0">
          {title && (
            <h3 className="text-sm font-medium mb-1">
              {title}
            </h3>
          )}
          <div className="text-sm">
            {children}
          </div>
        </div>

        {dismissible && onDismiss && (
          <button
            type="button"
            onClick={onDismiss}
            className={cn(
              "flex-shrink-0 rounded-md p-1.5 hover:bg-black/5 focus:outline-none focus:ring-2 focus:ring-offset-2",
              variant === 'success' && "focus:ring-green-600",
              variant === 'error' && "focus:ring-red-600",
              variant === 'warning' && "focus:ring-orange-600",
              variant === 'info' && "focus:ring-blue-600"
            )}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Fermer</span>
          </button>
        )}
      </div>
    </div>
  )
}

// Convenience components
export const SuccessAlert = (props: Omit<AlertProps, 'variant'>) => 
  <Alert variant="success" {...props} />

export const ErrorAlert = (props: Omit<AlertProps, 'variant'>) => 
  <Alert variant="error" {...props} />

export const WarningAlert = (props: Omit<AlertProps, 'variant'>) => 
  <Alert variant="warning" {...props} />

export const InfoAlert = (props: Omit<AlertProps, 'variant'>) => 
  <Alert variant="info" {...props} />
