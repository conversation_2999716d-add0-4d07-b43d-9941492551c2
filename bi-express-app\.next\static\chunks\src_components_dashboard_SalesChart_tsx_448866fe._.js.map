{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Bi_Express/bi-express-app/src/components/dashboard/SalesChart.tsx"], "sourcesContent": ["'use client'\n\nimport { \n  LineChart, \n  Line, \n  XAxis, \n  YAxis, \n  CartesianGrid, \n  Tooltip, \n  ResponsiveContainer \n} from 'recharts'\n\n// Données d'exemple pour le graphique\nconst salesData = [\n  { month: 'Jan', revenue: 2400000, profit: 480000 },\n  { month: 'Fév', revenue: 1398000, profit: 279600 },\n  { month: 'Mar', revenue: 9800000, profit: 1960000 },\n  { month: 'Avr', revenue: 3908000, profit: 781600 },\n  { month: 'Mai', revenue: 4800000, profit: 960000 },\n  { month: 'Jun', revenue: 3800000, profit: 760000 },\n  { month: 'Jul', revenue: 4300000, profit: 860000 },\n]\n\nexport function SalesChart() {\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n      <div className=\"mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Évolution des ventes</h3>\n        <p className=\"text-sm text-gray-600 mt-1\">\n          Chi<PERSON><PERSON> d'affaires et bénéfices des 7 derniers mois\n        </p>\n      </div>\n      \n      <div className=\"h-80\">\n        <ResponsiveContainer width=\"100%\" height=\"100%\">\n          <LineChart data={salesData}>\n            <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f0f0f0\" />\n            <XAxis \n              dataKey=\"month\" \n              stroke=\"#6b7280\"\n              fontSize={12}\n            />\n            <YAxis \n              stroke=\"#6b7280\"\n              fontSize={12}\n              tickFormatter={(value) => `${(value / 1000000).toFixed(1)}M`}\n            />\n            <Tooltip \n              formatter={(value: number, name: string) => [\n                `${(value / 1000000).toFixed(2)}M CFA`,\n                name === 'revenue' ? 'Chiffre d\\'affaires' : 'Bénéfices'\n              ]}\n              labelStyle={{ color: '#374151' }}\n              contentStyle={{ \n                backgroundColor: 'white', \n                border: '1px solid #e5e7eb',\n                borderRadius: '8px'\n              }}\n            />\n            <Line \n              type=\"monotone\" \n              dataKey=\"revenue\" \n              stroke=\"#3b82f6\" \n              strokeWidth={3}\n              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}\n              activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}\n            />\n            <Line \n              type=\"monotone\" \n              dataKey=\"profit\" \n              stroke=\"#10b981\" \n              strokeWidth={3}\n              dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}\n              activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2 }}\n            />\n          </LineChart>\n        </ResponsiveContainer>\n      </div>\n      \n      <div className=\"flex items-center justify-center space-x-6 mt-4\">\n        <div className=\"flex items-center\">\n          <div className=\"w-3 h-3 bg-blue-500 rounded-full mr-2\"></div>\n          <span className=\"text-sm text-gray-600\">Chiffre d'affaires</span>\n        </div>\n        <div className=\"flex items-center\">\n          <div className=\"w-3 h-3 bg-green-500 rounded-full mr-2\"></div>\n          <span className=\"text-sm text-gray-600\">Bénéfices</span>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AAYA,sCAAsC;AACtC,MAAM,YAAY;IAChB;QAAE,OAAO;QAAO,SAAS;QAAS,QAAQ;IAAO;IACjD;QAAE,OAAO;QAAO,SAAS;QAAS,QAAQ;IAAO;IACjD;QAAE,OAAO;QAAO,SAAS;QAAS,QAAQ;IAAQ;IAClD;QAAE,OAAO;QAAO,SAAS;QAAS,QAAQ;IAAO;IACjD;QAAE,OAAO;QAAO,SAAS;QAAS,QAAQ;IAAO;IACjD;QAAE,OAAO;QAAO,SAAS;QAAS,QAAQ;IAAO;IACjD;QAAE,OAAO;QAAO,SAAS;QAAS,QAAQ;IAAO;CAClD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAK5C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACvC,cAAA,6LAAC,wJAAA,CAAA,YAAS;wBAAC,MAAM;;0CACf,6LAAC,gKAAA,CAAA,gBAAa;gCAAC,iBAAgB;gCAAM,QAAO;;;;;;0CAC5C,6LAAC,wJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,QAAO;gCACP,UAAU;;;;;;0CAEZ,6LAAC,wJAAA,CAAA,QAAK;gCACJ,QAAO;gCACP,UAAU;gCACV,eAAe,CAAC,QAAU,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;0CAE9D,6LAAC,0JAAA,CAAA,UAAO;gCACN,WAAW,CAAC,OAAe,OAAiB;wCAC1C,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,KAAK,CAAC;wCACtC,SAAS,YAAY,wBAAwB;qCAC9C;gCACD,YAAY;oCAAE,OAAO;gCAAU;gCAC/B,cAAc;oCACZ,iBAAiB;oCACjB,QAAQ;oCACR,cAAc;gCAChB;;;;;;0CAEF,6LAAC,uJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;oCAAE,MAAM;oCAAW,aAAa;oCAAG,GAAG;gCAAE;gCAC7C,WAAW;oCAAE,GAAG;oCAAG,QAAQ;oCAAW,aAAa;gCAAE;;;;;;0CAEvD,6LAAC,uJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;oCAAE,MAAM;oCAAW,aAAa;oCAAG,GAAG;gCAAE;gCAC7C,WAAW;oCAAE,GAAG;oCAAG,QAAQ;oCAAW,aAAa;gCAAE;;;;;;;;;;;;;;;;;;;;;;0BAM7D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;kCAE1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;AAKlD;KApEgB", "debugId": null}}]}