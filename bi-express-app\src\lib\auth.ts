// Système d'authentification simple pour l'application
export interface User {
  id: string
  email: string
  name: string
  role: 'admin' | 'manager' | 'user'
  createdAt: Date
}

export interface AuthSession {
  user: User
  token: string
  expiresAt: Date
}

// Utilisateurs par défaut pour la démo
const DEFAULT_USERS: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: 'Administrateur',
    role: 'admin',
    createdAt: new Date('2024-01-01')
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: 'Gestionnaire',
    role: 'manager',
    createdAt: new Date('2024-01-01')
  },
  {
    id: '3',
    email: '<EMAIL>',
    name: 'Utilisateur',
    role: 'user',
    createdAt: new Date('2024-01-01')
  }
]

// Mots de passe par défaut (en production, utiliser un système de hachage)
const DEFAULT_PASSWORDS: Record<string, string> = {
  '<EMAIL>': 'admin123',
  '<EMAIL>': 'manager123',
  '<EMAIL>': 'user123'
}

// Stockage en mémoire des sessions (en production, utiliser Redis ou une base de données)
let activeSessions: Map<string, AuthSession> = new Map()

/**
 * Génère un token de session aléatoire
 */
function generateSessionToken(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

/**
 * Authentifie un utilisateur avec email et mot de passe
 */
export async function authenticateUser(
  email: string, 
  password: string
): Promise<{ success: boolean; session?: AuthSession; error?: string }> {
  // Simulation d'un délai d'authentification
  await new Promise(resolve => setTimeout(resolve, 500))

  // Vérifier si l'utilisateur existe
  const user = DEFAULT_USERS.find(u => u.email === email)
  if (!user) {
    return { success: false, error: 'Utilisateur non trouvé' }
  }

  // Vérifier le mot de passe
  if (DEFAULT_PASSWORDS[email] !== password) {
    return { success: false, error: 'Mot de passe incorrect' }
  }

  // Créer une session
  const token = generateSessionToken()
  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 heures

  const session: AuthSession = {
    user,
    token,
    expiresAt
  }

  // Stocker la session
  activeSessions.set(token, session)

  return { success: true, session }
}

/**
 * Valide un token de session
 */
export async function validateSession(token: string): Promise<AuthSession | null> {
  const session = activeSessions.get(token)
  
  if (!session) {
    return null
  }

  // Vérifier si la session n'a pas expiré
  if (session.expiresAt < new Date()) {
    activeSessions.delete(token)
    return null
  }

  return session
}

/**
 * Déconnecte un utilisateur
 */
export async function logoutUser(token: string): Promise<void> {
  activeSessions.delete(token)
}

/**
 * Obtient tous les utilisateurs (pour l'administration)
 */
export async function getAllUsers(): Promise<User[]> {
  return DEFAULT_USERS
}

/**
 * Vérifie si un utilisateur a les permissions pour une action
 */
export function hasPermission(user: User, action: string): boolean {
  const permissions = {
    admin: ['read', 'write', 'delete', 'manage_users', 'view_reports', 'manage_settings'],
    manager: ['read', 'write', 'view_reports'],
    user: ['read']
  }

  return permissions[user.role]?.includes(action) || false
}

/**
 * Obtient les informations de l'utilisateur connecté depuis les cookies
 */
export async function getCurrentUser(): Promise<User | null> {
  // En production, récupérer le token depuis les cookies HTTP-only
  if (typeof window === 'undefined') {
    return null // Côté serveur
  }

  const token = localStorage.getItem('auth_token')
  if (!token) {
    return null
  }

  const session = await validateSession(token)
  return session?.user || null
}

/**
 * Sauvegarde le token d'authentification
 */
export function saveAuthToken(token: string): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('auth_token', token)
  }
}

/**
 * Supprime le token d'authentification
 */
export function removeAuthToken(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth_token')
  }
}

/**
 * Vérifie si l'utilisateur est connecté
 */
export async function isAuthenticated(): Promise<boolean> {
  const user = await getCurrentUser()
  return user !== null
}

/**
 * Middleware d'authentification pour les pages protégées
 */
export async function requireAuth(): Promise<{ authenticated: boolean; user?: User; redirectTo?: string }> {
  const user = await getCurrentUser()
  
  if (!user) {
    return {
      authenticated: false,
      redirectTo: '/login'
    }
  }

  return {
    authenticated: true,
    user
  }
}

/**
 * Obtient les statistiques des utilisateurs connectés
 */
export function getActiveSessionsCount(): number {
  // Nettoyer les sessions expirées
  const now = new Date()
  for (const [token, session] of activeSessions.entries()) {
    if (session.expiresAt < now) {
      activeSessions.delete(token)
    }
  }
  
  return activeSessions.size
}

/**
 * Obtient les rôles disponibles
 */
export function getAvailableRoles(): Array<{ value: string; label: string }> {
  return [
    { value: 'admin', label: 'Administrateur' },
    { value: 'manager', label: 'Gestionnaire' },
    { value: 'user', label: 'Utilisateur' }
  ]
}
