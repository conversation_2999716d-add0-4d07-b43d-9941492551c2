'use client'

import { useState } from 'react'
import { Search, X } from 'lucide-react'
import { getShipmentByTracking } from '@/lib/actions/shipments'
import { ShipmentStatusBadge } from './ShipmentStatusBadge'

interface TrackingButtonProps {
  trackingNumber: string
}

export function TrackingButton({ trackingNumber }: TrackingButtonProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [shipmentData, setShipmentData] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const handleTrack = async () => {
    if (!isOpen) {
      setIsOpen(true)
      setLoading(true)
      
      try {
        const data = await getShipmentByTracking(trackingNumber)
        setShipmentData(data)
      } catch (error) {
        console.error('Erreur lors du suivi:', error)
      } finally {
        setLoading(false)
      }
    } else {
      setIsOpen(false)
      setShipmentData(null)
    }
  }

  return (
    <div className="relative">
      <button
        onClick={handleTrack}
        className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        {isOpen ? (
          <>
            <X className="h-3 w-3 mr-1" />
            Fermer
          </>
        ) : (
          <>
            <Search className="h-3 w-3 mr-1" />
            Suivre
          </>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 top-8 w-96 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-10">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-gray-900">
              Suivi: {trackingNumber}
            </h4>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          </div>

          {loading ? (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-sm text-gray-500 mt-2">Chargement...</p>
            </div>
          ) : shipmentData ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Statut:</span>
                <ShipmentStatusBadge status={shipmentData.status} />
              </div>
              
              <div className="border-t pt-3">
                <h5 className="text-xs font-medium text-gray-900 mb-2">
                  Événements de suivi
                </h5>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {shipmentData.trackingEvents.map((event: any, index: number) => (
                    <div key={event.id} className="text-xs">
                      <div className="flex items-start space-x-2">
                        <div className={`w-2 h-2 rounded-full mt-1 ${
                          index === 0 ? 'bg-blue-500' : 'bg-gray-300'
                        }`} />
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">
                            {event.description}
                          </p>
                          <p className="text-gray-500">
                            {new Date(event.timestamp).toLocaleString('fr-FR')}
                          </p>
                          <p className="text-gray-500">{event.location}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-sm text-gray-500">
                Aucune information de suivi disponible
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
